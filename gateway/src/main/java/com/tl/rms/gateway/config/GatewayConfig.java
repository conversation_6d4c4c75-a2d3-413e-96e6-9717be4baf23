package com.tl.rms.gateway.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * <AUTHOR>
 * @date 2025/3/18
 */
@Data
@Configuration
@PropertySource({"classpath:configLayer/${spring.profiles.active}/GatewayConfig.properties"})
@EnableApolloConfig
public class GatewayConfig {

    @Value("${tlmall.gateway.executeTime}")
    private long executeTime;

}
