package com.tl.rms.gateway.filter;

import com.anywide.dawdler.clientplug.web.session.DawdlerSessionFilter;
import com.anywide.dawdler.clientplug.web.session.conf.JedisConfig;
import com.tl.rms.gateway.convertor.HttpServletRequestConvert;
import com.tl.rms.gateway.convertor.HttpServletResponseConvert;
import com.tl.rms.gateway.exception.GatewayException;
import com.tl.rms.gateway.utils.RequestUtil;
import com.tl.rms.lib.beans.web.HeaderConstants;
import com.tl.rms.lib.session.user.LoginUser;
import com.tl.rms.lib.useragent.DeviceType;
import com.tl.rms.util.IpUtil;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.CoreSubscriber;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

/**
 * 1. 所有来自网关的初始请求的session id都需要传递到后端请求。
 * 2. 所有来自网关的初始请求的上下文数据（ip, sessionId, requestURI, userId, clientId）都需要传递到后端请求，
 * 作为日志输出的扩展字段。
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
@Data
@Slf4j
@Component
@ConfigurationProperties("tlmall")
public class ContextFilter implements GlobalFilter, Ordered {

    private Map<String, Acl> loginAcl;

    /**
     * 全局禁止访问的URL，例如spring boot的/info
     */
    private List<String> disableGlobalUrl;

    private DawdlerSessionFilter filter;

    public ContextFilter(JedisConfig jedisConfig) throws ServletException {
        filter = new DawdlerSessionFilter(jedisConfig);
        FilterConfig fc = new FilterConfig() {

            @Override
            public ServletContext getServletContext() {
                return null;
            }

            @Override
            public Enumeration<String> getInitParameterNames() {
                return null;
            }

            @Override
            public String getInitParameter(String name) {
                return null;
            }

            @Override
            public String getFilterName() {
                return null;
            }

        };

        filter.init(fc);
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        exchange.getAttributes().put("log_begin_dt", (new Date()).getTime());
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        HttpServletRequestConvert requestConvert = new HttpServletRequestConvert(request);
        HttpServletResponseConvert responseConvert = new HttpServletResponseConvert(response);
        String requestURI = request.getURI().getPath();
        log.info("requestURI: {}", requestURI);
        // HttpSession session = request.getSession(false);
        // if (session != null) {
        // addSessionIdHeader(ctx,session.getId());
        // }
        checkURLNeedDisable(requestURI);// 被禁止访问的URL
        Map<String, Mono<Void>> result = new HashMap<>();
        Map<String, String> headerMap = new HashMap<>();
        FilterChain myChain = new FilterChain() {

            @Override
            public void doFilter(ServletRequest request, ServletResponse response) throws IOException, ServletException {
                HttpServletRequest httpRequest = (HttpServletRequest) request;
                HttpServletResponse httpResponse = (HttpServletResponse) response;
                HttpSession session = httpRequest.getSession(false);
                Long userId;
                if (session == null) {
                    userId = null;
                } else {
                    userId = (Long) session.getAttribute(LoginUser.ATTR_USER_ID);
                }
                log.info("userId: {}, requestURI: {}", userId, requestURI);
                if (userId == null && checkNeedLogin(httpRequest)) {
                    httpResponse.setStatus(401);
                    Mono<Void> mono = new Mono<Void>() {
                        @Override
                        public void subscribe(CoreSubscriber<? super Void> actual) {
                            actual.onComplete();
                        }
                    };
                    result.put("mono", mono);
                } else {
                    result.put("mono", chain.filter(RequestUtil.setHeaders(exchange, headerMap)));
                }
                addCtxHeader(headerMap, requestConvert, session);

            }
        };
        try {
            filter.doFilter(requestConvert, responseConvert, myChain);
        } catch (IOException | ServletException e) {
            log.error("doFilter error: {} ", e.getMessage(), e);
        }
        addUserAgent(headerMap, requestConvert);
        chain.filter(RequestUtil.setHeaders(exchange, headerMap));
        return result.get("mono");
    }

    // 把session id 传递到后端请求，用以保证后端的服务能获取到session (不生效)
    // private void addSessionIdHeader(RequestContext ctx, String sessionId) {
    // ctx.addZuulRequestHeader("Cookie", "_pttl_key=" + sessionId);
    // }
    private void checkURLNeedDisable(String url) {
        for (String str : disableGlobalUrl) {
            if (url.endsWith(str)) {
                log.warn("请求地址[{}],在禁止访问列表（disableGlobalUrl）中", str);
                GatewayException.illegalRequest();
            }
        }
    }

    private boolean checkNeedLogin(HttpServletRequest request) {

        if (loginAcl == null || loginAcl.isEmpty()) {
            return false;
        }

        log.info("loginAcl: {}", loginAcl);

        String apiUrl = request.getContextPath() + request.getServletPath();
        String service = apiUrl.substring(1, apiUrl.indexOf("/", 1));
        Acl acl = loginAcl.get(service);

        if (acl == null || (CollectionUtils.isEmpty(acl.getBlacklist()) && CollectionUtils.isEmpty(acl.getWhitelist()))) {
            return false;
        }

        String endPoint = apiUrl.replace("/" + service, "");
        log.info("endPoint: {}", endPoint);
        // 白名单控制逻辑: 白名单中的页面可以匿名访问，其他全部需要登录访问。
        if (!CollectionUtils.isEmpty(acl.getWhitelist())) {
            return !acl.getWhitelist().contains(endPoint);
        }

        // 黑名单控制逻辑： 黑名单中的页面需要登录访问，其他全部不需要登录访问。
        return acl.getBlacklist().contains(endPoint);

    }

    private void addUserAgent(Map<String, String> headerMap, HttpServletRequest request) {

        String userAgent = request.getHeader("User-Agent");
        headerMap.put("host", request.getHeader("Host"));
        headerMap.put(HeaderConstants.HEADER_CTX_MOBILE_FLAG, "0"); // 默认非移动端登录
        if (userAgent != null && userAgent.contains("mobileTlmall")) { // 同云适配约定，云适配移动端会重写ua请求头，如果包含指定字符则认为是移动端
            headerMap.put(HeaderConstants.HEADER_CTX_MOBILE_FLAG, "1");
            return;
        }

        // 增加非空校验，避免后续代码空指针异常
        if (StringUtils.isEmpty(userAgent)) {
            return;
        }

        DeviceType.UserAgent ua = DeviceType.UserAgentUtil.parseUserAgent(userAgent);
        headerMap.put("deviceType", ua.getDeviceType());
        headerMap.put("osType", ua.getOsType());
        headerMap.put("osVersion", ua.getOsVersion());
        headerMap.put("browserType", ua.getBrowserType());
        headerMap.put("browserVersion", ua.getBrowserVersion());
        if (StringUtils.equals("MAC", ua.getOsType())) {
            headerMap.put(HeaderConstants.HEADER_CTX_MAC_FLAG, "1");
        } else {
            headerMap.put(HeaderConstants.HEADER_CTX_MAC_FLAG, "0");
        }
    }

    /**
     * 这些数据将作为日志输出的扩展字段
     */
    private void addCtxHeader(Map<String, String> headerMap, HttpServletRequest request, HttpSession session) {

        headerMap.put(HeaderConstants.HEADER_CTX_IP, IpUtil.getIpAddress(request));
        headerMap.put(HeaderConstants.HEADER_CTX_REQUEST_URI, request.getRequestURI());
        if (session == null) {
            return;
        }
        headerMap.put(HeaderConstants.HEADER_CTX_SESSION_ID, session.getId());
        headerMap.put(HeaderConstants.HEADER_CTX_USER_ID, String.valueOf(session.getAttribute(LoginUser.ATTR_USER_ID)));
        String userName = (String) session.getAttribute(LoginUser.ATTR_USER_NAME);
        if (StringUtils.isNotEmpty(userName)) {
            try {
                headerMap.put(HeaderConstants.HEADER_CTX_USER_NAME, URLEncoder.encode(userName, "utf-8"));
            } catch (UnsupportedEncodingException e) {

            }
        }
    }

    @Override
    public int getOrder() {
        return 0;
    }

}
