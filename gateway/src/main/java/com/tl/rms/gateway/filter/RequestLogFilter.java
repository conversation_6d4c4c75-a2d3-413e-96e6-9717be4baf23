package com.tl.rms.gateway.filter;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.tl.rms.gateway.config.GatewayConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/3/18
 */
@Slf4j
@EnableApolloConfig
public class RequestLogFilter implements GlobalFilter, Ordered {

    @Autowired
    private GatewayConfig gatewayConfig;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {

        return chain.filter(exchange).then(Mono.fromRunnable(() -> {
            Object beginDT = exchange.getAttribute("log_begin_dt");
            if (beginDT != null) {
                ServerHttpRequest request = exchange.getRequest();
                ServerHttpResponse response = exchange.getResponse();
                Long endDT = System.currentTimeMillis();
                long time = endDT - (Long) beginDT;
                if (time > gatewayConfig.getExecuteTime()) {
                    log.info("URI:[{}] 响应码[{}] 耗时[{}]ms", request.getPath(), response.getStatusCode().value(), time);
                }
            }
        }));

    }

    @Override
    public int getOrder() {
        return 0;
    }

}
