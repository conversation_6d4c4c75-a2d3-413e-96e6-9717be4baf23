package com.tl.rms.gateway;

import jakarta.servlet.MultipartConfigElement;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.util.unit.DataSize;

/**
 * <AUTHOR>
 * @date 2025/3/18
 */
@SpringBootApplication
public class GatewayServerRunner {

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(GatewayServerRunner.class);
        app.run(args);
    }

    /**
     * 文件上传配置
     *
     * @return MultipartConfigElement
     */
    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        //单个文件最大
        factory.setMaxFileSize(DataSize.parse("20480KB"));
        /// 设置总上传数据总大小
        factory.setMaxRequestSize(DataSize.parse("20480KB"));

        return factory.createMultipartConfig();
    }
}
