server:
  port: 8310
spring:
  application:
    name: rms-gateway
  profiles:
    active: dev
  config:
    import: > 
      apollo://tech.application-public.yml,
      apollo://tech.redis-tlmall.yml,
      apollo://tech.session-redis.yml,
      apollo://application.yml


jasypt:
  encryptor:
    password: 123456789
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    algorithm: PBEWithMD5AndDES
