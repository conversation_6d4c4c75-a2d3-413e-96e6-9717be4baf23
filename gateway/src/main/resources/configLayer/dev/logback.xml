<configuration>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%date %-5level [%thread] %X{ip} %X{isMobile} %X{requestURI} %X{userId} %X{clientId} %logger - %m%n</pattern>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="STDOUT" />
    </root>
    <logger name="com.tl.rms" level="DEBUG">
    </logger>

</configuration>