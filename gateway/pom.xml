<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.tl</groupId>
        <artifactId>rms</artifactId>
        <version>1.0</version>
    </parent>

    <groupId>com.tl.rms.gateway</groupId>
    <artifactId>rms-gateway</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.tl.rms.api</groupId>
                <artifactId>rms-api</artifactId>
                <version>1.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-lib</artifactId>
                <version>1.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client-config-data</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-consul-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-discovery-consul</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-beans</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-session</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-useragent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-gateway-util</artifactId>
        </dependency>

        <!-- Springdoc OpenAPI -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webflux-ui</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <layout>ZIP</layout>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>