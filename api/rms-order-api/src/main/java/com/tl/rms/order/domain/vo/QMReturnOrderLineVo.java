package com.tl.rms.order.domain.vo;

import java.time.LocalDate;
import java.util.List;

import com.tl.rms.order.domain.LocalDateAdapter;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import lombok.Data;

/**
 * 奇门退货入库单行项目VO
 * <AUTHOR>
 */
@Data
@XmlAccessorType(jakarta.xml.bind.annotation.XmlAccessType.FIELD)
public class QMReturnOrderLineVo {
    
    /**
     * 单据行号，string（50）
     */
    private String orderLineNo;

    /**
     * 交易平台订单, string (50)
     */
    private String sourceOrderCode;

    /**
     * 交易平台子订单编码, string (50)
     */
    private String subSourceOrderCode;

    /**
     * 货主编码, string (50) , 必填
     */
    private String ownerCode;

    /**
     * 订单标记
     */
    private String orderFlag;

    /**
     * 退货原因, string (200)
     */
    private String returnReason;

    /**
     * 商品编码, string (50) , 必填
     */
    private String itemCode;

    /**
     * 仓储系统商品编码, string (50) , 条件必填
     */
    private String itemId;

    /**
     * 商品序列号列表
     */
    private List<String> snList;

    /**
     * 库存类型, string (50) , ZP=正品, CC=残次,JS=机损, XS= 箱损, 默认为 ZP
     */
    private String inventoryType;

    /**
     * 应收商品数量, int, 必填
     */
    private Integer planQty;

    /**
     * 批次编码, string (50)
     */
    private String batchCode;

    /**
     * 生产日期, string (10) , YYYY-MM-DD
     */
    @XmlJavaTypeAdapter(LocalDateAdapter.class)
    private LocalDate productDate;

    /**
     * 过期日期, string (10) , YYYY-MM-DD
     */
    @XmlJavaTypeAdapter(LocalDateAdapter.class)
    private LocalDate expireDate;

    /**
     * 生产批号, string (50)
     */
    private String produceCode;
}
