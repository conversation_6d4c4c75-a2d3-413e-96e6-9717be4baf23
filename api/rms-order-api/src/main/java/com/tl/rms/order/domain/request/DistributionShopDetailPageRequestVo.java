package com.tl.rms.order.domain.request;

import com.tl.rms.common.model.PageParamsVo;
import lombok.Data;

/**
 * 店铺销售任务详情查询参数实体
 *
 * <AUTHOR>
 * @date 2025/7/29
 */
@Data
public class DistributionShopDetailPageRequestVo extends PageParamsVo {

    // 店铺唯一主键，必传
    private Long shopId;

    // 分货批次号，必传
    private String distributionNo;

    // 产业
    private String industryId;

    // 品类
    private String categoryId;

    // 机型
    private String modelName;

    // 国标码
    private String gbCode;

    // 物料编码
    private String materialCode;

    // 物料名称
    private String materialName;

    // item码
    private String itemCode;

    // 项目
    private String projectId;

}
