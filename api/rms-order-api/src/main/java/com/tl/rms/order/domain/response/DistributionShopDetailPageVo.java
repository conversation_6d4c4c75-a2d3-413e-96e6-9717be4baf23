package com.tl.rms.order.domain.response;

import lombok.Data;

import java.util.Date;

@Data
public class DistributionShopDetailPageVo {

    /** 分货批次号 */
    private String distributionNo;

    /** 产业（如：手机、智慧屏） */
    private String industryName;

    /** 品类（如：电脑、生态软件） */
    private String categoryName;

    /** 国标码 */
    private String gbCode;

    /** 物料编码 */
    private String materialCode;

    /** item码 */
    private String itemCode;

    /** 机型（如：Mate 60、P60） */
    private String model;

    /** 颜色（如：黑色、白色） */
    private String color;

    /** 项目（如：春季促销、新品首发） */
    private String projectName;

    /** 仓位（仓库位置编码） */
    private Long warehouseId;

    /** 分货人（操作人姓名/工号） */
    private Long createById;

    /** 分货人（操作人姓名/工号） */
    private String createByName;

    /** 生效时间（分货规则生效的时间点） */
    private Date effectiveTime;

    /** 计划任务（分货目标数量） */
    private Integer plannedTask;

    /** 达成（已完成分货比例/数量） */
    private Integer achievement;

    /** 剩余库存（当前可用库存量） */
    private Integer remainingStock;

    /** 店铺编码:处理逻辑用不做返回 */
    private String shopCode;

}
