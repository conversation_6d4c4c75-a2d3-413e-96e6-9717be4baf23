package com.tl.rms.order.domain.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 吉客云 退换补货单
 *
 * <AUTHOR>
 * @date 2023/5/31
 */
@Data
public class JkyReturn implements Serializable {
    /**
     * 售后单系统Id
     */
    private Long tradeAfterId;

    /**
     * 售后单编号
     */
    private String returnChangeNo;

    /**
     * 售后单来源(1-手工登记,4-纠纷单,5-Excel导入,6-门店,7-网店售后单,8-错漏单)
     */
    private Integer tradeAfterFrom;

    /**
     * 售后单状态(1000:预入库待确认,1001-待审核,1002-待收货,1003-待结算,1004-已结算待收货,1005-已收货待结算,1007-已完成,1008-已取消,1009-已拒绝,1010-待支付,1014-待退货-待通知退货,1015-待退货-已通知退货,10081-已取消-被合并,10082-已取消-被拆分)
     */
    private String tradeAfterStatus;

    /**
     * 系统订单id
     */
    private Long tradeId;

    /**
     * 系统订单号
     */
    private String tradeNo;

    /**
     * 网店订单号
     */
    private String sourceTradeNo;

    /**
     * 网点售后单号
     */
    private String sourceTradeAfterNo;

    /**
     * 退货仓库id
     */
    private Long warehouseId;

    /**
     * 退货仓库编码
     */
    private String warehouseCode;

    /**
     * 退货仓库名称
     */
    private String warehouseName;

    /**
     * 物流公司id
     */
    private Long logisticId;

    /**
     * 物流名称
     */
    private String logisticName;

    /**
     * 发货仓库id
     */
    private Long sendWarehouseId;

    /**
     * 发货仓库名称
     */
    private String sendWarehouseName;

    /**
     * 物流单号
     */
    private String mainPostid;

    /**
     * 销售渠道id
     */
    private Long shopId;

    /**
     * 销售渠道
     */
    private String shopName;

    /**
     * 销售渠道Code
     */
    private String shopCode;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 原订单渠道id
     */
    private Long sourceShopId;

    /**
     * 原订单渠道
     */
    private String sourceShopName;

    /**
     * 售后原因描述
     */
    private String reasonDesc;

    /**
     * 取消售后原因描述
     */
    private String cancelReasonDesc;

    /**
     * 拒绝售后原因描述
     */
    private String rejectReasonDesc;

    /**
     * 问题描述
     */
    private String problemDesc;

    /**
     * 客户备注
     */
    private String customerRemark;

    /**
     * 客服备注
     */
    private String serviceRemark;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 收货时间
     */
    private Date deliveryTime;

    /**
     * 发货时间
     */
    private Date consignTime;

    /**
     * 货品摘要
     */
    private String goodslist;

    /**
     * 标记id
     */
    private String flagIds;

    /**
     * 标记名称
     */
    private String flagNames;

    /**
     * 登记人id
     */
    private Long registerId;

    /**
     * 登记人
     */
    private String registrant;

    /**
     * 责任人code
     */
    private Long responsiblePersonCode;

    /**
     * 责任人描述
     */
    private String responsiblePersonDesc;

    /**
     * 三方仓入库单号
     */
    private String stockInNo;

    /**
     * 收货单号
     */
    private String deliveryNo;

    /**
     * 收货经办人
     */
    private String deliveryPerson;

    /**
     * 审核人id
     */
    private Long auditorId;

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 源仓库id
     */
    private Long sourceWarehouseId;

    /**
     * 源仓库名称
     */
    private String sourceWarehouseName;

    /**
     * 订单摘要
     */
    private String tradeOrderSummary;

    /**
     * 冻结标记(1冻结0未冻结)
     */
    private Integer isFreeze;

    /**
     * 冻结原因
     */
    private String freezeReason;

    /**
     * 建单时间
     */
    private Date gmtCreate;

    /**
     * 最后修改时间
     */
    private Date gmtModified;

    /**
     * 原销售单状态
     */
    private Integer sourceTradeStatus;

    /**
     * 补发销售单
     */
    private String sendTradeNos;

    /**
     * 退货销售单
     */
    private String returnTradeNos;

    /**
     * 是否软删除
     */
    private Integer isDelete;

    /**
     * 配送方式（退）类型。1：普通快递 2：上门自提
     */
    private String returnLogisticType;

    /**
     * 配送方式（退）
     */
    private String returnLogisticTypeExplain;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 更新人
     */
    private String modifiedBy;

}