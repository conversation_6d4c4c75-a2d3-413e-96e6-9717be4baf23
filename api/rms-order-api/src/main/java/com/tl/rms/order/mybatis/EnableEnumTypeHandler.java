package com.tl.rms.order.mybatis;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import com.tl.rms.common.utils.AbstractCommonEnumUtils;
import com.tl.rms.order.domain.enums.EnableEnum;

/**
 * <AUTHOR>
 */
@MappedTypes({EnableEnum.class})
public class EnableEnumTypeHandler implements TypeHandler<EnableEnum> {

    @Override
    public void setParameter(PreparedStatement ps, int i, EnableEnum parameter, JdbcType jdbcType) throws SQLException {
        if (parameter != null) {
            ps.setInt(i, parameter.getCode());
        }
    }

    @Override
    public EnableEnum getResult(ResultSet rs, String columnName) throws SQLException {
        Integer code = rs.getInt(columnName);
        if (rs.wasNull()) {
            return null;
        }
        return AbstractCommonEnumUtils.getEnum(EnableEnum.class, code);
    }

    @Override
    public EnableEnum getResult(ResultSet rs, int columnIndex) throws SQLException {
        Integer code = rs.getInt(columnIndex);
        if (rs.wasNull()) {
            return null;
        }
        return AbstractCommonEnumUtils.getEnum(EnableEnum.class, code);
    }

    @Override
    public EnableEnum getResult(CallableStatement cs, int columnIndex) throws SQLException {
        Integer code = cs.getInt(columnIndex);
        if (cs.wasNull()) {
            return null;
        }
        return AbstractCommonEnumUtils.getEnum(EnableEnum.class, code);
    }
}
