package com.tl.rms.order.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "BMC同步机型与产业品类关联信息")
public class BmcSyncModelIndustryCategoryVo {

    @Schema(description = "机型ID")
    private String modelId;

    @Schema(description = "机型名称")
    private String modelName;

    @Schema(description = "产业ID")
    private String industryId;

    @Schema(description = "产业名称")
    private String industryName;

    @Schema(description = "品类ID")
    private String categoryId;

    @Schema(description = "品类名称")
    private String categoryName;
}
