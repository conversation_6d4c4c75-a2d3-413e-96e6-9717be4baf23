package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.BaseEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum OnlineOrderStatusEnum implements BaseEnum<String> {
    WAIT_BUYER_PAY("等待买家付款", "等待买家付款"), WAIT_SELLER_SEND("等待卖家发货", "等待卖家发货"),
    WAIT_BUYER_CONFIRM("等待买家确认收货", "等待买家确认收货"), NO_NEED_SHIP("无需发货", "无需发货"), TRADE_SUCCESS("交易成功", "交易成功"),
    TRADE_CLOSED("交易关闭", "交易关闭"), ABNORMAL_PAUSED("异常单，已暂停", "异常单，已暂停"), ABNORMAL_LOCKED("异常单，已锁定", "异常单，已锁定"),
    PARTIAL_SHIPPED("卖家部分发货", "卖家部分发货"), ABNORMAL_FROZEN("异常单，冻结中", "异常单，冻结中"), OUT_OF_STOCK("缺货", "缺货"),
    RETURN("退货", "退货"), COMPLAINT("投诉", "投诉"), SPLIT("已拆分", "已拆分"), RETURN_EXCHANGE("退换货", "退换货"),
    WAIT_INVOICE("待开票", "待开票"), REFUND("退款", "退款"), PROCESSING("处理中", "处理中"), CANCELLED("已取消", "已取消"),
    WAIT_CONFIRM("待确认", "待确认"), WAIT_PLATFORM_RECEIVE("待平台收货", "待平台收货"), WAIT_PLATFORM_SEND("待平台发货", "待平台发货"),
    OTHER("其他", "其他");

    private final String code;
    private final String desc;

    OnlineOrderStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     */
    public static OnlineOrderStatusEnum getByCode(String code) {
        for (OnlineOrderStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return OTHER;
    }

    /**
     * 根据desc获取枚举
     */
    public static OnlineOrderStatusEnum getByDesc(String desc) {
        for (OnlineOrderStatusEnum status : values()) {
            if (status.desc.equals(desc)) {
                return status;
            }
        }
        return OTHER;
    }
}
