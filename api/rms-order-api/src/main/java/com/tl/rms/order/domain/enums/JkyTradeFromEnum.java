package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.EnumInfoVo;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 吉客云订单来源
 */
@Getter
public enum JkyTradeFromEnum {

    RETAIL_BUSINESS("1", "网店下载"),
    CONSIGNMENT_FROM_DISTRIBUTOR("2", "手工新建"),
    ORDER_IMPORT("3", "订单导入"),
    JI_SHOP("4", "吉商城"),
    AFTER_SALE("6", "售后"),
    STORE("7", "门店"),
    DISTRIBUTION("8", "分销"),
    JI_CHAIN_PURCHASE("9", "吉链采购"),
    JI_CHAIN_DISTRIBUTION("10", "吉链分销"),
    JI_SHOP_DISTRIBUTION("11", "吉商城分销"),
    QIMEN_DISTRIBUTION("12", "奇门分销"),
    SALES_REBATE("13", "销售返利"),
    STORE_REPLACEMENT("14", "门店补货");

    private final String value;
    private final String label;

    JkyTradeFromEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static String getLabelByValue(String value) {
        return Arrays.stream(JkyTradeFromEnum.values()).filter(type -> type.getValue().equals(value))
                .findFirst().map(JkyTradeFromEnum::getLabel).orElse(null);
    }

    public static List<EnumInfoVo> list() {
        return Arrays.stream(JkyTradeFromEnum.values()).map(
                level -> new EnumInfoVo(level.getValue(), level.getLabel())).collect(Collectors.toList());
    }
}
