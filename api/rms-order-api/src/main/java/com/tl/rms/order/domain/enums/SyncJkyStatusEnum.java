package com.tl.rms.order.domain.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Package : com.tl.rms.order.domain.Enum
 * @Description : 吉客云同步状态枚举
 * @Create on : 2025/4/16 14:50
 **/
@Getter
public enum SyncJkyStatusEnum {
    NOT_SYNC(0, "未同步"),
    SYNCING(1, "已同步"),
    SYNCING_ERROR(2, "同步"),
    ;

    private final Integer value;
    private final String desc;

    SyncJkyStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
