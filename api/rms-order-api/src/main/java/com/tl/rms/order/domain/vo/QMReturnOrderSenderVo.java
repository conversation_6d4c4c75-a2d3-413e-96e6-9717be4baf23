package com.tl.rms.order.domain.vo;

import jakarta.xml.bind.annotation.XmlAccessorType;
import lombok.Data;

/**
 * 奇门退货入库单发件人信息VO
 * <AUTHOR>
 */
@Data
@XmlAccessorType(jakarta.xml.bind.annotation.XmlAccessType.FIELD)
public class QMReturnOrderSenderVo {
    
    /**
     * 公司名称, string (200)
     */
    private String company;

    /**
     * 姓名, string (50) , 必填
     */
    private String name;

    /**
     * 邮编, string (50)
     */
    private String zipCode;

    /**
     * 固定电话, string (50)
     */
    private String tel;

    /**
     * 移动电话, string (50) , 必填
     */
    private String mobile;

    /**
     * 电子邮箱, string (50)
     */
    private String email;

    /**
     * 国家二字码，string（50）
     */
    private String countryCode;

    /**
     * 省份, string (50) , 必填
     */
    private String province;

    /**
     * 城市, string (50) , 必填
     */
    private String city;

    /**
     * 区域, string (50)
     */
    private String area;

    /**
     * 村镇, string (50)
     */
    private String town;

    /**
     * 详细地址, string (200) , 必填
     */
    private String detailAddress;
}
