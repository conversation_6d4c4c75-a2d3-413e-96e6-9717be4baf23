package com.tl.rms.order.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "吉客云订单对象")
public class JkyOrderVo {

    @Schema(description = "系统编码")
    private Long tradeId;

    @Schema(description = "订单编号")
    private String tradeNo;

    @Schema(description = "对账金额")
    private BigDecimal checkTotal;

    @Schema(description = "其它费用")
    private BigDecimal otherFee;

    @Schema(description = "结算币种")
    private String chargeCurrency;

    @Schema(description = "收款账户")
    private String accountName;

    @Schema(description = "支付方式 1.支付宝 2.财付通 3.微信支付 4.银联支付 5.盛付通 6.其它 7.现金 8.储值卡 9.扫码付 10.挂账 11.诺诺支付 16.易付宝 32.有赞支付 33.汇付支付 35.商盟支付 36.易宝支付 37.汇聚支付 38.合利宝支付 27.通联支付")
    private Integer payType;

    @Schema(description = "支付单号")
    private String payNo;

    @Schema(description = "卖家备注")
    private String sellerMemo;

    @Schema(description = "买家备注")
    private String buyerMemo;

    @Schema(description = "追加备注")
    private String appendMemo;

    @Schema(description = "订单来源 1.网店下载 2.手工新建 3.订单导入 4.吉商城 6.售后 7.门店 8.分销 9.吉链采购 10.吉链分销 11.吉商城分销 12.奇门分销 13.销售返利 14.门店补货")
    private Integer tradeFrom;

    @Schema(description = "登记人")
    private String register;

    @Schema(description = "业务员")
    private String seller;

    @Schema(description = "审核人")
    private String auditor;

    @Schema(description = "复核人")
    private String reviewer;

    @Schema(description = "预估重量")
    private BigDecimal estimateWeight;

    @Schema(description = "包裹重量")
    private BigDecimal packageWeight;

    @Schema(description = "订单总数量")
    private BigDecimal tradeCount;

    @Schema(description = "商品样数")
    private BigDecimal goodsTypeCount;

    @Schema(description = "冻结原因")
    private String freezeReason;

    @Schema(description = "问题单具体描述")
    private String abnormalDescription;

    @Schema(description = "网店订单号")
    private String onlineTradeNo;

    @Schema(description = "货品摘要")
    private String goodsList;

    @Schema(description = "创建时间")
    private LocalDateTime gmtCreate;

    @Schema(description = "最后修改时间")
    private LocalDateTime gmtModified;

    @Schema(description = "出库单号")
    private String stockoutNo;

    @Schema(description = "确认时间")
    private LocalDateTime confirmTime;

    @Schema(description = "部门名称")
    private String departName;

    @Schema(description = "承诺发货时间")
    private LocalDateTime lastShipTime;

    @Schema(description = "付款状态 0.未付款 5.部分付款 9.已付款")
    private Integer payStatus;

    @Schema(description = "结算币种编码")
    private String chargeCurrencyCode;

    @Schema(description = "结算汇率")
    private BigDecimal chargeExchangeRate;

    @Schema(description = "订单状态 1010待审核,1020审核中,1030预售,1050待复核,2000备货等待,2010备货等待等补货,2020服务等待,2030备货等待等生产,2040采购等待,3010虚拟发货,4110待发货待递交,4111待发货递交中,4112待发货已递交,4113待发货-递交失败,4121待发货-取消中,4122待发货已取消,4123待发货取消失败,4130待发货部分发货,4040代销发货待递交,4041代销发货已递交,5010已取消,5020已取消被合并,5030已取消被拆分,6000发货在途,9090已完成")
    private Integer tradeStatus;

    @Schema(description = "毛利")
    private BigDecimal grossProfit;

    @Schema(description = "订单预估体积")
    private BigDecimal estimateVolume;

    @Schema(description = "客户类型")
    private String customerTypeName;

    @Schema(description = "客户等级")
    private String customerGradeName;

    @Schema(description = "客户标签")
    private String customerTags;

    @Schema(description = "客户编号")
    private String customerCode;

    @Schema(description = "折扣")
    private BigDecimal customerDiscount;

    @Schema(description = "特别提醒")
    private String specialReminding;

    @Schema(description = "黑名单")
    private Integer blackList;

    @Schema(description = "下单时间")
    private LocalDateTime tradeTime;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "省")
    private String state;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "区县")
    private String district;

    @Schema(description = "街道")
    private String town;

    @Schema(description = "邮编")
    private String zip;

    @Schema(description = "支付时间")
    private LocalDateTime payTime;

    @Schema(description = "国家编码")
    private String countryCode;

    @Schema(description = "城市编码")
    private String cityCode;

    @Schema(description = "发票类型")
    private Integer invoiceType;

    @Schema(description = "购方名称")
    private String payerName;

    @Schema(description = "购方税号")
    private String payerRegno;

    @Schema(description = "购方开户行及帐号")
    private String payerBankAccount;

    @Schema(description = "购方电话")
    private String payerPhone;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "购方地址")
    private String payerAddress;

    @Schema(description = "发票号码")
    private String invoiceNo;

    @Schema(description = "发票代码")
    private String invoiceCode;

    @Schema(description = "发票开具状态")
    private Integer invoiceStatus;

    @Schema(description = "购方开户行")
    private String payerBankName;

    @Schema(description = "预定类别描述")
    private String preTypeDetail;

    @Schema(description = "付首款金额")
    private BigDecimal firstPayment;

    @Schema(description = "付尾款金额")
    private BigDecimal finalPayment;

    @Schema(description = "付首款时间")
    private LocalDateTime firstPayTime;

    @Schema(description = "付尾款时间")
    private LocalDateTime finalPayTime;

    @Schema(description = "复核时间")
    private LocalDateTime reviewTime;

    @Schema(description = "激活时间")
    private LocalDateTime activationTime;

    @Schema(description = "终端货款合计")
    private BigDecimal customerTotalFee;

    @Schema(description = "终端优惠")
    private BigDecimal customerDiscountFee;

    @Schema(description = "通知仓库发货时间")
    private LocalDateTime notifyPickTime;

    @Schema(description = "发货时间")
    private LocalDateTime consignTime;

    @Schema(description = "发货单单号")
    private String orderNo;

    @Schema(description = "终端应收邮资")
    private BigDecimal customerPostFee;

    @Schema(description = "店铺id")
    private String shopId;

    @Schema(description = "店铺编码")
    private String shopCode;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "终端应收合计")
    private BigDecimal customerPayment;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "对账状态 1对账，其他未对账")
    private Boolean isBillCheck;

    @Schema(description = "仓库编码")
    private String warehouseCode;

    @Schema(description = "仓库名称")
    private String warehouseName;

    @Schema(description = "物流名称")
    private String logisticName;

    @Schema(description = "对账时间")
    private LocalDateTime billDate;

    @Schema(description = "配送方式 1.普通快递 2.上门自提 3.门店配送 4.二次配送 5.无需配送 6.线下配送 7.自有物流")
    private Integer logisticType;

    @Schema(description = "物流单号")
    private String mainPostId;

    @Schema(description = "订单类型 1:零售业务,2:代发货(来自分销商),3:预售订单,4:周期性订购,5:代销售(供货商发货),6:现款现货,7:售后发货,8:售后退货,9:批发业务(B2B),10:试销业务,11:错漏调整,12:仅退款,13:销售返利,91:自定义1,92:自定义2,93:自定义3...100:自定义10")
    private Integer tradeType;

    @Schema(description = "商品金额")
    private BigDecimal totalFee;

    @Schema(description = "税额")
    private BigDecimal taxFee;

    @Schema(description = "应收邮资")
    private BigDecimal receivedPostFee;

    @Schema(description = "优惠金额")
    private BigDecimal discountFee;

    @Schema(description = "应收金额")
    private BigDecimal payment;

    @Schema(description = "平台优惠")
    private BigDecimal couponFee;

    @Schema(description = "已收金额")
    private BigDecimal receivedTotal;

    @Schema(description = "预估邮资")
    private BigDecimal postFee;

    @Schema(description = "完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "签收时间")
    private LocalDateTime signingTime;

    @Schema(description = "审核时间")
    private LocalDateTime settleAuditTime;

    @Schema(description = "是否删除 0.未被删除 1.被删除")
    private Integer isDelete;

    @Schema(description = "应收合计（本币）")
    private BigDecimal localPayment;

    @Schema(description = "汇率")
    private BigDecimal localExchangeRate;

    @Schema(description = "客户账号")
    private String customerAccount;

    @Schema(description = "公司本币")
    private String localCurrencyCode;

    @Schema(description = "平台完成时间")
    private LocalDateTime platCompleteTime;

    @Schema(description = "销售平台编号")
    private String shopTypeCode;

    @Schema(description = "标记id")
    private String flagIds;

    @Schema(description = "标记名称")
    private String flagNames;

    @Schema(description = "系统标记")
    private String sysFlagIds;

    @Schema(description = "售后来源单号")
    private String sourceAfterNo;

    @Schema(description = "政府补贴金额")
    private String govSubsidyAmount;

    @Schema(description = "商家承担国补金额")
    private String govSubsidyAmountMerchant;

    @Schema(description = "净应收金额")
    private String ebsAmount;

    @Schema(description = "采购折扣点位%")
    private BigDecimal purchaseDiscountRate;

    @Schema(description = "寄售客户编码")
    private String consignCode;

    @Schema(description = "发送EBS状态 0无需发送 1已发送 2待发送")
    private Integer sendEbsStatus;

    @Schema(description = "发送EBS时间")
    private LocalDateTime sendEbsTime;

    @Schema(description = "创建日期")
    private Integer createDate;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "更新时间")
    private LocalDateTime modifyTime;

    @Schema(description = "更新人")
    private String modifiedBy;

    @Schema(description = "异常备注")
    private String remark;

    @Schema(description = "订单商品列表")
    private List<JkyOrderGoodsVo> goodsVoList;

    // t_jky_order_ship
    @Schema(description = "物流签售时间(最早)")
    private LocalDateTime earliestSignTime;

    @Schema(description = "政府承担国补金额(非DB字段) = 【国补金额】-【商家承担国补金额】")
    private String govSubsidyAmountGovernment;

    @Schema(description = "净国补金额(非DB字段) = 【政府承担国补金额】* (1-【采购折扣点位(%)】)")
    private String netGovSubsidyAmount;

    @Schema(description = "采购折扣点位费用(非DB字段) = (【分摊后金额(行)】-【商家承担国补金额-抖音】) *【采购折扣点位(%)】")
    private String purchaseDiscountFee;

}