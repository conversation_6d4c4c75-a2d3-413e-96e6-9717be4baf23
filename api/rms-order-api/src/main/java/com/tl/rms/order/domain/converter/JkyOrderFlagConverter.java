package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.JkyOrderFlag;
import com.tl.rms.order.domain.vo.JkyOrderFlagVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface JkyOrderFlagConverter {

    JkyOrderFlagConverter MAPPER = Mappers.getMapper(JkyOrderFlagConverter.class);

    JkyOrderFlagVo toVo(JkyOrderFlag po);

    JkyOrderFlag toPo(JkyOrderFlagVo vo);

    List<JkyOrderFlagVo> toVoList(List<JkyOrderFlag> poList);

    List<JkyOrderFlag> toPoList(List<JkyOrderFlagVo> voList);
}