package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.EnumInfoVo;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 吉客云配送方式
 */
@Getter
public enum JkyTradeLogisticTypeEnum {

    NORMAL_EXPRESS("1", "普通快递"),
    SELF_PICKUP("2", "上门自提"),
    STORE_DELIVERY("3", "门店配送"),
    SECOND_DELIVERY("4", "二次配送"),
    NO_DELIVERY("5", "无需配送"),
    OFFLINE_DELIVERY("6", "线下配送"),
    OWN_LOGISTICS("7", "自有物流");

    private final String value;
    private final String label;

    JkyTradeLogisticTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static String getLabelByValue(String value) {
        return Arrays.stream(JkyTradeLogisticTypeEnum.values()).filter(type -> type.getValue().equals(value))
                .findFirst().map(JkyTradeLogisticTypeEnum::getLabel).orElse(null);
    }

    public static List<EnumInfoVo> list() {
        return Arrays.stream(JkyTradeLogisticTypeEnum.values()).map(
                level -> new EnumInfoVo(level.getValue(), level.getLabel())).collect(Collectors.toList());
    }
}
