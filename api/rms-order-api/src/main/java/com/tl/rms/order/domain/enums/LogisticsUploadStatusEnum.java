package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.BaseEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum LogisticsUploadStatusEnum implements BaseEnum<String> {
    NOT_UPLOADED("未上传", "未上传"), PENDING_UPLOAD("待上传", "待上传"), UPLOAD_SUCCESS("上传成功", "上传成功"),
    PARTIAL_UPLOAD("部分上传", "部分上传"), UPLOAD_FAILED("上传失败", "上传失败"), UPLOAD_CANCELED("取消上传", "取消上传");

    private final String code;
    private final String desc;

    LogisticsUploadStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     */
    public static LogisticsUploadStatusEnum getByCode(String code) {
        for (LogisticsUploadStatusEnum status : values()) {
            if (status.code.equalsIgnoreCase(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid LogisticsUploadStatusEnum code: " + code);
    }

    /**
     * 根据desc获取枚举
     */
    public static LogisticsUploadStatusEnum getByDesc(String desc) {
        for (LogisticsUploadStatusEnum status : values()) {
            if (status.desc.equals(desc)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid LogisticsUploadStatusEnum desc: " + desc);
    }
}