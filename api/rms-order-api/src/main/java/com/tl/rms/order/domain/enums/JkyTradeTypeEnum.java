package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.EnumInfoVo;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 吉客云订单类型
 */
@Getter
public enum JkyTradeTypeEnum {

    RETAIL_BUSINESS("1", "零售业务"),
    CONSIGNMENT_FROM_DISTRIBUTOR("2", "代发货(来自分销商)"),
    PRE_SALE_ORDER("3", "预售订单"),
    PERIODIC_ORDER("4", "周期性订购"),
    CONSIGNMENT_SALE("5", "代销售(供货商发货)"),
    CASH_ON_DELIVERY("6", "现款现货"),
    AFTER_SALE_DELIVERY("7", "售后发货"),
    AFTER_SALE_RETURN("8", "售后退货"),
    WHOLESALE_BUSINESS("9", "批发业务(B2B)"),
    TRIAL_SALE("10", "试销业务"),
    ERROR_ADJUSTMENT("11", "错漏调整"),
    REFUND_ONLY("12", "仅退款"),
    SALES_REBATE("13", "销售返利");

    private final String value;
    private final String label;

    JkyTradeTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static String getLabelByValue(String value) {
        return Arrays.stream(JkyTradeTypeEnum.values()).filter(type -> type.getValue().equals(value))
                .findFirst().map(JkyTradeTypeEnum::getLabel).orElse(null);
    }

    public static List<EnumInfoVo> list() {
        return Arrays.stream(JkyTradeTypeEnum.values()).map(
                level -> new EnumInfoVo(level.getValue(), level.getLabel())).collect(Collectors.toList());
    }
}
