package com.tl.rms.order.domain.converter;

import com.google.common.base.Splitter;
import com.tl.rms.common.utils.AbstractCommonEnumUtils;
import com.tl.rms.order.domain.enums.AuditStrategyRelationShipEnum;
import com.tl.rms.order.domain.enums.EnableEnum;
import com.tl.rms.order.domain.enums.JkyOrderAuditStrategyTypeEnum;
import com.tl.rms.order.domain.po.JkyOrderAutoAuditStrategy;
import com.tl.rms.order.domain.vo.JkyOrderAutoAuditStrategyDetailVo;
import com.tl.rms.order.domain.vo.JkyOrderAutoAuditStrategySaveVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface JkyOrderAutoAuditStrategyConverter {
    JkyOrderAutoAuditStrategyConverter MAPPER = Mappers.getMapper(JkyOrderAutoAuditStrategyConverter.class);

    default JkyOrderAutoAuditStrategy from(JkyOrderAutoAuditStrategySaveVo vo, boolean add) {
        JkyOrderAutoAuditStrategy jkyOrderAutoAuditStrategy = new JkyOrderAutoAuditStrategy();
        BeanUtils.copyProperties(vo, jkyOrderAutoAuditStrategy);

        jkyOrderAutoAuditStrategy.setStrategyType(
            AbstractCommonEnumUtils.getEnum(JkyOrderAuditStrategyTypeEnum.class, vo.getStrategyType()));
        jkyOrderAutoAuditStrategy.setTriggerRelationType(
            AbstractCommonEnumUtils.getEnum(AuditStrategyRelationShipEnum.class, vo.getTriggerRelationType()));
        jkyOrderAutoAuditStrategy.setStatus(AbstractCommonEnumUtils.getEnum(EnableEnum.class, vo.getStatus()));

        if (add) {
            jkyOrderAutoAuditStrategy.setCreateBy(vo.getOperator());
            jkyOrderAutoAuditStrategy.setUpdateBy(vo.getOperator());
        } else {
            jkyOrderAutoAuditStrategy.setUpdateBy(vo.getOperator());
        }
        return jkyOrderAutoAuditStrategy;
    }

    default JkyOrderAutoAuditStrategyDetailVo from(JkyOrderAutoAuditStrategy strategy) {
        JkyOrderAutoAuditStrategyDetailVo detail = new JkyOrderAutoAuditStrategyDetailVo();
        BeanUtils.copyProperties(strategy, detail);
        detail.setStrategyType(strategy.getStrategyType().getCode());
        detail.setTriggerRelationType(strategy.getTriggerRelationType().getCode());
        detail.setStatus(strategy.getStatus().getCode());
        if (StringUtils.isNotEmpty(strategy.getShops())) {
            detail.setShops(Splitter.on(",").splitToList(strategy.getShops()));
        }
        return detail;
    }

    default List<JkyOrderAutoAuditStrategyDetailVo> from(List<JkyOrderAutoAuditStrategy> strategies) {
        if (CollectionUtils.isEmpty(strategies)) {
            return Collections.emptyList();
        }
        List<JkyOrderAutoAuditStrategyDetailVo> list = new ArrayList<>(strategies.size());
        for (JkyOrderAutoAuditStrategy strategy : strategies) {
            list.add(from(strategy));
        }
        return list;
    }
}
