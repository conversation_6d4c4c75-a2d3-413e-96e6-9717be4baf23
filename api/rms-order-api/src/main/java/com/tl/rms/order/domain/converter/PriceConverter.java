package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.Price;
import com.tl.rms.order.domain.vo.BmcPushPriceVo;
import com.tl.rms.order.domain.vo.PriceVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PriceConverter {

    PriceConverter MAPPER = Mappers.getMapper(PriceConverter.class);

    PriceVo toVo(Price po);

    Price toPo(PriceVo vo);

    List<PriceVo> toVoList(List<Price> poList);

    List<Price> toPoList(List<PriceVo> voList);

    Price pushVoToPo(BmcPushPriceVo vo);
}