package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.Distribution;
import com.tl.rms.order.domain.vo.DistributionCreateDetailsVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface DistributionConverter {

    DistributionConverter MAPPER = Mappers.getMapper(DistributionConverter.class);

    DistributionCreateDetailsVo toVo(Distribution po);

}