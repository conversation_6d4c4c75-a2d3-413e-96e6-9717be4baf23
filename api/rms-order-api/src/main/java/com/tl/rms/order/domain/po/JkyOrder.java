package com.tl.rms.order.domain.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("t_jky_order")
public class JkyOrder {

    /**
     * 系统编码，主键
     */
    @TableId("trade_id")
    private Long tradeId;

    /**
     * 订单编号
     */
    @TableField("trade_no")
    private String tradeNo;

    /**
     * 对账金额
     */
    @TableField("check_total")
    private BigDecimal checkTotal;

    /**
     * 其它费用
     */
    @TableField("other_fee")
    private BigDecimal otherFee;

    /**
     * 结算币种
     */
    @TableField("charge_currency")
    private String chargeCurrency;

    /**
     * 收款账户
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 支付方式，如：1.支付宝 2.财付通 3.微信支付 4.银联支付 5.盛付通 6.其它 7.现金 8.储值卡 9.扫码付 10.挂账 11.诺诺支付 16.易付宝 32.有赞支付 33.汇付支付 35.商盟支付 36.易宝支付 37.汇聚支付 38.合利宝支付 27.通联支付
     */
    @TableField("pay_type")
    private Integer payType;

    /**
     * 支付单号
     */
    @TableField("pay_no")
    private String payNo;

    /**
     * 卖家备注
     */
    @TableField("seller_memo")
    private String sellerMemo;

    /**
     * 买家备注
     */
    @TableField("buyer_memo")
    private String buyerMemo;

    /**
     * 追加备注
     */
    @TableField("append_memo")
    private String appendMemo;

    /**
     * 订单来源，如：1.网店下载 2.手工新建 3.订单导入 4.吉商城 6.售后 7.门店 8.分销 9.吉链采购 10.吉链分销 11.吉商城分销 12.奇门分销 13.销售返利 14.门店补货
     */
    @TableField("trade_from")
    private Integer tradeFrom;

    /**
     * 登记人
     */
    @TableField("register")
    private String register;

    /**
     * 业务员
     */
    @TableField("seller")
    private String seller;

    /**
     * 审核人
     */
    @TableField("auditor")
    private String auditor;

    /**
     * 复核人
     */
    @TableField("reviewer")
    private String reviewer;

    /**
     * 预估重量
     */
    @TableField("estimate_weight")
    private BigDecimal estimateWeight;

    /**
     * 包裹重量
     */
    @TableField("package_weight")
    private BigDecimal packageWeight;

    /**
     * 订单总数量
     */
    @TableField("trade_count")
    private BigDecimal tradeCount;

    /**
     * 商品样数
     */
    @TableField("goods_type_count")
    private BigDecimal goodsTypeCount;

    /**
     * 冻结原因
     */
    @TableField("freeze_reason")
    private String freezeReason;

    /**
     * 问题单具体描述
     */
    @TableField("abnormal_description")
    private String abnormalDescription;

    /**
     * 网店订单号
     */
    @TableField("online_trade_no")
    private String onlineTradeNo;

    /**
     * 货品摘要
     */
    @TableField("goodslist")
    private String goodslist;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private LocalDateTime gmtCreate;

    /**
     * 最后修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 出库单号
     */
    @TableField("stockout_no")
    private String stockoutNo;

    /**
     * 确认时间
     */
    @TableField("confirm_time")
    private LocalDateTime confirmTime;

    /**
     * 部门名称
     */
    @TableField("depart_name")
    private String departName;

    /**
     * 承诺发货时间
     */
    @TableField("last_ship_time")
    private LocalDateTime lastShipTime;

    /**
     * 付款状态，如：0.未付款 5.部分付款 9.已付款
     */
    @TableField("pay_status")
    private Integer payStatus;

    /**
     * 结算币种编码
     */
    @TableField("charge_currency_code")
    private String chargeCurrencyCode;

    /**
     * 结算汇率
     */
    @TableField("charge_exchange_rate")
    private BigDecimal chargeExchangeRate;

    /**
     * 订单状态，如：1010待审核，1020审核中，1030预售，1050待复核，2000备货等待，2010备货等待等补货，2020服务等待，2030备货等待等生产，2040采购等待，3010虚拟发货，4110待发货待递交，4111待发货递交中，4112待发货已递交，4113待发货-递交失败，4121待发货-取消中，4122待发货已取消，4123待发货取消失败，4130待发货部分发货，4040代销发货待递交，4041代销发货已递交，5010已取消，5020已取消被合并，5030已取消被拆分，6000发货在途，9090已完成
     */
    @TableField("trade_status")
    private Integer tradeStatus;

    /**
     * 毛利
     */
    @TableField("gross_profit")
    private BigDecimal grossProfit;

    /**
     * 订单预估体积
     */
    @TableField("estimate_volume")
    private BigDecimal estimateVolume;

    /**
     * 客户类型名称
     */
    @TableField("customer_type_name")
    private String customerTypeName;

    /**
     * 客户等级名称
     */
    @TableField("customer_grade_name")
    private String customerGradeName;

    /**
     * 客户标签
     */
    @TableField("customer_tags")
    private String customerTags;

    /**
     * 客户编号
     */
    @TableField("customer_code")
    private String customerCode;

    /**
     * 折扣
     */
    @TableField("customer_discount")
    private BigDecimal customerDiscount;

    /**
     * 特别提醒
     */
    @TableField("special_reminding")
    private String specialReminding;

    /**
     * 黑名单标记
     */
    @TableField("black_list")
    private Integer blackList;

    /**
     * 下单时间
     */
    @TableField("trade_time")
    private LocalDateTime tradeTime;

    /**
     * 国家
     */
    @TableField("country")
    private String country;

    /**
     * 省
     */
    @TableField("state")
    private String state;

    /**
     * 城市
     */
    @TableField("city")
    private String city;

    /**
     * 区县
     */
    @TableField("district")
    private String district;

    /**
     * 街道
     */
    @TableField("town")
    private String town;

    /**
     * 邮编
     */
    @TableField("zip")
    private String zip;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    private LocalDateTime payTime;

    /**
     * 国家编码
     */
    @TableField("country_code")
    private String countryCode;

    /**
     * 城市编码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 发票类型
     */
    @TableField("invoice_type")
    private Integer invoiceType;

    /**
     * 购方名称
     */
    @TableField("payer_name")
    private String payerName;

    /**
     * 购方税号
     */
    @TableField("payer_regno")
    private String payerRegno;

    /**
     * 购方开户行及帐号
     */
    @TableField("payer_bank_account")
    private String payerBankAccount;

    /**
     * 购方电话
     */
    @TableField("payer_phone")
    private String payerPhone;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;

    /**
     * 购方地址
     */
    @TableField("payer_address")
    private String payerAddress;

    /**
     * 发票号码
     */
    @TableField("invoice_no")
    private String invoiceNo;

    /**
     * 发票代码
     */
    @TableField("invoice_code")
    private String invoiceCode;

    /**
     * 发票开具状态
     */
    @TableField("invoice_status")
    private Integer invoiceStatus;

    /**
     * 购方开户行
     */
    @TableField("payer_bank_name")
    private String payerBankName;

    /**
     * 预定类别描述
     */
    @TableField("pre_typedetail")
    private String preTypedetail;

    /**
     * 付首款金额
     */
    @TableField("first_payment")
    private BigDecimal firstPayment;

    /**
     * 付尾款金额
     */
    @TableField("final_payment")
    private BigDecimal finalPayment;

    /**
     * 付首款时间
     */
    @TableField("first_paytime")
    private LocalDateTime firstPaytime;

    /**
     * 付尾款时间
     */
    @TableField("final_paytime")
    private LocalDateTime finalPaytime;

    /**
     * 复核时间
     */
    @TableField("review_time")
    private LocalDateTime reviewTime;

    /**
     * 激活时间
     */
    @TableField("activation_time")
    private LocalDateTime activationTime;

    /**
     * 终端货款合计
     */
    @TableField("customer_total_fee")
    private BigDecimal customerTotalFee;

    /**
     * 终端优惠
     */
    @TableField("customer_discount_fee")
    private BigDecimal customerDiscountFee;

    /**
     * 通知仓库发货时间
     */
    @TableField("notify_pick_time")
    private LocalDateTime notifyPickTime;

    /**
     * 发货时间
     */
    @TableField("consign_time")
    private LocalDateTime consignTime;

    /**
     * 发货单单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 终端应收邮资
     */
    @TableField("customer_post_fee")
    private BigDecimal customerPostFee;

    /**
     * 店铺id
     */
    @TableField("shop_id")
    private String shopId;

    /**
     * 店铺编码
     */
    @TableField("shop_code")
    private String shopCode;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 终端应收合计
     */
    @TableField("customer_payment")
    private BigDecimal customerPayment;

    /**
     * 公司名称
     */
    @TableField("company_name")
    private String companyName;

    /**
     * 对账状态 1对账，其他未对账
     */
    @TableField("is_bill_check")
    private Boolean isBillCheck;

    /**
     * 仓库编码
     */
    @TableField("warehouse_code")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @TableField("warehouse_name")
    private String warehouseName;

    /**
     * 物流名称
     */
    @TableField("logistic_name")
    private String logisticName;

    /**
     * 对账时间
     */
    @TableField("bill_date")
    private LocalDateTime billDate;

    /**
     * 配送方式 1.普通快递 2.上门自提 3.门店配送 4.二次配送 5.无需配送 6.线下配送 7.自有物流
     */
    @TableField("logistic_type")
    private Integer logisticType;

    /**
     * 物流单号
     */
    @TableField("main_postid")
    private String mainPostid;

    /**
     * 订单类型 1:零售业务,2:代发货(来自分销商),3:预售订单,4:周期性订购,5:代销售(供货商发货),6:现款现货,7:售后发货,8:售后退货,9:批发业务(B2B),10:试销业务,11:错漏调整,12:仅退款,13:销售返利,91:自定义1,92:自定义2,93:自定义3...100:自定义10
     */
    @TableField("trade_type")
    private Integer tradeType;

    /**
     * 商品金额
     */
    @TableField("total_fee")
    private BigDecimal totalFee;

    /**
     * 税额
     */
    @TableField("tax_fee")
    private BigDecimal taxFee;

    /**
     * 应收邮资
     */
    @TableField("received_post_fee")
    private BigDecimal receivedPostFee;

    /**
     * 优惠金额
     */
    @TableField("discount_fee")
    private BigDecimal discountFee;

    /**
     * 应收金额
     */
    @TableField("payment")
    private BigDecimal payment;

    /**
     * 平台优惠
     */
    @TableField("coupon_fee")
    private BigDecimal couponFee;

    /**
     * 已收金额
     */
    @TableField("received_total")
    private BigDecimal receivedTotal;

    /**
     * 预估邮资
     */
    @TableField("post_fee")
    private BigDecimal postFee;

    /**
     * 完成时间
     */
    @TableField("complete_time")
    private LocalDateTime completeTime;

    /**
     * 签收时间
     */
    @TableField("signing_time")
    private LocalDateTime signingTime;

    /**
     * 审核时间
     */
    @TableField("settle_audit_time")
    private LocalDateTime settleAuditTime;

    /**
     * 是否删除 0.未被删除 1.被删除
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 应收合计（本币）
     */
    @TableField("local_payment")
    private BigDecimal localPayment;

    /**
     * 汇率
     */
    @TableField("local_exchange_rate")
    private BigDecimal localExchangeRate;

    /**
     * 客户账号
     */
    @TableField("customer_account")
    private String customerAccount;

    /**
     * 公司本币
     */
    @TableField("local_currency_code")
    private String localCurrencyCode;

    /**
     * 平台完成时间
     */
    @TableField("plat_complete_time")
    private LocalDateTime platCompleteTime;

    /**
     * 销售平台编号
     */
    @TableField("shop_type_code")
    private String shopTypeCode;

    /**
     * 标记id
     */
    @TableField("flag_ids")
    private String flagIds;

    /**
     * 标记名称
     */
    @TableField("flag_names")
    private String flagNames;

    /**
     * 系统标记
     */
    @TableField("sys_flag_ids")
    private String sysFlagIds;

    /**
     * 售后来源单号
     */
    @TableField("source_after_no")
    private String sourceAfterNo;

    /**
     * 政府补贴
     */
    @TableField("gov_subsidy")
    private String govSubsidy;

    /**
     * 政府补贴金额
     */
    @TableField("gov_subsidy_amount")
    private String govSubsidyAmount;

    /**
     * 商家承担国补金额
     */
    @TableField("gov_subsidy_amount_merchant")
    private String govSubsidyAmountMerchant;

    /**
     * 净应收金额
     */
    @TableField("ebs_amount")
    private String ebsAmount;

    /**
     * 采购折扣点位%
     */
    @TableField("purchase_discount_rate")
    private BigDecimal purchaseDiscountRate;

    /**
     * 寄售客户编码
     */
    @TableField("consign_code")
    private String consignCode;

    /**
     * 发送EBS状态 0无需发送 1已发送 2待发送
     */
    @TableField("send_ebs_status")
    private Integer sendEbsStatus;

    /**
     * 发送EBS时间
     */
    @TableField("send_ebs_time")
    private LocalDateTime sendEbsTime;

    /**
     * 客户编码
     */
    @TableField("customer_id")
    private String customerId;

    /**
     * 分公司名称
     */
    @TableField("branch_name")
    private String branchName;

    /**
     * 创建日期
     */
    @TableField("create_date")
    private Integer createDate;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新时间
     */
    @TableField("modify_time")
    private LocalDateTime modifyTime;

    /**
     * 更新人
     */
    @TableField("modified_by")
    private String modifiedBy;

    /**
     * 异常备注
     */
    @TableField("remark")
    private String remark;

}