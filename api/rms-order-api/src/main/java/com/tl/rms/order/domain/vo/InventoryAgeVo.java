package com.tl.rms.order.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Schema(description = "库龄对象")
public class InventoryAgeVo {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "账龄日期")
    private LocalDate expiryDate;

    @Schema(description = "业务实体")
    private String ouName;

    @Schema(description = "部门ID")
    private String department;

    @Schema(description = "部门")
    private String departmentName;

    @Schema(description = "二级部门")
    private String secondDepName;

    @Schema(description = "生意模式")
    private String businessModeName;

    @Schema(description = "项目ID")
    private String project;

    @Schema(description = "项目")
    private String projectName;

    @Schema(description = "产业")
    private String productGenera;

    @Schema(description = "品类")
    private String productCategory;

    @Schema(description = "产品系列")
    private String productSubclass;

    @Schema(description = "产品类型")
    private String materialType;

    @Schema(description = "机型系列")
    private String modelSeries;

    @Schema(description = "机型")
    private String materialModel;

    @Schema(description = "颜色")
    private String color;

    @Schema(description = "是否样机")
    private String isPrototype;

    @Schema(description = "物料描述")
    private String description;

    @Schema(description = "物料id")
    private String itemCode;

    @Schema(description = "总库存数量")
    private Integer totalQty;

    @Schema(description = "总库存金额")
    private BigDecimal totalAmount;

    @Schema(description = "本仓数量")
    private Integer totalCurrentinvQty;

    @Schema(description = "铺货数量")
    private Integer totalPackinvQty;

    @Schema(description = "0-30天数量")
    private Integer firstInterval;

    @Schema(description = "0-30天金额")
    private BigDecimal firstAmount;

    @Schema(description = "0-30天占比")
    private BigDecimal firstRate;

    @Schema(description = "31-60天数量")
    private Integer secondInterval;

    @Schema(description = "31-60天金额")
    private BigDecimal secondAmount;

    @Schema(description = "31-60天占比")
    private BigDecimal secondRate;

    @Schema(description = "61-90天数量")
    private Integer thirdlyInterval;

    @Schema(description = "61-90天金额")
    private BigDecimal thirdlyAmount;

    @Schema(description = "61-90天占比")
    private BigDecimal thirdlyRate;

    @Schema(description = "91-120天数量")
    private Integer fourthlyInterval;

    @Schema(description = "91-120天金额")
    private BigDecimal fourthlyAmount;

    @Schema(description = "91-120天占比")
    private BigDecimal fourthlyRate;

    @Schema(description = "121-150天数量")
    private Integer eleventhInterval;

    @Schema(description = "121-150天金额")
    private BigDecimal eleventhAmount;

    @Schema(description = "121-150天占比")
    private BigDecimal eleventhRate;

    @Schema(description = "151-180天数量")
    private Integer twelfthInterval;

    @Schema(description = "151-180天金额")
    private BigDecimal twelfthAmount;

    @Schema(description = "151-180天占比")
    private BigDecimal twelfthRate;

    @Schema(description = "181-210天数量")
    private Integer thirteenthInterval;

    @Schema(description = "181-210天金额")
    private BigDecimal thirteenthAmount;

    @Schema(description = "181-210天占比")
    private BigDecimal thirteenthRate;

    @Schema(description = "211-240天数量")
    private Integer fourteenthInterval;

    @Schema(description = "211-240天金额")
    private BigDecimal fourteenthAmount;

    @Schema(description = "211-240天占比")
    private BigDecimal fourteenthRate;

    @Schema(description = "241-270天数量")
    private Integer fifteenthInterval;

    @Schema(description = "241-270天金额")
    private BigDecimal fifteenthAmount;

    @Schema(description = "241-270天占比")
    private BigDecimal fifteenthRate;

    @Schema(description = "271-300天数量")
    private Integer sixteenthInterval;

    @Schema(description = "271-300天金额")
    private BigDecimal sixteenthAmount;

    @Schema(description = "271-300天占比")
    private BigDecimal sixteenthRate;

    @Schema(description = "301-330天数量")
    private Integer seventeenthInterval;

    @Schema(description = "301-330天金额")
    private BigDecimal seventeenthAmount;

    @Schema(description = "301-330天占比")
    private BigDecimal seventeenthRate;

    @Schema(description = "331-365天数量")
    private Integer eighteenthInterval;

    @Schema(description = "331-365天金额")
    private BigDecimal eighteenthAmount;

    @Schema(description = "331-365天占比")
    private BigDecimal eighteenthRate;

    @Schema(description = "1年-2年数量")
    private Integer eighthInterval;

    @Schema(description = "1年-2年金额")
    private BigDecimal eighthAmount;

    @Schema(description = "1年-2年占比")
    private BigDecimal eighthRate;

    @Schema(description = "2年以上数量")
    private Integer ninthInterval;

    @Schema(description = "2年以上金额")
    private BigDecimal ninthAmount;

    @Schema(description = "2年以上占比")
    private BigDecimal ninthRate;

    @Schema(description = "60天以上合计数量")
    private Integer tenthInterval;

    @Schema(description = "60天以上合计金额")
    private BigDecimal tenthAmount;

    @Schema(description = "60天以上合计占比")
    private BigDecimal tenthRate;

    @Schema(description = "90天以上合计数量")
    private Integer nineteenthInterval;

    @Schema(description = "90天以上合计金额")
    private BigDecimal nineteenthAmount;

    @Schema(description = "90天以上合计占比")
    private BigDecimal nineteenthRate;

    @Schema(description = "181天以上合计数量")
    private Integer sixthInterval;

    @Schema(description = "181天以上合计金额")
    private BigDecimal sixthAmount;

    @Schema(description = "181天以上合计占比")
    private BigDecimal sixthRate;

    @Schema(description = "121-180天合计数量")
    private Integer fifthInterval;

    @Schema(description = "121-180天合计金额")
    private BigDecimal fifthAmount;

    @Schema(description = "121-180天合计占比")
    private BigDecimal fifthRate;

    @Schema(description = "181天-1年合计数量")
    private Integer seventhInterval;

    @Schema(description = "181天-1年合计金额")
    private BigDecimal seventhAmount;

    @Schema(description = "181天-1年合计占比")
    private BigDecimal seventhRate;
}