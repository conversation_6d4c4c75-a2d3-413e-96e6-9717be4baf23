package com.tl.rms.order.domain.enums;

import lombok.Getter;

/**
 * 经营目标数据类型
 *
 * <AUTHOR>
 * @date 2025/5/21
 */
@Getter
public enum BusinessObjectivesDataTypeEnum {
    MONTHLY(1, "月度目标"),
    YEARLY(2, "年度目标");

    private final int value;
    private final String desc;

    BusinessObjectivesDataTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static BusinessObjectivesDataTypeEnum getByValue(int value) {
        for (BusinessObjectivesDataTypeEnum type : values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        return null;
    }


}
