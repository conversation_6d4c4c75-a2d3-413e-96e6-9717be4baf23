package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.DeliveryOrderInvoice;
import com.tl.rms.order.domain.vo.QMDeliveryOrderInvoiceVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

@Mapper
public interface DeliveryOrderInvoiceConverter {
    DeliveryOrderInvoiceConverter INSTANCE = Mappers.getMapper(DeliveryOrderInvoiceConverter.class);

    default DeliveryOrderInvoice fromQMDeliveryOrderInvoiceVo(QMDeliveryOrderInvoiceVo qmDeliveryOrderInvoiceVo) {
        if (qmDeliveryOrderInvoiceVo == null) {
            return null;
        }
        DeliveryOrderInvoice deliveryOrderInvoice = new DeliveryOrderInvoice();
        BeanUtils.copyProperties(qmDeliveryOrderInvoiceVo, deliveryOrderInvoice);
        LocalDateTime now = LocalDateTime.now();
        deliveryOrderInvoice.setCreateTime(now);
        deliveryOrderInvoice.setUpdateTime(now);
        return deliveryOrderInvoice;
    }

    // DeliveryOrderInvoiceVo toVo(DeliveryOrderInvoice po);
    //
    // DeliveryOrderInvoice toPo(DeliveryOrderInvoiceVo vo);
    //
    // List<DeliveryOrderInvoiceVo> toVoList(List<DeliveryOrderInvoice> poList);
    //
    // List<DeliveryOrderInvoice> toPoList(List<DeliveryOrderInvoiceVo> voList);
}