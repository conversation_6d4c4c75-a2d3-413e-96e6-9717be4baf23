package com.tl.rms.order.domain.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 吉客云 销售单 货品详情 货品发货批次明细
 *
 * <AUTHOR>
 * @date 2023/5/22
 */
@Data
public class JkyOrderGoodsDelivery implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 商品明细id
     */
    private Long subTradeId;

    /**
     * 订单id
     */
    private Long tradeId;

    /**
     * 规格id
     */
    private Long specId;

    /**
     * 货品id
     */
    private Long goodsId;

    /**
     * 数量
     */
    private BigDecimal sendCount;

    /**
     * 生产日期
     */
    private String productionDate;

    /**
     * 到期日期
     */
    private String expirationDate;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 更新人
     */
    private String modifiedBy;

}