package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.EnumInfoVo;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 吉客云交易状态枚举
 */
@Getter
public enum JkyTradeStatusEnum {

    // 待审核状态
    PENDING_AUDIT("1010", "待审核"),
    AUDITING("1020", "审核中"),
    PRE_SALE("1030", "预售"),
    PENDING_REVIEW("1050", "待复核"),

    // 备货状态
    STOCKING_WAITING("2000", "备货等待"),
    STOCKING_REPLENISHMENT("2010", "备货等待等补货"),
    SERVICE_WAITING("2020", "服务等待"),
    STOCKING_PRODUCTION("2030", "备货等待等生产"),
    PROCUREMENT_WAITING("2040", "采购等待"),

    // 发货状态
    VIRTUAL_DELIVERY("3010", "虚拟发货"),
    PENDING_DELIVERY_WAITING("4110", "待发货待递交"),
    PENDING_DELIVERY_SUBMITTING("4111", "待发货递交中"),
    PENDING_DELIVERY_SUBMITTED("4112", "待发货已递交"),
    PENDING_DELIVERY_SUBMIT_FAILED("4113", "待发货-递交失败"),
    PENDING_DELIVERY_CANCELING("4121", "待发货-取消中"),
    PENDING_DELIVERY_CANCELED("4122", "待发货已取消"),
    PENDING_DELIVERY_CANCEL_FAILED("4123", "待发货取消失败"),
    PENDING_DELIVERY_PARTIAL("4130", "待发货部分发货"),
    CONSIGNMENT_WAITING_SUBMIT("4040", "代销发货待递交"),
    CONSIGNMENT_SUBMITTED("4041", "代销发货已递交"),

    // 取消状态
    CANCELED("5010", "已取消"),
    CANCELED_MERGED("5020", "已取消被合并"),
    CANCELED_SPLIT("5030", "已取消被拆分"),

    // 发货状态
    IN_TRANSIT("6000", "发货在途"),

    // 完成状态
    COMPLETED("9090", "已完成");

    private final String value;
    private final String label;

    JkyTradeStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static String getLabelByValue(String value) {
        return Arrays.stream(JkyTradeStatusEnum.values()).filter(status -> status.getValue().equals(value))
                .findFirst().map(JkyTradeStatusEnum::getLabel).orElse(null);
    }

    public static List<EnumInfoVo> list() {
        return Arrays.stream(JkyTradeStatusEnum.values()).map(
                level -> new EnumInfoVo(level.getValue(), level.getLabel())).collect(Collectors.toList());
    }
}
