package com.tl.rms.order.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "物料及物料可用库存查询响应对象")
public class Material4StockAvailableQuantityVo extends MaterialVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 4461445606782652108L;

    @Schema(description = "可用库存数量")
    private Integer availableQuantity;
}
