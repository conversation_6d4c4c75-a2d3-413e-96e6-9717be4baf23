package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.PriceHistory;
import com.tl.rms.order.domain.vo.PriceHistoryVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PriceHistoryConverter {

    PriceHistoryConverter MAPPER = Mappers.getMapper(PriceHistoryConverter.class);

    PriceHistoryVo toVo(PriceHistory po);

    PriceHistory toPo(PriceHistoryVo vo);

    List<PriceHistoryVo> toVoList(List<PriceHistory> poList);

    List<PriceHistory> toPoList(List<PriceHistoryVo> voList);
}