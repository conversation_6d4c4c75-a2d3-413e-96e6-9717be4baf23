package com.tl.rms.order.domain.request;

import com.tl.rms.common.model.PageParamsVo;
import lombok.Data;

/**
 * 店铺销售任务列表查询参数实体
 *
 * <AUTHOR>
 * @date 2025/7/25
 */
@Data
public class DistributionShopPageRequestVo extends PageParamsVo {

    // 店铺唯一主键，必传
    private Long shopId;

    // 产业
    private String industryId;

    // 品类
    private String categoryId;

    // 批次状态
    private String status;

    // 创建人
    private Long createBy;

    // 分货批次号
    private String distributionNo;

}
