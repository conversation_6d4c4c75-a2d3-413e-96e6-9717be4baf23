package com.tl.rms.order.domain.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 吉客云 销售单 序列号
 *
 * <AUTHOR>
 * @date 2023/5/22
 */
@Data
public class JkyOrderGoodsSerial implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 订单id
     */
    private Long tradeId;

    /**
     * 子订单号
     */
    private Long subTradeId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 序列号
     */
    private String serialNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 更新人
     */
    private String modifiedBy;

}