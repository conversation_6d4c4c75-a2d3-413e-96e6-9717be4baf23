package com.tl.rms.order.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 经营目标vo
 *
 * <AUTHOR>
 * @date 2025/5/22
 */
@Schema(description="经营目标更新")
@Data
public class BusinessObjectivesCompleteUpdateVo  implements Serializable {

    /**
     * 头部数据
     */
    private BusinessObjectivesUpdateVo updateVo;

    /**
     * 行数据
     */
    private List<BusinessObjectivesLineVo> lineDataList;
}
