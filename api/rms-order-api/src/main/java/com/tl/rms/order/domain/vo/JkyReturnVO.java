package com.tl.rms.order.domain.vo;

import com.tl.rms.order.domain.po.JkyReturn;
import com.tl.rms.order.domain.po.JkyReturnPay;
import com.tl.rms.order.domain.po.JkyReturnReceiver;
import lombok.Data;

import java.util.List;

@Data
public class JkyReturnVO extends JkyReturn {

    private Integer delete;

    private String probleamDesc;

    /**
     * 补发销售单
     */
    private List<String> sendTradeNo;

    /**
     * 退货销售单
     */
    private List<String> returnTradeNo;

    /**
     * 货品详情
     */
    private List<JkyReturnGoodsVO> returnChangeGoodsDetail;

    /**
     * 结算信息
     */
    private JkyReturnPay returnChangePay;

    /**
     * 收件人信息
     */
    private JkyReturnReceiver returnChangeReceiver;

}