package com.tl.rms.order.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 调整分货单确认分货前提示请求参数vo
 *
 * <AUTHOR>
 * @date 2025/7/25
 */
@Data
public class AdjustDistributionConfirmRequestVo {
    @Schema(description = "分货单行ID")
    private Long distributionLineId;

    @Schema(description = "分货单行预留库存")
    private Integer reservedQuantity;

    @Schema(description = "分货单行商店数据")
    private List<AdjustDistributionConfirmShopRequestVo> confirmShopList;
}
