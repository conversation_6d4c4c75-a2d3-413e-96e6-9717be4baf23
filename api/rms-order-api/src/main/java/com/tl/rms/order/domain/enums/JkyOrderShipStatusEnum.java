package com.tl.rms.order.domain.enums;

import lombok.extern.slf4j.Slf4j;

/**
 * 吉客云订单物流信息行状态
 *
 * <AUTHOR>
 * @date 2023/7/14
 */
@Slf4j
public enum JkyOrderShipStatusEnum {

    REMOVED(0, "REMOVED"),//行取消
    OUTBOUND(1, "OUTBOUND"),//出库
    SIGN(2, "SIGN"),//签收
    INBOUND(3, "INBOUND");//入库

    private int code;
    private String value;

    JkyOrderShipStatusEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static int getCodeByValue(String value) {
        for (JkyOrderShipStatusEnum enu : JkyOrderShipStatusEnum.values()) {
            if (enu.getValue().equals(value)) {
                return enu.getCode();
            }
        }
        log.warn("枚举JkyOrderShipStatusEnum中无:{}", value);
        return -1;
    }

    /**
     * 取流程靠后的状态
     */
    public static String max(String value1, String value2) {
        if (value1 != null && value1.equals(value2)) {
            return value1;
        }

        int code1 = getCodeByValue(value1);
        int code2 = getCodeByValue(value2);
        return code1 > code2 ? value1 : value2;
    }
}
