package com.tl.rms.order.domain.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 吉客云 退换补货单货品信息
 *
 * <AUTHOR>
 * @date 2023/5/31
 */
@Data
public class JkyReturnGoods implements Serializable {
    /**
     * 货品id
     */
    private Long subTradeId;

    /**
     * 售后订单id
     */
    private Long tradeAfterId;

    /**
     * 货品编号id
     */
    private Long goodsId;

    /**
     * 货品编号
     */
    private String goodsNo;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 规格id
     */
    private Long specId;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 单位
     */
    private String unit;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 销售单数量
     */
    private Integer sellCount;

    /**
     * 售后原因描述
     */
    private String reasonDesc;

    /**
     * 退货数量
     */
    private Integer returnCount;

    /**
     * 退货金额
     */
    private BigDecimal returnFee;

    /**
     * 发货数量
     */
    private Integer sendCount;

    /**
     * 退货优惠
     */
    private BigDecimal returnDiscounts;

    /**
     * 发货优惠
     */
    private BigDecimal sendDiscounts;

    /**
     * 发货金额
     */
    private BigDecimal sendFee;

    /**
     * 应退金额
     */
    private BigDecimal shouldReturnFee;

    /**
     * 备注
     */
    private String remark;

    /**
     * 货品业务特性(1-定制生产（转生产后发货）,2-虚拟物品/服务（无仓储作业）,3-卡券（结算冲抵）,4-代销（转供应商发货）,5-预售品（转预售单）,6-序列号管理（单品跟踪）,7-需上门安装（发货后转工单）)
     */
    private Integer goodsAttribute;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 退换单创建时间
     */
    private Date gmtCreate;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 金额
     */
    private BigDecimal sellTotal;

    /**
     * 实退数量
     */
    private Integer deliveryCount;

    /**
     * 组合装标记 1-是组合装 0-不是组合装
     */
    private Integer isFit;

    /**
     * 赠品标记 1-是赠品 0-不是赠品
     */
    private Integer isGift;

    /**
     * 分摊后退货金额
     */
    private BigDecimal shareReturnFee;

    /**
     * 分摊后发货金额
     */
    private BigDecimal shareSendFee;

    /**
     * 分摊后应退金额
     */
    private BigDecimal shareShouldReturnFee;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 更新人
     */
    private String modifiedBy;

}