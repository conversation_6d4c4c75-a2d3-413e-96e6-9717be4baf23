package com.tl.rms.order.domain.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 库存汇总查询响应结果vo
 *
 * <AUTHOR>
 * @date 2025/8/13
 */
@Data
public class RetailInventoryTotalVo {

    @Schema(description = "总库存")
    private Integer totalInventory;

    // -----
    @Schema(description = "实物库存")
    private Integer physicalInventory;

    @Schema(description = "良品")
    private Integer stockQty;

    @Schema(description = "不良品")
    private Integer damageQty;

    @Schema(description = "盒损")
    private Integer boxLossInventory;

    @Schema(description = "售后")
    private Integer afterSalesQty;

    @Schema(description = "演示退机")
    private Integer ysjInventory;

    // -----
    @Schema(description = "非实物库存")
    private Integer nonPhysicalInventory;

    @Schema(description = "借机")
    private Integer checkInQty;

    @Schema(description = "铺货")
    private Integer phInventory;

    // -----
    @Schema(description = "在途库存")
    private Integer intransitInventory;

    @Schema(description = "采购在途")
    private Integer buyIntransit;

    @Schema(description = "销售退在途")
    private Integer returnIntransit;

    @Schema(description = "跨仓调拨在途")
    private Integer switchBackInTransit;

    @Schema(description = "还机入在途")
    private Integer sendBackIntransit;
}
