package com.tl.rms.order.mybatis;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import com.tl.rms.common.utils.AbstractCommonEnumUtils;
import com.tl.rms.order.domain.enums.EnableEnum;
import com.tl.rms.order.domain.enums.JkyOrderAuditStrategyTypeEnum;

/**
 * <AUTHOR>
 */
@MappedTypes({JkyOrderAuditStrategyTypeEnum.class})
public class JkyOrderAuditStrategyTypeHandler implements TypeHandler<JkyOrderAuditStrategyTypeEnum> {

    @Override
    public void setParameter(PreparedStatement ps, int i, JkyOrderAuditStrategyTypeEnum parameter, JdbcType jdbcType) throws SQLException {
        if (parameter != null) {
            ps.setInt(i, parameter.getCode());
        }
    }

    @Override
    public JkyOrderAuditStrategyTypeEnum getResult(ResultSet rs, String columnName) throws SQLException {
        Integer code = rs.getInt(columnName);
        if (rs.wasNull()) {
            return null;
        }
        return AbstractCommonEnumUtils.getEnum(JkyOrderAuditStrategyTypeEnum.class, code);
    }

    @Override
    public JkyOrderAuditStrategyTypeEnum getResult(ResultSet rs, int columnIndex) throws SQLException {
        Integer code = rs.getInt(columnIndex);
        if (rs.wasNull()) {
            return null;
        }
        return AbstractCommonEnumUtils.getEnum(JkyOrderAuditStrategyTypeEnum.class, code);
    }

    @Override
    public JkyOrderAuditStrategyTypeEnum getResult(CallableStatement cs, int columnIndex) throws SQLException {
        Integer code = cs.getInt(columnIndex);
        if (cs.wasNull()) {
            return null;
        }
        return AbstractCommonEnumUtils.getEnum(JkyOrderAuditStrategyTypeEnum.class, code);
    }
}
