package com.tl.rms.order.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 分货销售统计-物料/国标码维度响应vo
 * <AUTHOR>
 */
@Data
public class DistributionSalesStatisticsRespVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // 物料编码或国标码
    private String code;

    // 店铺编码(销售单中店铺编码可以对应上)
    private String shopCode;

    // 销售数量
    private BigDecimal sellCount;
}