package com.tl.rms.order.domain.enums;

/**
 * 吉客云销售单 订单类型
 *
 * <AUTHOR>
 * @date 2023/6/9
 */
public enum JkyOrderTradeTypeEnum {

    /**
     * 吉客云销售单 订单类型
     */
    TYPE_1(1, "零售业务"),
    TYPE_2(2, "代发货(来自分销商)"),
    TYPE_3(3, "预售订单"),
    TYPE_4(4, "周期性订购"),
    TYPE_5(5, "代销售(供货商发货)"),
    TYPE_6(6, "现款现货"),
    TYPE_7(7, "售后发货"),
    TYPE_8(8, "售后退货"),
    TYPE_9(9, "批发业务(B2B)"),
    TYPE_10(10, "试销业务"),
    TYPE_11(11, "错漏调整"),
    TYPE_12(12, "仅退款"),
    TYPE_13(13, "销售返利"),
    ;

    private Integer type;
    private String desc;

    JkyOrderTradeTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getDesc(Integer type) {
        for (JkyOrderTradeTypeEnum typeEnum : JkyOrderTradeTypeEnum.values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum.getDesc();
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}
