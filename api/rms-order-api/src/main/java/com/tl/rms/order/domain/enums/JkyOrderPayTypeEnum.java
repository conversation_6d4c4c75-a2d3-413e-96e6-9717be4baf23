package com.tl.rms.order.domain.enums;

/**
 * 吉客云销售单 支付方式
 *
 * <AUTHOR>
 * @date 2023/6/9
 */
public enum JkyOrderPayTypeEnum {

    /**
     * 吉客云销售单 支付方式
     */
    TYPE_1(1, "支付宝"),
    TYPE_2(2, "财付通"),
    TYPE_3(3, "微信支付"),
    TYPE_4(4, "银联支付"),
    TYPE_5(5, "盛付通"),
    TYPE_6(6, "其它"),
    TYPE_7(7, "现金"),
    TYPE_8(8, "储值卡"),
    TYPE_9(9, "扫码付"),
    TYPE_10(10, "挂账"),
    TYPE_11(11, "诺诺支付"),
    TYPE_16(16, "易付宝"),
    TYPE_27(27, "通联支付"),
    TYPE_32(32, "有赞支付"),
    TYPE_33(33, "汇付支付"),
    TYPE_35(35, "商盟支付"),
    TYPE_36(36, "易宝支付"),
    TYPE_37(37, "汇聚支付"),
    TYPE_38(38, "合利宝支付"),
    ;

    private Integer type;
    private String desc;

    JkyOrderPayTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getDesc(Integer type) {
        for (JkyOrderPayTypeEnum typeEnum : JkyOrderPayTypeEnum.values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum.getDesc();
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}
