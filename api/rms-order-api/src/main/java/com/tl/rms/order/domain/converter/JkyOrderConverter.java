package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.JkyOrder;
import com.tl.rms.order.domain.vo.JkyOrderVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface JkyOrderConverter {

    JkyOrderConverter MAPPER = Mappers.getMapper(JkyOrderConverter.class);

    JkyOrderVo toVo(JkyOrder po);

    JkyOrder toPo(JkyOrderVo vo);

    List<JkyOrderVo> toVoList(List<JkyOrder> poList);

    List<JkyOrder> toPoList(List<JkyOrderVo> voList);
}