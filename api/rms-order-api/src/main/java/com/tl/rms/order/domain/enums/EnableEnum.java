package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.BaseEnum;

/**
 * <AUTHOR>
 */
public enum EnableEnum implements BaseEnum<Integer> {
    DISABLED(0, "禁用"), ENABLED(1, "启用");

    private final Integer code;
    private final String desc;

    EnableEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
