package com.tl.rms.order.domain.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 吉客云 退换补货单结算信息
 *
 * <AUTHOR>
 * @date 2023/5/31
 */
@Data
public class JkyReturnPay implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 售后id
     */
    private Long tradeAfterId;

    /**
     * 退货金额
     */
    private BigDecimal returnAccounts;

    /**
     * 发货金额
     */
    private BigDecimal sendAccounts;

    /**
     * 应收邮资
     */
    private BigDecimal receivePostFee;

    /**
     * 应退合计
     */
    private BigDecimal returnTotal;

    /**
     * 结算币种
     */
    private String settlementCurrency;

    /**
     * 优惠
     */
    private BigDecimal discountFee;

    /**
     * 付款账户
     */
    private String payAccount;

    /**
     * 客户开户行名称
     */
    private String customerBankName;

    /**
     * 付款状态 0 未付款 1 部分付款 2 已付款
     */
    private Integer payStatus;

    /**
     * 付款金额
     */
    private BigDecimal payAccounts;

    /**
     * 付款时间
     */
    private Date payTime;

    /**
     * 付款人
     */
    private String payee;

    /**
     * 收款人账户类型 (1-现金,2-银行,3-支付宝,4-微信)
     */
    private Integer customerAccountType;

    /**
     * 客户收款账号
     */
    private String customerBankAccount;

    /**
     * 补发类型 (1-先入库后补发,2-先补发后入库)
     */
    private Integer resendType;

    /**
     * 结算类型(2001-先结算后入库,2002-先入库后结算)
     */
    private String settlementType;

    /**
     * 退款方式(1-网店平台退款,2-转账支付,3-转预存款)
     */
    private String refundType;

    /**
     * 退款方式id(1-网店平台退款,2-转账支付,3-转客户账户余额)
     */
    private Integer refundTypeCode;

    /**
     * 结算人
     */
    private String settlementPerson;

    /**
     * 结算人id
     */
    private Long settlementPersonId;

    /**
     * 结算时间
     */
    private Date settlementString;

    /**
     * 退换单创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 结算币种code
     */
    private String settlementCurrencyCode;

    /**
     * 付款账户id
     */
    private Long payAccountId;

    /**
     * 付款申请单号
     */
    private String paymentNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 更新人
     */
    private String modifiedBy;

}