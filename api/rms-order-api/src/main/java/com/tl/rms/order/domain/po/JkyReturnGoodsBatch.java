package com.tl.rms.order.domain.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 吉客云 退换补货单货品批次信息
 *
 * <AUTHOR>
 * @date 2023/5/31
 */
@Data
public class JkyReturnGoodsBatch implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 货品id
     */
    private Long subTradeId;

    /**
     * 售后订单id
     */
    private Long tradeAfterId;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 批次数量
     */
    private Integer batchAmount;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 到期日期
     */
    private Date expirationDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 更新人
     */
    private String modifiedBy;

}