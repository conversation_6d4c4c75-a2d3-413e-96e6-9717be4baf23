package com.tl.rms.order.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 分货创建行vo
 *
 * <AUTHOR>
 * @date 2025/7/22
 */
@Data
@Schema(description = "分货创建行vo")
public class DistributionLineCreateRequestVo {
    @Schema(description = "分货单行ID")
    private Long distributionLineId;

    @Schema(description="国标码")
    private String gbCode;

    @Schema(description="物料编码")
    private String materialCode;

    @Schema(description="仓库名称")
    private String warehouseNames;

    @Schema(description="可用库存")
    private Integer availableQuantity;

    @Schema(description="预留库存")
    private Integer reservedQuantity;

    @Schema(description="分货单行商店")
    private List<DistributionLineShopCreateRequestVo> distributionLineShopList;
}
