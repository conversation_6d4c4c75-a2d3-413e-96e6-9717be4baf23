package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.PromotionActivityLine;
import com.tl.rms.order.domain.vo.PromotionActivityLineVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PromotionActivityLineConverter {

    PromotionActivityLineConverter MAPPER = Mappers.getMapper(PromotionActivityLineConverter.class);

    PromotionActivityLineVo toVo(PromotionActivityLine po);

    PromotionActivityLine toPo(PromotionActivityLineVo vo);

    List<PromotionActivityLineVo> toVoList(List<PromotionActivityLine> poList);

    List<PromotionActivityLine> toPoList(List<PromotionActivityLineVo> voList);

}