package com.tl.rms.order.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class JkyOrderEbsVO {

    private String tradeId;

    /**
     * 订单编号
     */
    private String tradeNo;

    /**
     * 订单类型
     */
    private Integer tradeType;

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 客户编码
     */
    private String customerId;

    /**
     * 分公司名称
     */
    private String branchName;

    /**
     * 国补金额
     */
    private String govSubsidyAmount;

    /**
     * 商家承担国补金额
     */
    private String govSubsidyAmountMerchant;

    /**
     * 省份
     */
    private String state;

    /**
     * 标记名称
     */
    private String flagNames;

    /**
     * 寄售客户编码或名称
     */
    private String consignCode;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 净应收金额
     */
    private String ebsAmount;

    /**
     * 采购折扣点位
     */
    private BigDecimal purchaseDiscountRate;

    /**
     * 售后来源单号
     */
    private String sourceAfterNo;

    /**
     * 网店订单号
     */
    private String onlineTradeNo;

    /**
     * 退货原单号
     */
    private String returnSourceTradeNo;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 货品详情
     */
    private List<JkyOrderGoodsEbsVO> goodsDetail;

}