package com.tl.rms.order.domain.vo;

import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import lombok.Data;

/**
 * 奇门退货入库单行项目集合VO
 * <AUTHOR>
 */
@Data
@XmlAccessorType(jakarta.xml.bind.annotation.XmlAccessType.FIELD)
public class QMReturnOrderLinesVo {
    
    @XmlElement(name = "orderLine")
    private List<QMReturnOrderLineVo> orderLines;
}
