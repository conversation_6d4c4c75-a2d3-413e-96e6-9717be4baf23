package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.PromotionActivity;
import com.tl.rms.order.domain.vo.PromotionActivityUpdateVo;
import com.tl.rms.order.domain.vo.PromotionActivityVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PromotionActivityConverter {

    PromotionActivityConverter MAPPER = Mappers.getMapper(PromotionActivityConverter.class);

    PromotionActivityVo toVo(PromotionActivity po);

    PromotionActivity toPo(PromotionActivityVo vo);

    PromotionActivity toPo(PromotionActivityUpdateVo vo);

    List<PromotionActivityVo> toVoList(List<PromotionActivity> poList);

    List<PromotionActivity> toPoList(List<PromotionActivityVo> voList);
}