package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.BaseEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum ExpressCompanyEnum implements BaseEnum<String> {
    EMS("EMS", "EMS"), SHENTONG("申通快递", "申通快递"), DANGDANG("当当宅配", "当当宅配"), JD("京东配送", "京东配送"), GUOTONG("国通", "国通"),
    KUAJIE("快捷", "快捷"), QUANFENG("全峰", "全峰"), YOUSU("优速", "优速"), ZHONGTONG("中通快递", "中通快递"), YUANTONG("圆通", "圆通"),
    YUNDA("韵达", "韵达"), DEBANG_LOGISTICS("德邦物流", "德邦物流"), BAISHI_HUITONG("百世汇通", "百世汇通"), BAISHI_KUAIDI("百世快运", "百世快运"),
    POST("邮政小包", "邮政小包"), DEBANG_EXPRESS("德邦快递", "德邦快递"), DEBANG_KUAIYUN("德邦快运", "德邦快运"), RIRISHUN("日日顺", "日日顺"),
    OTHER("其他", "其他"), ZHAIJISONG("宅急送", "宅急送"), TIANTIAN("天天快递", "天天快递"), SF("顺丰速运", "顺丰速运");

    private final String code; // 现在与desc同值（中文）
    private final String desc;

    ExpressCompanyEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据中文code获取枚举
     */
    public static ExpressCompanyEnum getByCode(String code) {
        for (ExpressCompanyEnum company : values()) {
            if (company.code.equals(code)) { // 精确匹配中文
                return company;
            }
        }
        return OTHER;
    }

    /**
     * 根据中文desc获取枚举
     */
    public static ExpressCompanyEnum getByDesc(String desc) {
        for (ExpressCompanyEnum company : values()) {
            if (company.desc.equals(desc)) {
                return company;
            }
        }
        return OTHER;
    }
}
