package com.tl.rms.order.mybatis;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import com.tl.rms.common.utils.AbstractCommonEnumUtils;
import com.tl.rms.order.domain.enums.AuditStrategyRelationShipEnum;
import org.apache.ibatis.type.*;

/**
 * <AUTHOR>
 */
@MappedTypes({AuditStrategyRelationShipEnum.class})
public class AuditStrategyRelationShipEnumTypeHandler implements TypeHandler<AuditStrategyRelationShipEnum> {

    @Override
    public void setParameter(PreparedStatement ps, int i, AuditStrategyRelationShipEnum parameter, JdbcType jdbcType)
        throws SQLException {
        if (parameter != null) {
            ps.setInt(i, parameter.getCode());
        }
    }

    @Override
    public AuditStrategyRelationShipEnum getResult(ResultSet rs, String columnName) throws SQLException {
        Integer code = rs.getInt(columnName);
        if (rs.wasNull()) {
            return null;
        }
        return AbstractCommonEnumUtils.getEnum(AuditStrategyRelationShipEnum.class, code);
    }

    @Override
    public AuditStrategyRelationShipEnum getResult(ResultSet rs, int columnIndex) throws SQLException {
        Integer code = rs.getInt(columnIndex);
        if (rs.wasNull()) {
            return null;
        }
        return AbstractCommonEnumUtils.getEnum(AuditStrategyRelationShipEnum.class, code);
    }

    @Override
    public AuditStrategyRelationShipEnum getResult(CallableStatement cs, int columnIndex) throws SQLException {
        Integer code = cs.getInt(columnIndex);
        if (cs.wasNull()) {
            return null;
        }
        return AbstractCommonEnumUtils.getEnum(AuditStrategyRelationShipEnum.class, code);
    }
}
