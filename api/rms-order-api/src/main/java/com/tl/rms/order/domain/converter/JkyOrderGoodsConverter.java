package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.JkyOrderGoods;
import com.tl.rms.order.domain.vo.JkyOrderGoodsVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface JkyOrderGoodsConverter {

    JkyOrderGoodsConverter MAPPER = Mappers.getMapper(JkyOrderGoodsConverter.class);

    JkyOrderGoodsVo toVo(JkyOrderGoods po);

    JkyOrderGoods toPo(JkyOrderGoodsVo vo);

    List<JkyOrderGoodsVo> toVoList(List<JkyOrderGoods> poList);

    List<JkyOrderGoods> toPoList(List<JkyOrderGoodsVo> voList);
}