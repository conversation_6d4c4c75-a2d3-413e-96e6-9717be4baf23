package com.tl.rms.order.domain.vo;

import com.tl.rms.order.domain.po.ConditionItem;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class JkyOrderAutoAuditStrategySaveVo {
    private Long id;
    @NotBlank(message = "策略名称不能为空")
    private String strategyName;
    @NotNull(message = "策略类型不能为空")
    private Integer strategyType;
    private String shops;
    @NotNull(message = "策略有效时间不能为空")
    private LocalDateTime startTime;
    @NotNull(message = "策略有效时间不能为空")
    private LocalDateTime endTime;
    @NotNull(message = "策略优先级不能为空")
    private Integer priority;
    @NotNull(message = "状态不能为空")
    private Integer status;
    @NotNull(message = "订单触发条件设置不能为空")
    private Integer triggerRelationType;
    @NotEmpty(message = "条件项配置不能为空")
    private List<ConditionItem> triggerConditionItems;
    private List<ConditionItem> excludeConditionItems;
    private String operator;
}
