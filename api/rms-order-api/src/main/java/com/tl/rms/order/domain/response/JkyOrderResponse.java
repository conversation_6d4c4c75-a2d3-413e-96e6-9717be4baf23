package com.tl.rms.order.domain.response;

import com.tl.rms.order.domain.vo.JkyOrderSyncVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 吉客云 销售单接口返回业务数据
 *
 * <AUTHOR>
 * @date 2023/5/22
 */
@Data
public class JkyOrderResponse implements Serializable {

    /**
     * 仅当hasTotal为1是返回，要求翻页过程中不取总数
     */
    private Integer totalResults;

    /**
     * 销售单
     */
    private List<JkyOrderSyncVO> trades;

}