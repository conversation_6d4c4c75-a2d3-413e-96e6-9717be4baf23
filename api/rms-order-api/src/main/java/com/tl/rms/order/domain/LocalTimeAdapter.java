package com.tl.rms.order.domain;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

import jakarta.xml.bind.annotation.adapters.XmlAdapter;

/**
 * <AUTHOR>
 */
public class LocalTimeAdapter extends XmlAdapter<String, LocalTime> {

    // 定义日期时间格式，推荐使用 ISO-8601 格式，例如：2024-06-01T10:00:00
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    @Override
    public LocalTime unmarshal(String v) throws Exception {
        if (v == null || v.trim().isEmpty()) {
            return null;
        }
        return LocalTime.parse(v, FORMATTER);
    }

    @Override
    public String marshal(LocalTime v) throws Exception {
        if (v == null) {
            return null;
        }
        return v.format(FORMATTER);
    }
}
