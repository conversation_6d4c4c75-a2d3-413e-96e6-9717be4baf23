package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.DeliveryOrderSender;
import com.tl.rms.order.domain.vo.QMDeliveryOrderSenderVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

@Mapper
public interface DeliveryOrderSenderConverter {
    DeliveryOrderSenderConverter INSTANCE = Mappers.getMapper(DeliveryOrderSenderConverter.class);

    default DeliveryOrderSender fromQMDeliveryOrderSenderVo(QMDeliveryOrderSenderVo senderInfo) {
        if (senderInfo == null) {
            return null;
        }
        DeliveryOrderSender deliveryOrderSender = new DeliveryOrderSender();
        BeanUtils.copyProperties(senderInfo, deliveryOrderSender);
        LocalDateTime now = LocalDateTime.now();
        deliveryOrderSender.setCreateTime(now);
        deliveryOrderSender.setUpdateTime(now);
        return deliveryOrderSender;
    }

    // DeliveryOrderSenderVo toVo(DeliveryOrderSender po);
    //
    // DeliveryOrderSender toPo(DeliveryOrderSenderVo vo);
    //
    // List<DeliveryOrderSenderVo> toVoList(List<DeliveryOrderSender> poList);
    //
    // List<DeliveryOrderSender> toPoList(List<DeliveryOrderSenderVo> voList);
}