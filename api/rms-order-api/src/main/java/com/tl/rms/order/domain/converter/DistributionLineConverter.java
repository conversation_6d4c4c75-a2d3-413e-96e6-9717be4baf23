package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.DistributionLine;
import com.tl.rms.order.domain.vo.DistributionLineVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface DistributionLineConverter {

    DistributionLineConverter MAPPER = Mappers.getMapper(DistributionLineConverter.class);

    DistributionLineVo toVo(DistributionLine po);

    DistributionLine toPo(DistributionLineVo vo);

    List<DistributionLineVo> toVoList(List<DistributionLine> poList);

    List<DistributionLine> toPoList(List<DistributionLineVo> voList);
}