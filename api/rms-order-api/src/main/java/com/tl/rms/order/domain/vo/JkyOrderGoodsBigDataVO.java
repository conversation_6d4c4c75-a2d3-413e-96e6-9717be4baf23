package com.tl.rms.order.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 吉客云销售单同步大数据
 *
 * <AUTHOR>
 * @date 2023/5/30
 */
@Data
public class JkyOrderGoodsBigDataVO {

    /**
     * 订单行编码
     */
    private Long lineId;

    /**
     * 订单编号
     */
    private String tradeNo;

    /**
     * 订单状态 1010待审核,1020审核中,1030预售,1050待复核,2000备货等待,2010备货等待等补货,2020服务等待,2030备货等待等生产,2040采购等待,3010虚拟发货,4110待发货待递交,4111待发货递交中,4112待发货已递交,4113待发货-递交失败,4121待发货-取消中,4122待发货已取消,4123待发货取消失败,4130待发货部分发货,4040代销发货待递交,4041代销发货已递交,5010已取消,5020已取消被合并,5030已取消被拆分,6000发货在途,9090已完成
     * add: 10000 已取消-驳回审核
     */
    private Integer tradeStatus;

    /**
     * 订单状态
     */
    private String tradeStatusStr;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 订单类型 1:零售业务,2:代发货(来自分销商),3:预售订单,4:周期性订购,5:代销售(供货商发货),6:现款现货,7:售后发货,8:售后退货,9:批发业务(B2B),10:试销业务,11:错漏调整,12:仅退款,13:销售返利,91:自定义1,92:自定义2,93:自定义3...100:自定义10
     */
    private Integer tradeType;

    /**
     * 订单类型
     */
    private String tradeTypeStr;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 物流名称
     */
    private String logisticName;

    /**
     * 物流单号
     */
    private String mainPostid;

    /**
     * 网店订单号
     */
    private String onlineTradeNo;

    /**
     * 发货时间
     */
    private Date consignTime;

    /**
     * 应收合计（本币）
     */
    private BigDecimal localPayment;

    /**
     * 订单总数量
     */
    private Long tradeCount;

    /**
     * 货品摘要
     */
    private String goodslist;

    /**
     * 客户账号
     */
    private String customerAccount;

    /**
     * 备注
     */
    private String sellerMemo;

    /**
     * 买家备注
     */
    private String buyerMemo;

    /**
     * 物料编码
     */
    private String matCode;

    /**
     * 货品编号
     */
    private String goodsNo;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 数量
     */
    private BigDecimal sellCount;

    /**
     * 单价
     */
    private BigDecimal sellPrice;

    /**
     * 优惠金额-货品
     */
    private BigDecimal discountTotal;

    /**
     * 折扣
     */
    private BigDecimal customerDiscount;

    /**
     * 应收邮资
     */
    private BigDecimal receivedPostFee;

    /**
     * 优惠金额-订单
     */
    private BigDecimal discountFee;

    /**
     * 应收金额
     */
    private BigDecimal payment;

    /**
     * 平台优惠
     */
    private BigDecimal couponFee;

    /**
     * 已收金额
     */
    private BigDecimal receivedTotal;

    /**
     * 赠品标记
     */
    private Integer isGift;

    /**
     * 实际价格-分摊后金额
     */
    private BigDecimal shareFavourableAfterFee;

    /**
     * 应收价格-总金额
     */
    private BigDecimal sellTotal;

    /**
     * 支付方式 1.支付宝 2.财付通 3.微信支付 4.银联支付 5.盛付通 6.其它 7.现金 8.储值卡 9.扫码付 10.挂账 11.诺诺支付 16.易付宝 32.有赞支付 33.汇付支付 35.商盟支付 36.易宝支付 37.汇聚支付 38.合利宝支付 27.通联支付
     */
    private Integer payType;

    /**
     * 支付方式
     */
    private String payTypeStr;

    /**
     * 平台主播id
     */
    private String platAuthorId;

    /**
     * 平台主播名称
     */
    private String platAuthorName;

    /**
     * 售后来源单号
     */
    private String sourceAfterNo;

    /**
     * 客户类型
     */
    private String customerTypeName;

    /**
     * 客户等级
     */
    private String customerGradeName;

    /**
     * 客户标签
     */
    private String customerTags;

    /**
     * 客户编号
     */
    private String customerCode;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 标记名称
     */
    private String flagNames;

    /**
     * 出库数量
     */
    private Long outQty = 0L;

    /**
     * 签收数量
     */
    private Long signQty = 0L;

    /**
     * 入库数量
     */
    private Long inQty = 0L;

    /**
     * 出库时间
     */
    private String outDate = "";

    /**
     * 签收时间
     */
    private String signDate = "";

    /**
     * 入库时间
     */
    private String inDate = "";

    /**
     * 行状态
     */
    private String lineStatus;

    /**
     * 是否生成对账单
     */
    private Integer isGenBill = 0;

}
