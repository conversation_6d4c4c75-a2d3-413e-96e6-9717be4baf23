package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.BaseEnum;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum JkyOnlineOrderGoodsRefundStatusEnum implements BaseEnum<String> {
    /**
     * 未申请退款
     */
    NO_REFUND("未申请退款", "NO_REFUND"),
    /**
     * 退款创建
     */
    REFUND_CREATED("退款创建", "CREATED"),
    /**
     * 退款成功
     */
    REFUND_SUCCESS("退款成功", "SUCCESS"),
    /**
     * 退款关闭
     */
    REFUND_CLOSED("退款关闭", "CLOSED");

    private final String desc;
    private final String code;

}
