package com.tl.rms.order.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分货创建行商店vo
 *
 * <AUTHOR>
 * @date 2025/7/22
 */
@Data
@Schema(description = "分货创建行商店vo")
public class DistributionLineShopCreateRequestVo {
    @Schema(description = "分货单行商店ID")
    private Long distributionLineShopId;

    @Schema(description="店铺编码(销售单中店铺编码可以对应上)")
    private String shopCode;

    // 店铺ID
    private Long shopId;

    // 店铺名称
    private String shopName;

    @Schema(description="当期分货数量")
    private Integer currentDistributionQuantity;

}
