package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.JkyOrderGoods;
import com.tl.rms.order.domain.po.JkyOrderGoodsOrigin;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface JkyOrderGoodsOriginConverter {

    JkyOrderGoodsOriginConverter MAPPER = Mappers.getMapper(JkyOrderGoodsOriginConverter.class);

    JkyOrderGoodsOrigin toPo(JkyOrderGoods vo);

    List<JkyOrderGoodsOrigin> toPoList(List<JkyOrderGoods> voList);
}