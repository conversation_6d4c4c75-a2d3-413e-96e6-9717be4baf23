package com.tl.rms.order.domain.enums;

/**
 * 吉客云销售单 订单状态
 *
 * <AUTHOR>
 * @date 2023/6/9
 */
public enum JkyOrderTradeStatusEnum {

    /**
     * 吉客云销售单 订单状态
     */
    STATUS_1010(1010, "待审核"),
    STATUS_1020(1020, "审核中"),
    STATUS_1030(1030, "预售"),
    STATUS_1050(1050, "待复核"),
    STATUS_2000(2000, "备货等待"),
    STATUS_2010(2010, "备货等待等补货"),
    STATUS_2020(2020, "服务等待"),
    STATUS_2030(2030, "备货等待等生产"),
    STATUS_2040(2040, "采购等待"),
    STATUS_3010(3010, "虚拟发货"),
    STATUS_4110(4110, "待发货待递交"),
    STATUS_4111(4111, "待发货递交中"),
    STATUS_4112(4112, "待发货已递交"),
    STATUS_4113(4113, "待发货-递交失败"),
    STATUS_4121(4121, "待发货-取消中"),
    STATUS_4122(4122, "待发货已取消"),
    STATUS_4123(4123, "待发货取消失败"),
    STATUS_4130(4130, "待发货部分发货"),
    STATUS_4040(4040, "代销发货待递交"),
    STATUS_4041(4041, "代销发货已递交"),
    STATUS_5010(5010, "已取消"),
    STATUS_5020(5020, "已取消被合并"),
    STATUS_5030(5030, "已取消被拆分"),
    STATUS_6000(6000, "发货在途"),
    STATUS_9090(9090, "已完成"),

    /**
     * 大数据大屏业务需要，新增状态：已取消-驳回审核
     */
    STATUS_10000(10000, "已取消-驳回审核"),
    ;

    JkyOrderTradeStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    private Integer status;

    private String desc;

    public static String getDesc(Integer status) {
        for (JkyOrderTradeStatusEnum statusEnum : JkyOrderTradeStatusEnum.values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum.getDesc();
            }
        }
        return null;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

}
