package com.tl.rms.order.domain.response;

import lombok.Data;

/**
 * 分货单调整行页面展示实体
 */
@Data
public class DistributionAdjustLinePageVo {

    private Long id;

    /**
     * 品类
     */
    private String category;

    /**
     * 项目
     */
    private String project;

    /**
     * 产品系列
     */
    private String productSeriesName;

    /**
     * 机型
     */
    private String model;

    /**
     * 颜色
     */
    private String color;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 国标码
     */
    private String gbCode;

    /**
     * ITEM码
     */
    private String itemCode;

    /**
     * 分货批次号
     */
    private String distributionNo;

    /**
     * 调整单号
     */
    private String adjustNo;

}
