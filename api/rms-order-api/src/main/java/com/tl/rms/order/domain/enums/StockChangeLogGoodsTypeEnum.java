package com.tl.rms.order.domain.enums;

import lombok.Getter;

/**
 * 库存变动日志库存属性
 *
 * <AUTHOR>
 * @date 2025/8/13
 */
@Getter
public enum StockChangeLogGoodsTypeEnum {
    /**
     * 货品属性
     * OK=良品, BOXLOSS=盒损, DAMAGE=不良品, DEMO BACK=演示机退, AFTER SALES=售后
     */
    OK("OK", "良品"),
    BOXLOSS("BOXLOSS", "盒损"),
    DAMAGE("DAMAGE", "不良品"),
    DEMO_BACK("DEMO BACK", "演示机退"),
    AFTER_SALES("AFTER SALES", "售后");

    private final String value;
    private final String desc;

    StockChangeLogGoodsTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static StockChangeLogGoodsTypeEnum getByValue(String value) {
        for (StockChangeLogGoodsTypeEnum item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}
