package com.tl.rms.order.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 分货单调整行页面展示头部信息实体
 */
@Data
public class DistributionAdjustLineHeaderVo {

    // 分货批次号
    private String distributionNo;

    // 生效时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING)
    private Date effectTime;

    private Long createBy;

    // 创建人
    private String createByName;

    // 产业
    private String industry;

    // 分货类型 MATERIAL-物料 SKU-SKU
    private String type;

    // 调整单号
    private String adjustNo;

}
