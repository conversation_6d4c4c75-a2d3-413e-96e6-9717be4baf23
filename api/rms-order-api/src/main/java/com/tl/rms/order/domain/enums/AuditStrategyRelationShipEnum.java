package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.BaseEnum;

/**
 * <AUTHOR>
 */
public enum AuditStrategyRelationShipEnum implements BaseEnum<Integer> {
    ALL(0, "全部订单"), AND(1, "并且"), OR(2, "或者");

    private final Integer code;
    private final String desc;

    AuditStrategyRelationShipEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
