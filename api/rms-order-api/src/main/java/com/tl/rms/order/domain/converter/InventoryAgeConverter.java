package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.InventoryAge;
import com.tl.rms.order.domain.vo.InventoryAgeExportVo;
import com.tl.rms.order.domain.vo.InventoryAgeVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface InventoryAgeConverter {

    InventoryAgeConverter MAPPER = Mappers.getMapper(InventoryAgeConverter.class);

    InventoryAgeVo toVo(InventoryAge po);

    InventoryAge toPo(InventoryAgeVo vo);

    List<InventoryAgeVo> toVoList(List<InventoryAge> poList);

    List<InventoryAge> toPoList(List<InventoryAgeVo> voList);

    InventoryAgeExportVo toExportVo(InventoryAgeVo vo);

    List<InventoryAgeExportVo> toExportVoList(List<InventoryAgeVo> voList);
}