package com.tl.rms.order.domain.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 分货单调整行页面展示实体
 */
@Data
public class DistributionShopPageVo {

    private Long id;

    // 产业
    private String industry;

    // 品类
    private String category;

    // 分货批次号
    private String distributionNo;

    // 生效时间
    private Date effectiveTime;

    // 失效时间
    private Date invalidTime;

    private String status;

    // 批次状态 PENDING-待创建 INVALID-已失效 VALID-生效中
    private String statusName;

    // 创建人ID
    private Long createBy;

    // 创建人名称
    private String createByName;

    // 更新时间
    private Date updateTime;

}
