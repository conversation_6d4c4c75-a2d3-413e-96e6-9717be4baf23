package com.tl.rms.order.domain.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tl.rms.order.domain.enums.DistributionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 延用上批分货单vo
 *
 * <AUTHOR>
 * @date 2025/7/22
 */
@Data
@Schema(description = "延用上批分货单vo")
public class PreviousDistributionByIndustryIdVo {
    @Schema(description = "分货单ID")
    private Long distributionId;

    @Schema(description = "分货单批次号")
    private String distributionNo;

    @Schema(description = "分货单产业id")
    private String industryId;

    /**
     * 分货单类型
     * @see DistributionTypeEnum#SKU
     * @see DistributionTypeEnum#MATERIAL
     */
    @Schema(description = "分货单类型")
    private String distributionType;

    @Schema(description = "分货单行")
    Page<DistributionLineVo> distributionLineVoPage;
}
