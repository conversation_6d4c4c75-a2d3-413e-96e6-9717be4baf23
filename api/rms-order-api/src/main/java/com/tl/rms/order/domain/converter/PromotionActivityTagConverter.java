package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.PromotionActivityTag;
import com.tl.rms.order.domain.vo.PromotionActivityTagSaveVo;
import com.tl.rms.order.domain.vo.PromotionActivityTagVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PromotionActivityTagConverter {

    PromotionActivityTagConverter MAPPER = Mappers.getMapper(PromotionActivityTagConverter.class);

    PromotionActivityTagVo toVo(PromotionActivityTag po);

    PromotionActivityTag toPo(PromotionActivityTagVo vo);

    PromotionActivityTag toPo(PromotionActivityTagSaveVo vo);

    List<PromotionActivityTagVo> toVoList(List<PromotionActivityTag> poList);

    List<PromotionActivityTag> toPoList(List<PromotionActivityTagVo> voList);
}