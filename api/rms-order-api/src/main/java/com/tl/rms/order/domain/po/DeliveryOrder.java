package com.tl.rms.order.domain.po;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

import com.baomidou.mybatisplus.annotation.*;

import jakarta.xml.bind.annotation.XmlAccessorType;
import lombok.Data;

/**
 * 出库单主表
 */
@XmlAccessorType(jakarta.xml.bind.annotation.XmlAccessType.FIELD)
@Data
@TableName("t_delivery_order")
public class DeliveryOrder {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 出库单号
     */
    @TableField("delivery_order_code")
    private String deliveryOrderCode;

    /**
     * 原出库单号（ERP 分配）
     */
    @TableField("pre_delivery_order_code")
    private String preDeliveryOrderCode;

    /**
     * 原出库单号（WMS 分配）
     */
    @TableField("pre_delivery_order_id")
    private String preDeliveryOrderId;

    /**
     * 出库单类型 JYCK=一般交易出库单, HHCK=换货出库单, BFCK=补发出库单，QTCK=其他出库单
     */
    @TableField("order_type")
    private String orderType;

    /**
     * 仓库编码
     */
    @TableField("warehouse_code")
    private String warehouseCode;

    /**
     * 订单来源平台编码 TB=淘宝、TM=天猫、JD=京东等
     */
    @TableField("source_platform_code")
    private String sourcePlatformCode;

    /**
     * 订单来源平台名称
     */
    @TableField("source_platform_name")
    private String sourcePlatformName;

    /**
     * 发货单创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 最晚发货时间
     */
    @TableField("latest_delivery_time")
    private LocalDateTime latestDeliveryTime;

    /**
     * 最晚揽收时间
     */
    @TableField("latest_collection_time")
    private LocalDateTime latestCollectionTime;

    /**
     * 前台订单创建时间(下单时间)
     */
    @TableField("place_order_time")
    private LocalDateTime placeOrderTime;

    /**
     * 订单支付时间
     */
    @TableField("pay_time")
    private LocalDateTime payTime;

    /**
     * 支付平台交易号
     */
    @TableField("pay_no")
    private String payNo;

    /**
     * 服务编码 NCWLJH=集包
     */
    @TableField("service_code")
    private String serviceCode;

    /**
     * 操作员(审核员)编码
     */
    @TableField("operator_code")
    private String operatorCode;

    /**
     * 操作员(审核员)名称
     */
    @TableField("operator_name")
    private String operatorName;

    /**
     * 操作(审核)时间
     */
    @TableField("operate_time")
    private LocalDateTime operateTime;

    /**
     * 店铺名称
     */
    @TableField("shop_nick")
    private String shopNick;

    /**
     * 卖家名称
     */
    @TableField("seller_nick")
    private String sellerNick;

    /**
     * 买家昵称
     */
    @TableField("buyer_nick")
    private String buyerNick;

    /**
     * 订单总金额(元)
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 商品总金额(元)
     */
    @TableField("item_amount")
    private BigDecimal itemAmount;

    /**
     * 订单折扣金额(元)
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 快递费用(元)
     */
    @TableField("freight")
    private BigDecimal freight;

    /**
     * 应收金额(元)
     */
    @TableField("ar_amount")
    private BigDecimal arAmount;

    /**
     * 已收金额(元)
     */
    @TableField("got_amount")
    private BigDecimal gotAmount;

    /**
     * COD服务费
     */
    @TableField("service_fee")
    private BigDecimal serviceFee;

    /**
     * 物流公司编码 SF=顺丰、EMS=标准快递等
     */
    @TableField("logistics_code")
    private String logisticsCode;

    /**
     * 物流公司名称
     */
    @TableField("logistics_name")
    private String logisticsName;

    /**
     * 运单号
     */
    @TableField("express_code")
    private String expressCode;

    /**
     * 快递区域编码,大头笔信息
     */
    @TableField("logistics_area_code")
    private String logisticsAreaCode;

    /**
     * 投递时延要求 1=工作日,2=节假日,101=当日达,102=次晨达,103=次日达,104=预约达
     */
    @TableField("schedule_type")
    private Integer scheduleType;

    /**
     * 要求送达日期
     */
    @TableField("schedule_day")
    private LocalDate scheduleDay;

    /**
     * 投递时间范围要求(开始时间)
     */
    @TableField("schedule_start_time")
    private LocalTime scheduleStartTime;

    /**
     * 投递时间范围要求(结束时间)
     */
    @TableField("schedule_end_time")
    private LocalTime scheduleEndTime;

    /**
     * 发货服务类型 PTPS（普通配送），LLPS（冷链配送），HBP(环保配)
     */
    @TableField("delivery_type")
    private String deliveryType;

    /**
     * 是否紧急 Y/N
     */
    @TableField("is_urgency")
    private String isUrgency;

    /**
     * 是否需要发票 Y/N
     */
    @TableField("invoice_flag")
    private String invoiceFlag;

    /**
     * 是否需要保险 Y/N
     */
    @TableField("insurance_flag")
    private String insuranceFlag;

    /**
     * 买家留言
     */
    @TableField("buyer_message")
    private String buyerMessage;

    /**
     * 卖家留言
     */
    @TableField("seller_message")
    private String sellerMessage;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 更新人
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;
}