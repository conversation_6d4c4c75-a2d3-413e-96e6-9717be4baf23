package com.tl.rms.order.domain.vo;

import java.time.LocalDateTime;
import java.util.List;

import com.tl.rms.order.domain.po.ConditionItem;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class JkyOrderAutoAuditStrategyDetailVo {

    private Long id;
    /**
     * 策略名称（手动输入）
     */
    private String strategyName;
    /**
     * 策略类型（0 销售单自动审核）
     */
    private Integer strategyType;
    private List<String> shops;
    /**
     * 条件组合关系（枚举：0 全部订单 1 满足所有(AND,组内) 2 满足任意(OR,组内)）
     */
    private Integer triggerRelationType;
    private List<ConditionItem> triggerConditionItems;
    private List<ConditionItem> excludeConditionItems;

    /**
     * 策略有效期开始时间
     */
    private LocalDateTime startTime;

    /**
     * 策略有效期结束时间
     */
    private LocalDateTime endTime;

    /**
     * 优先级（数字越大优先级最高，支持正整数，如 0,1,2...）
     */
    private Integer priority;

    /**
     * 启用状态（枚举：1 启用 0 禁用，默认 1）
     */
    private Integer status;
}
