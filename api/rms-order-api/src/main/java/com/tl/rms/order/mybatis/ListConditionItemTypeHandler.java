package com.tl.rms.order.mybatis;

import java.util.List;

import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tl.rms.order.domain.enums.JkyOrderAuditStrategyTypeEnum;
import com.tl.rms.order.domain.po.ConditionItem;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

/**
 * <AUTHOR>
 */
@MappedTypes({List.class})
@MappedJdbcTypes({JdbcType.VARCHAR})
public class ListConditionItemTypeHandler extends AbstractJsonTypeHandler<List<ConditionItem>> {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    // 定义类型引用，用于 Jackson 反序列化
    private static final TypeReference<List<ConditionItem>> TYPE_REFERENCE =
        new TypeReference<List<ConditionItem>>() {};

    @Override
    protected List<ConditionItem> parse(String json) {
        try {
            return OBJECT_MAPPER.readValue(json, TYPE_REFERENCE);
        } catch (Exception e) {
            throw new RuntimeException("JSON 反序列化为 List<ConditionItem>失败: " + json, e);
        }
    }

    @Override
    protected String toJson(List<ConditionItem> obj) {
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            throw new RuntimeException("List<ConditionItem>序列化为 JSON 失败 ", e);
        }
    }
}
