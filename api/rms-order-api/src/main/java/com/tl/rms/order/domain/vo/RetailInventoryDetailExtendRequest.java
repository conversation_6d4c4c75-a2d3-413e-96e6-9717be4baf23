package com.tl.rms.order.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 库存明细接口请求参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "库存明细接口请求参数")
public class RetailInventoryDetailExtendRequest {

    @Schema(description = "项目，选填，精确匹配")
    private String projectName;

    @Schema(description = "产业，选填，精确匹配")
    private String industryName;

    @Schema(description = "品类，选填，精确匹配")
    private String categoryName;

    @Schema(description = "产品系列，选填，精确匹配")
    private String productSeriesName;

    @Schema(description = "产品名称，选填，模糊匹配")
    private String productName;

    @Schema(description = "机型，选填，模糊匹配")
    private String modelName;

    @Schema(description = "物料编码，多选，格式：编码1:::编码2")
    private String materialId;

    @Schema(description = "物料编码模糊搜索")
    private String materialIdLike;

    @Schema(description = "物料描述，选填，模糊匹配")
    private String materialDesc;

    @Schema(description = "国标码，多选，格式：编码1:::编码2")
    private String gbCode;

    @Schema(description = "页码，从1开始")
    private Integer page = 1;

    @Schema(description = "每页大小")
    private Integer size = 10;
}
