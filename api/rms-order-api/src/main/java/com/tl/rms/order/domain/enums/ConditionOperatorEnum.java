package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.BaseEnum;

/**
 * <AUTHOR>
 */
public enum ConditionOperatorEnum implements BaseEnum<Integer> {
    IN(0, "包含"), NOT_IN(1, "不包含"), EQ(2, "等于"), NE(3, "不等于"), GT(4, "大于"), LT(5, "小于");

    ConditionOperatorEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final Integer code;
    private final String desc;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
