package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.IndustryCategory;
import com.tl.rms.order.domain.vo.IndustryCategoryVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface IndustryCategoryConverter {

    IndustryCategoryConverter MAPPER = Mappers.getMapper(IndustryCategoryConverter.class);

    IndustryCategoryVo toVo(IndustryCategory po);

    IndustryCategory toPo(IndustryCategoryVo vo);

    List<IndustryCategoryVo> toVoList(List<IndustryCategory> poList);

    List<IndustryCategory> toPoList(List<IndustryCategoryVo> voList);

}