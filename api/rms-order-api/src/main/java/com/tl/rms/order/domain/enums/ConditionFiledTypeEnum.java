package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.BaseEnum;

/**
 * <AUTHOR>
 */
public enum ConditionFiledTypeEnum implements BaseEnum<Integer> {
    REGION(0, "收货区域"),
    WAREHOUSE(1, "发货仓库"),
    ORDER_SOURCE(2, "订单来源"),
    PAYMENT_TIME(3, "付款时间"),
    HAS_REMARK(4, "有备注"),
    ORDER_FLAG(5, "订单标记"),
    GOODS(6, "货品"),
    GOODS_CATEGORY(7, "品类");

    ConditionFiledTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final Integer code;
    private final String desc;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
