package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.DistributionLineShop;
import com.tl.rms.order.domain.vo.DistributionLineShopVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface DistributionLineShopConverter {

    DistributionLineShopConverter MAPPER = Mappers.getMapper(DistributionLineShopConverter.class);

    DistributionLineShopVo toVo(DistributionLineShop po);

    DistributionLineShop toPo(DistributionLineShopVo vo);

    List<DistributionLineShopVo> toVoList(List<DistributionLineShop> poList);

    List<DistributionLineShop> toPoList(List<DistributionLineShopVo> voList);
}