package com.tl.rms.order.domain.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "销售任务调整日志导出对象")
public class DistributionAdjustLineExportVo {

    public static final String EXPORT_EXCEL_NAME = "销售任务调整日志";

    @ExcelProperty(value = "品类")
    private String category;

    @ExcelProperty(value = "项目")
    private String project;

    @ExcelProperty(value = "产品系列")
    private String productSeriesName;

    @ExcelProperty(value = "机型")
    private String model;

    @ExcelProperty(value = "颜色")
    private String color;

    @ExcelProperty(value = "物料编码")
    private String materialCode;

    @ExcelProperty(value = "物料名称")
    private String materialName;

    @ExcelProperty(value = "国标码")
    private String gbCode;

    @ExcelProperty(value = "item码")
    private String itemCode;

    @ExcelProperty(value = "店铺")
    private String shopName;

    @ExcelProperty(value = "当期分货")
    private Integer distributionQuantity;

}
