package com.tl.rms.order.domain.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 销售单自定义字段
 * fields添加columnExt
 *
 * <AUTHOR>
 * @date 2025/4/1
 */
@Getter
@Setter
public class JkyOrderExtendVO implements Serializable {

    /**
     * 自定义字段1
     */
    private String customizeTradeColumn1;

    /**
     * 自定义字段5
     */
    private String customizeTradeColumn5;

    /**
     * 国补金额-多多
     */
    private String CustomizeTradeColumn7;

    /**
     * 国补金额-抖音
     */
    private String CustomizeTradeColumn8;

    /**
     * 国补金额-天猫
     */
    private String CustomizeTradeColumn9;

    /**
     * 国补金额-京东
     */
    private String CustomizeTradeColumn10;

    /**
     * 销售公司-抖音（寄售客户）
     */
    private String CustomizeTradeColumn11;

    /**
     * 公司主体-天猫（寄售客户）
     */
    private String CustomizeTradeColumn12;

    /**
     * 政府承担国补金额-抖音
     */
    private String CustomizeTradeColumn13;

    /**
     * 商家承担国补金额-抖音
     */
    private String CustomizeTradeColumn14;

}