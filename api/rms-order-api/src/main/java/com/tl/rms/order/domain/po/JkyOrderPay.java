package com.tl.rms.order.domain.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 吉客云 销售单 订单支付详情
 *
 * <AUTHOR>
 * @date 2023/5/22
 */
@Data
public class JkyOrderPay implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 订单id
     */
    private Long tradeId;

    /**
     * 结算方式：1.担保交易2.银行收款3.现金收款4.货到付款5.欠款计应收6.客户预存款7.多种结算8.退换货冲抵9.电子钱包
     */
    private Integer chargeType;

    /**
     * 结算币种
     */
    private String chargeCurrency;

    /**
     * 收款帐户
     */
    private String chargeAccount;

    /**
     * 收款账户名称
     */
    private String accountName;

    /**
     * 支付方式：1,支付宝 2,财付通 3,微信支付 4,银联支付 5,盛付通 6,其它 7,现金 8,储值卡 9,扫码付 10,挂账 11,诺诺支付 16,易付宝 32,有赞支付 33,汇付支付 35,商盟支付 36,易宝支付 37,汇聚支付 38,合利宝支付 27,通联支付
     */
    private Integer payType;

    /**
     * 支付单号
     */
    private String payNo;

    /**
     * 支付金额
     */
    private BigDecimal payment;

    /**
     * 结算币种编码
     */
    private String chargeCurrencyCode;

    /**
     * 结算汇率
     */
    private BigDecimal chargeExchangeRate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 更新人
     */
    private String modifiedBy;

}