package com.tl.rms.order.domain.constant;

import java.math.BigDecimal;
import java.util.regex.Pattern;

public class Constants {

    /**
     * 默认发货仓Id
     */
    public static final String DEFAULT_WAREHOUSE_ID = "01012239";

    /**
     * 默认发货仓名称
     */
    public static final String DEFAULT_WAREHOUSE_NAME = "总部深圳联运通仓";

    /**
     * 公用 数据逻辑删除标识 0-未删除 1-已删除
     */
    public static final int LOGIC_NOT_DELETE = 0;
    public static final int LOGIC_DELETE = 1;

    /**
     * 公用(使用到0或1的 皆可使用) 是否  0-否(false) 1-是(true)
     */
    public static final int PUBLIC_NO = 0;
    public static final int PUBLIC_YES = 1;

    /**
     * 小数点 两位 匹配模式
     */
    public static final Pattern DECIMAL_PATTERN = Pattern.compile("^\\d+(\\.\\d{1,4})?$");

    /**
     * 万元转元 进率
     */
    public static final BigDecimal TEN_THOUSAND = new BigDecimal("10000");

    public static final BigDecimal HUNDRED = new BigDecimal("100");

}