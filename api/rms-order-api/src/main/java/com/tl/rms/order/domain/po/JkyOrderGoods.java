package com.tl.rms.order.domain.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("t_jky_order_goods")
public class JkyOrderGoods {

    /**
     * 商品明细id
     */
    @TableId("sub_trade_id")
    private Long subTradeId;

    /**
     * 销售单id
     */
    @TableField("trade_id")
    private Long tradeId;

    /**
     * 货品编号
     */
    @TableField("goods_no")
    private String goodsNo;

    /**
     * 货品名称
     */
    @TableField("goods_name")
    private String goodsName;

    /**
     * 规格名称
     */
    @TableField("spec_name")
    private String specName;

    /**
     * 条码
     */
    @TableField("barcode")
    private String barcode;

    /**
     * 数量
     */
    @TableField("sell_count")
    private BigDecimal sellCount;

    /**
     * 单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 单价
     */
    @TableField("sell_price")
    private BigDecimal sellPrice;

    /**
     * 总金额
     */
    @TableField("sell_total")
    private BigDecimal sellTotal;

    /**
     * 货品成本
     */
    @TableField("cost")
    private BigDecimal cost;

    /**
     * 优惠金额
     */
    @TableField("discount_total")
    private BigDecimal discountTotal;

    /**
     * 抵扣积分
     */
    @TableField("discount_point")
    private Integer discountPoint;

    /**
     * 税额
     */
    @TableField("tax_fee")
    private BigDecimal taxFee;

    /**
     * 分摊金额
     */
    @TableField("share_favourable_fee")
    private BigDecimal shareFavourableFee;

    /**
     * 预估重量
     */
    @TableField("estimate_weight")
    private BigDecimal estimateWeight;

    /**
     * 货品备注
     */
    @TableField("goods_memo")
    private String goodsMemo;

    /**
     * 货品类别
     */
    @TableField("cate_name")
    private String cateName;

    /**
     * 品牌
     */
    @TableField("brand_name")
    private String brandName;

    /**
     * 货品标签
     */
    @TableField("goods_tags")
    private String goodsTags;

    /**
     * 组合装标记
     */
    @TableField("is_fit")
    private Integer isFit;

    /**
     * 赠品标记
     */
    @TableField("is_gift")
    private Integer isGift;

    /**
     * 优惠
     */
    @TableField("discount_fee")
    private BigDecimal discountFee;

    /**
     * 税率
     */
    @TableField("tax_rate")
    private BigDecimal taxRate;

    /**
     * 预估体积（单个货品）
     */
    @TableField("estimate_goods_volume")
    private BigDecimal estimateGoodsVolume;

    /**
     * 是否预售货品标记1是0否
     */
    @TableField("is_presell")
    private Integer isPresell;

    /**
     * 终端销售单价
     */
    @TableField("customer_price")
    private BigDecimal customerPrice;

    /**
     * 终端销售金额
     */
    @TableField("customer_total")
    private BigDecimal customerTotal;

    /**
     * 交易编号
     */
    @TableField("trade_goods_no")
    private String tradeGoodsNo;

    /**
     * 交易名称
     */
    @TableField("trade_goods_name")
    private String tradeGoodsName;

    /**
     * 交易规格
     */
    @TableField("trade_goods_spec")
    private String tradeGoodsSpec;

    /**
     * 交易单位
     */
    @TableField("trade_goods_unit")
    private String tradeGoodsUnit;

    /**
     * 网店子订单号
     */
    @TableField("source_subtrade_no")
    private String sourceSubtradeNo;

    /**
     * 平台代码
     */
    @TableField("plat_code")
    private String platCode;

    /**
     * 平台商品id
     */
    @TableField("plat_goods_id")
    private String platGoodsId;

    /**
     * 平台主播id
     */
    @TableField("plat_author_id")
    private String platAuthorId;

    /**
     * 平台主播名称
     */
    @TableField("plat_author_name")
    private String platAuthorName;

    /**
     * 平台赠品标记
     */
    @TableField("is_plat_gift")
    private String isPlatGift;

    /**
     * 货品平台优惠
     */
    @TableField("goods_plat_discount_fee")
    private BigDecimal goodsPlatDiscountFee;

    /**
     * 分摊后金额
     */
    @TableField("share_favourable_after_fee")
    private BigDecimal shareFavourableAfterFee;

    /**
     * 实付金额
     */
    @TableField("divide_sell_total")
    private BigDecimal divideSellTotal;

    /**
     * 订单商品优惠分摊
     */
    @TableField("share_order_discount_fee")
    private BigDecimal shareOrderDiscountFee;

    /**
     * 订单平台优惠分摊
     */
    @TableField("share_order_plat_discount_fee")
    private BigDecimal shareOrderPlatDiscountFee;

    /**
     * 网店主订单号
     */
    @TableField("source_trade_no")
    private String sourceTradeNo;

    /**
     * 终端网店订单号
     */
    @TableField("customer_trade_no")
    private String customerTradeNo;

    /**
     * 实发数
     */
    @TableField("actual_send_count")
    private BigDecimal actualSendCount;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新时间
     */
    @TableField("modify_time")
    private LocalDateTime modifyTime;

    /**
     * 更新人
     */
    @TableField("modified_by")
    private String modifiedBy;
}