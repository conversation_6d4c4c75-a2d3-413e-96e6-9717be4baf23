package com.tl.rms.order.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 促销活动
 * <AUTHOR>
 */
@Schema(description="促销活动更新")
@Data
public class PromotionActivityCompleteUpdateVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private PromotionActivityUpdateVo updateVo;
    private List<PromotionActivityLineVo> shopDataList;

}