package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.BaseEnum;

/**
 * <AUTHOR>
 */
public enum ConditionValueTypeEnum implements BaseEnum<Integer> {

    INPUT(0, "值输入"), SELECT(1, "选择");

    ConditionValueTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final Integer code;
    private final String desc;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
