package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.DeliveryOrder;
import com.tl.rms.order.domain.vo.QMDeliveryOrderVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

@Mapper
public interface DeliveryOrderConverter {
    DeliveryOrderConverter INSTANCE = Mappers.getMapper(DeliveryOrderConverter.class);

    default DeliveryOrder fromQmDeliveryOrderVo(QMDeliveryOrderVo deliveryOrderVo) {
        if (deliveryOrderVo == null) {
            return null;
        }
        DeliveryOrder deliveryOrder = new DeliveryOrder();
        BeanUtils.copyProperties(deliveryOrderVo, deliveryOrder);
        deliveryOrder.setUpdateTime(LocalDateTime.now());
        return deliveryOrder;
    }
}