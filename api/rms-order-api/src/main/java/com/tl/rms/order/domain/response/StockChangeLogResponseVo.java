package com.tl.rms.order.domain.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 库存变动记录响应vo
 * <AUTHOR>
 */

@Data
public class StockChangeLogResponseVo {

    /**
     * 主键ID
     * 自增唯一标识，用于区分不同的出入库记录
     */
    private Long id;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 出入库单号
     */
    private String code;

    /**
     * 出入库类型
     * XTRK=销售退货, CGRK=采购入库, DBRK=跨项目调拨入库,
     * PYRK=盘盈入库, XSCK=销售出库, DBCK=跨项目调拨出库, PKCK=盘亏出库
     * @see com.tl.rms.order.domain.enums.StockChangeLogInventoryTypeEnums
     */
    private String type;

    /**
     * 出入库时间
     */
    private LocalDateTime handleTime;

    /**
     * 销售退货原因
     */
    private String reason;

    /**
     * 变更数量
     * 入库为正数，出库为负数
     */
    private Integer qty;

    /**
     * 货品属性
     * OK=良品, BOXLOSS=盒损, DAMAGE=不良品, DEMO BACK=演示机退, AFTER SALES=售后
     * @see com.tl.rms.order.domain.enums.StockChangeLogGoodsTypeEnum
     */
    private String goodsType;

    /**
     * 物流单号
     */
    private String trackingNumber;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 制单人
     */
    private String operator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private Long updateBy;

}