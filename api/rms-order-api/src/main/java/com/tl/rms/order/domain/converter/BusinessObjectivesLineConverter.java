package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.BusinessObjectivesLine;
import com.tl.rms.order.domain.vo.BusinessObjectivesLineVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BusinessObjectivesLineConverter {

    BusinessObjectivesLineConverter MAPPER = Mappers.getMapper(BusinessObjectivesLineConverter.class);

    BusinessObjectivesLineVo toVo(BusinessObjectivesLine po);

    BusinessObjectivesLine toPo(BusinessObjectivesLineVo vo);

    List<BusinessObjectivesLineVo> toVoList(List<BusinessObjectivesLine> poList);

    List<BusinessObjectivesLine> toPoList(List<BusinessObjectivesLineVo> voList);

}