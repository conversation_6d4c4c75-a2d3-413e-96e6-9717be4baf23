package com.tl.rms.order.domain.vo;

import jakarta.xml.bind.annotation.XmlAccessorType;
import lombok.Data;

/**
 * 奇门退货入库单主信息VO
 * <AUTHOR>
 */
@Data
@XmlAccessorType(jakarta.xml.bind.annotation.XmlAccessType.FIELD)
public class QMReturnOrderVo {
    
    /**
     * ERP 的退货入库单编码, string (50) , 必填
     */
    private String returnOrderCode;

    /**
     * 货主编码, string (50) , 必填
     */
    private String ownerCode;

    /**
     * 仓库编码, string (50)，必填
     */
    private String warehouseCode;

    /**
     * 单据类型, string (50)，THRK=退货入库，HHRK=换货入库
     */
    private String orderType;

    /**
     * 订单来源平台编码, string (50)
     */
    private String sourcePlatformCode;

    /**
     * 订单来源平台名称, string (200)
     */
    private String sourcePlatformName;

    /**
     * 店铺名称, string (200)
     */
    private String shopNick;

    /**
     * 卖家名称, string (200)
     */
    private String sellerNick;

    /**
     * 订单标记
     */
    private String orderFlag;

    /**
     * 原出库单号（ERP 分配）, string(50) ,必填
     */
    private String preDeliveryOrderCode;

    /**
     * 原出库单号（WMS 分配）, string (50)，条件必填
     */
    private String preDeliveryOrderId;

    /**
     * 物流公司编码, string (50) , 必填
     */
    private String logisticsCode;

    /**
     * 物流公司名称, string (200)
     */
    private String logisticsName;

    /**
     * 运单号, string (50)
     */
    private String expressCode;

    /**
     * 退货原因, string (200)
     */
    private String returnReason;

    /**
     * 买家昵称, string (50)
     */
    private String buyerNick;

    /**
     * 发件人信息
     */
    private QMReturnOrderSenderVo senderInfo;

    /**
     * 备注, string (500)
     */
    private String remark;
}
