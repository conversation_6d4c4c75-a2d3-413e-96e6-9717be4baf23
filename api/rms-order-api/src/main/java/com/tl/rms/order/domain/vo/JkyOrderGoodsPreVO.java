package com.tl.rms.order.domain.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 吉客云销售单同步大数据
 * 前置对象 解耦
 *
 * <AUTHOR>
 * @date 2024/6/24
 */
@Getter
@Setter
@ToString
public class JkyOrderGoodsPreVO {

    /**
     * 销售单号数据
     */
    private List<String> tradeNos;

    /**
     * 销售单号
     */
    private String tradeNo;

    /**
     * 物料编码
     */
    private String matCode;

}
