package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.EnumInfoVo;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分货状态枚举
 * <AUTHOR>
 */
@Getter
public enum DistributionStatusEnum {

    PENDING("PENDING", "待创建"),
    INVALID("INVALID", "已失效"),
    VALID("VALID", "生效中"),
    ;

    private final String value;
    private final String label;

    DistributionStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static String getLabelByValue(String value) {
        return Arrays.stream(DistributionStatusEnum.values()).filter(type -> type.getValue().equals(value))
                .findFirst().map(DistributionStatusEnum::getLabel).orElse(null);
    }

    public static List<EnumInfoVo> list() {
        return Arrays.stream(DistributionStatusEnum.values()).map(
                level -> new EnumInfoVo(level.getValue(), level.getLabel())).collect(Collectors.toList());
    }
}
