package com.tl.rms.order.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class JkyOrderGoodsEbsVO {

    /**
     * 条码
     */
    private String barcode;

    /**
     * 数量
     */
    private BigDecimal sellCount;

    /**
     * 单位
     */
    private String unit;

    /**
     * 分摊后金额
     */
    private BigDecimal shareFavourableAfterFee;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 终端网店订单号
     */
    private String customerTradeNo;

    /**
     * 机型
     */
    private String goodsName;

}
