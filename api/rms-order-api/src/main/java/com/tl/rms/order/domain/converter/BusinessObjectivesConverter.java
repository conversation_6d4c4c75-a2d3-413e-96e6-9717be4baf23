package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.BusinessObjectives;
import com.tl.rms.order.domain.vo.BusinessObjectivesUpdateVo;
import com.tl.rms.order.domain.vo.BusinessObjectivesVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BusinessObjectivesConverter {

    BusinessObjectivesConverter MAPPER = Mappers.getMapper(BusinessObjectivesConverter.class);

    BusinessObjectivesVo toVo(BusinessObjectives po);

    BusinessObjectives toPo(BusinessObjectivesVo vo);

    BusinessObjectives toPo(BusinessObjectivesUpdateVo vo);

    List<BusinessObjectivesVo> toVoList(List<BusinessObjectives> poList);

    List<BusinessObjectives> toPoList(List<BusinessObjectivesVo> voList);

}