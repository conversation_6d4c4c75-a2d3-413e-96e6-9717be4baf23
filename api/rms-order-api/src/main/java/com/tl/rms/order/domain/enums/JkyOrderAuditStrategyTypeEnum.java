package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.BaseEnum;

/**
 * <AUTHOR>
 */
public enum JkyOrderAuditStrategyTypeEnum implements BaseEnum<Integer> {

    AUTO(0, "销售单自动审核");

    private final Integer code;
    private final String desc;

    JkyOrderAuditStrategyTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
