package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.DeliveryOrderReceiver;
import com.tl.rms.order.domain.vo.QMDeliveryOrderReceiverVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

@Mapper
public interface DeliveryOrderReceiverConverter {
    DeliveryOrderReceiverConverter INSTANCE = Mappers.getMapper(DeliveryOrderReceiverConverter.class);

    default DeliveryOrderReceiver fromQMDeliveryOrderReceiverVo(QMDeliveryOrderReceiverVo receiverInfo) {
        if (receiverInfo == null) {
            return null;
        }
        DeliveryOrderReceiver deliveryOrderReceiver = new DeliveryOrderReceiver();
        BeanUtils.copyProperties(receiverInfo, deliveryOrderReceiver);
        LocalDateTime now = LocalDateTime.now();
        deliveryOrderReceiver.setCreateTime(now);
        deliveryOrderReceiver.setUpdateTime(now);
        return deliveryOrderReceiver;
    }

    // DeliveryOrderReceiverVo toVo(DeliveryOrderReceiver po);
    //
    // DeliveryOrderReceiver toPo(DeliveryOrderReceiverVo vo);
    //
    // List<DeliveryOrderReceiverVo> toVoList(List<DeliveryOrderReceiver> poList);
    //
    // List<DeliveryOrderReceiver> toPoList(List<DeliveryOrderReceiverVo> voList);
}