package com.tl.rms.order.domain.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 吉客云 销售单 其他应收
 *
 * <AUTHOR>
 * @date 2023/5/22
 */
@Data
public class JkyOrderOtherFee implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 订单id
     */
    private Long tradeId;

    /**
     * 费用
     */
    private BigDecimal expenseFee;

    /**
     * 费用名称
     */
    private String expenseItemName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 更新人
     */
    private String modifiedBy;

}