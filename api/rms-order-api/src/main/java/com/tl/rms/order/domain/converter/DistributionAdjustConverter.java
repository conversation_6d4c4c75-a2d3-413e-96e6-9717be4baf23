package com.tl.rms.order.domain.converter;

import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface DistributionAdjustConverter {

    DistributionAdjustConverter MAPPER = Mappers.getMapper(DistributionAdjustConverter.class);

}