package com.tl.rms.order.domain.enums;

import lombok.Getter;

/**
 * 库存变动日志库存类型
 *
 * <AUTHOR>
 * @date 2025/8/13
 */
@Getter
public enum StockChangeLogInventoryTypeEnums {
    /**
     * 出入库类型
     * 出入库类型:XTRK=销售退货,CGRK=采购入库,DBRK=跨项目调拨入库,QTRK=其他入库,
     * PYRK=盘盈入库,XSCK=销售出库,DBCK=跨项目调拨出库,QTCK=其他出库,PKCK=盘亏出库
     * 20250826 新增 (JJRK)借机拒签入库、(HJRK)还机入库、(JJCK)借机出库
     */
    XTRK("XTRK", "销售退货"),
    CGRK("CGRK", "采购入库"),
    DBRK("DBRK", "跨项目调拨入库"),
    QTRK("QTRK", "其他入库"),
    PYRK("PYRK", "盘盈入库"),
    XSCK("XSCK", "销售出库"),
    DBCK("DBCK", "跨项目调拨出库"),
    QTCK("QTCK", "其他出库"),
    PKCK("PKCK", "盘亏出库"),
    JJRK("JJRK", "借机拒签入库"),
    HJRK("HJRK", "还机入库"),
    JJCK("JJCK", "借机出库");

    private final String value;
    private final String desc;

    StockChangeLogInventoryTypeEnums(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static StockChangeLogInventoryTypeEnums getByValue(String value) {
        for (StockChangeLogInventoryTypeEnums item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}
