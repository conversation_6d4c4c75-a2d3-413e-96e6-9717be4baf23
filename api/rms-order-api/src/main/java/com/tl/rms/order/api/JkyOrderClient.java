package com.tl.rms.order.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tl.rms.common.model.EnumInfoVo;
import com.tl.rms.lib.beans.web.restful.ResponseMessage;
import com.tl.rms.order.domain.po.JkyOrder;
import com.tl.rms.order.domain.po.JkyOrderShip;
import com.tl.rms.order.domain.po.extend.JkyOrderShipExtend;
import com.tl.rms.order.domain.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

@FeignClient(contextId = "JkyOrderClient", value = "rms-order")
public interface JkyOrderClient {

    String PATH = "/jky/order";

    @GetMapping(PATH + "/flag/list")
    List<JkyOrderFlagVo> flagList();

    @GetMapping(PATH + "/trade/status/list")
    List<EnumInfoVo> tradeStatusList();

    @GetMapping(PATH + "/trade/type/list")
    List<EnumInfoVo> tradeTypeList();

    @GetMapping(PATH + "/trade/logistic/type/list")
    List<EnumInfoVo> tradeLogisticTypeList();

    @GetMapping(PATH + "/trade/from/list")
    List<EnumInfoVo> tradeFromList();

    @GetMapping(PATH + "/trade/pay/status/list")
    List<EnumInfoVo> tradePayStatusList();

    @PostMapping(PATH + "/page")
    Page<JkyOrderVo> page(@RequestBody JkyOrderQueryReqVo jkyOrderQueryReqVo);

    @GetMapping(PATH + "/{tradeId}")
    JkyOrderVo getByTradeId(@PathVariable("tradeId") Long tradeId);

    @GetMapping(PATH + "/distribution/sales/statistics/by/gbCode")
    List<DistributionSalesStatisticsRespVo> distributionSalesStatisticsByGbCode(DistributionSalesStatisticsGbCodeReqVo requestVo);

    @GetMapping(PATH + "/distribution/sales/statistics/by/materialCode")
    List<DistributionSalesStatisticsRespVo> distributionSalesStatisticsByMaterialCode(DistributionSalesStatisticsMaterialCodeReqVo requestVo);

    @PostMapping(PATH + "/batchSyncInternetShopOrderData")
    void batchSyncInternetShopOrderData(@RequestBody List<JkyOnlineOrderDataVo> jkyOnlineOrderDatumVos);

    @PostMapping(PATH + "/syncJkyOrder")
    ResponseMessage syncJkyOrder(@RequestBody List<JkyOrderSyncVO> orderVOs, @RequestParam("userId") String userId);

    @PostMapping(PATH + "/syncJkyOrderStatus")
    ResponseMessage syncJkyOrderStatus(@RequestBody List<JkyOrderSyncVO> orderVOs, @RequestParam("userId") String userId);

    @PostMapping(PATH + "/queryJkyOrderMaxAuditTime")
    ResponseMessage<String> queryJkyOrderMaxAuditTime(@RequestParam(value = "type", required = false) String type);

    @PostMapping(PATH + "/listTradeNo2Update")
    ResponseMessage<List<String>> listTradeNo2Update();

    @PostMapping(PATH + "/listTradeNoByDate")
    ResponseMessage<List<String>> listTradeNoByDate(@RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate);

    @PostMapping(PATH + "/queryJkyOrderEbsVOByTradeId")
    ResponseMessage<JkyOrderEbsVO> queryJkyOrderEbsVOByTradeId(@RequestParam("tradeId") Long tradeId);

    @PostMapping(PATH + "/queryJkyOrderGoodsEbsVOByTradeId")
    ResponseMessage<List<JkyOrderGoodsEbsVO>> queryJkyOrderGoodsEbsVOByTradeId(@RequestParam("tradeId") Long tradeId);

    @PostMapping(PATH + "/updateJkyOrderSendEbsTimeCustBranch")
    ResponseMessage updateJkyOrderSendEbsTimeCustBranch(@RequestBody JkyOrderEbsVO order);

    @PostMapping(PATH + "/updateJkyOrderSendEbsStatusError")
    ResponseMessage updateJkyOrderSendEbsStatusError(@RequestParam("tradeId") Long tradeId, @RequestParam("errorMsg") String errorMsg);

    @PostMapping(PATH + "/queryJkyOrderShopCodeByTradeNo")
    ResponseMessage<String> queryJkyOrderShopCodeByTradeNo(@RequestParam("tradeNo") String tradeNo);

    @PostMapping(PATH + "/queryJkyOrderByBillTradeNo")
    ResponseMessage<List<JkyOrder>> queryJkyOrderByBillTradeNo(@RequestParam("billTradeNo") String billTradeNo);

    @PostMapping(PATH + "/syncJkyReturn")
    ResponseMessage syncJkyReturn(@RequestBody List<JkyReturnVO> returnVOs, @RequestParam("userId") String userId);

    @PostMapping(PATH + "/queryJkyReturnMaxAuditTime")
    ResponseMessage<String> queryJkyReturnMaxAuditTime();

    @PostMapping(PATH + "/saveJkyOrderShip")
    ResponseMessage saveJkyOrderShip(@RequestBody List<JkyOrderShip> jkyOrderShips);

    @PostMapping(PATH + "/queryByOrderId")
    List<JkyOrderShipExtend> queryByOrderId(@RequestParam("orderId") String orderId);

    @PostMapping(PATH + "/updateOut2EbsStatus")
    void updateOut2EbsStatus(@RequestBody List<Long> ids);

    @PostMapping(PATH + "/updateSign2EbsStatus")
    void updateSign2EbsStatus(@RequestBody List<Long> ids);

    @PostMapping(PATH + "/updateIn2EbsStatus")
    void updateIn2EbsStatus(@RequestBody List<Long> ids);

    @PostMapping(PATH + "/updateCancelLine2EbsStatus")
    void updateCancelLine2EbsStatus(@RequestBody List<Long> ids);

    @PostMapping(PATH + "/getRateByShopAndModel")
    ResponseMessage<BigDecimal> getRateByShopAndModel(@RequestParam("shopCode") String shopCode, @RequestParam("modelName") String modelName);
}