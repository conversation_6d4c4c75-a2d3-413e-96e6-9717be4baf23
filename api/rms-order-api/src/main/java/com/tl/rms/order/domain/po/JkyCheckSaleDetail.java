package com.tl.rms.order.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;

public class JkyCheckSaleDetail implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 销售类型
     */
    private Integer salesType;

    /**
     * 销售编号
     */
    private String salesNo;

    /**
     * 销售金额
     */
    private BigDecimal salesAmount;

    /**
     * 销售数量
     */
    private Integer salesQuantity;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 货品编号
     */
    private String goodsNo;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 销售日期
     */
    private Long salesDate;

    /**
     * 规格名称
     */
    private String skuName;

    /**
     * 关联对账单id
     */
    private Long checkId;

    /**
     * t_jky_check_sale_detail
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     * @return id 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 销售类型
     * @return sales_type 销售类型
     */
    public Integer getSalesType() {
        return salesType;
    }

    /**
     * 销售类型
     * @param salesType 销售类型
     */
    public void setSalesType(Integer salesType) {
        this.salesType = salesType;
    }

    /**
     * 销售编号
     * @return sales_nos 销售编号
     */
    public String getSalesNo() {
        return salesNo;
    }

    /**
     * 销售编号
     * @param salesNos 销售编号
     */
    public void setSalesNo(String salesNo) {
        this.salesNo= salesNo== null ? null : salesNo.trim();
    }

    /**
     * 销售金额
     * @return sales_amount 销售金额
     */
    public BigDecimal getSalesAmount() {
        return salesAmount;
    }

    /**
     * 销售金额
     * @param salesAmount 销售金额
     */
    public void setSalesAmount(BigDecimal salesAmount) {
        this.salesAmount = salesAmount;
    }

    /**
     * 销售数量
     * @return sales_quantity 销售数量
     */
    public Integer getSalesQuantity() {
        return salesQuantity;
    }

    /**
     * 销售数量
     * @param salesQuantity 销售数量
     */
    public void setSalesQuantity(Integer salesQuantity) {
        this.salesQuantity = salesQuantity;
    }

    /**
     * 商品名称
     * @return goods_name 商品名称
     */
    public String getGoodsName() {
        return goodsName;
    }

    /**
     * 商品名称
     * @param goodsName 商品名称
     */
    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName == null ? null : goodsName.trim();
    }

    /**
     * 货品编号
     * @return goods_no 货品编号
     */
    public String getGoodsNo() {
        return goodsNo;
    }

    /**
     * 货品编号
     * @param goodsNo 货品编号
     */
    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo == null ? null : goodsNo.trim();
    }

    /**
     * 单位名称
     * @return unit_name 单位名称
     */
    public String getUnitName() {
        return unitName;
    }

    /**
     * 单位名称
     * @param unitName 单位名称
     */
    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    /**
     * 销售日期
     * @return sales_date 销售日期
     */
    public Long getSalesDate() {
        return salesDate;
    }

    /**
     * 销售日期
     * @param salesDate 销售日期
     */
    public void setSalesDate(Long salesDate) {
        this.salesDate = salesDate;
    }

    /**
     * 规格名称
     * @return sku_name 规格名称
     */
    public String getSkuName() {
        return skuName;
    }

    /**
     * 规格名称
     * @param skuName 规格名称
     */
    public void setSkuName(String skuName) {
        this.skuName = skuName == null ? null : skuName.trim();
    }

    /**
     * 关联对账单id
     * @return check_id 关联对账单id
     */
    public Long getCheckId() {
        return checkId;
    }

    /**
     * 关联对账单id
     * @param checkId 关联对账单id
     */
    public void setCheckId(Long checkId) {
        this.checkId = checkId;
    }
}