package com.tl.rms.order.domain.enums;

import com.google.common.collect.Lists;
import com.tl.rms.common.exception.CommonException;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
public enum ConditionFieldWithOperatorEnum {
    ONE(ConditionFiledTypeEnum.REGION, Lists.newArrayList(ConditionOperatorEnum.IN, ConditionOperatorEnum.NOT_IN)),
    TWO(ConditionFiledTypeEnum.WAREHOUSE,
        Lists.newArrayList(ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE, ConditionOperatorEnum.IN,
            ConditionOperatorEnum.NOT_IN)),
    THREE(ConditionFiledTypeEnum.ORDER_SOURCE,
        Lists.newArrayList(ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE, ConditionOperatorEnum.IN,
            ConditionOperatorEnum.NOT_IN)),
    FOUR(ConditionFiledTypeEnum.PAYMENT_TIME,
        Lists.newArrayList(ConditionOperatorEnum.EQ, ConditionOperatorEnum.NE, ConditionOperatorEnum.GT,
            ConditionOperatorEnum.LT)),
    FIVE(ConditionFiledTypeEnum.HAS_REMARK, Lists.newArrayList(ConditionOperatorEnum.IN, ConditionOperatorEnum.NOT_IN)),
    SIX(ConditionFiledTypeEnum.ORDER_FLAG, Lists.newArrayList(ConditionOperatorEnum.IN, ConditionOperatorEnum.NOT_IN)),
    SEVEN(ConditionFiledTypeEnum.GOODS, Lists.newArrayList(ConditionOperatorEnum.IN, ConditionOperatorEnum.NOT_IN)),
    EIGHT(ConditionFiledTypeEnum.GOODS_CATEGORY,
        Lists.newArrayList(ConditionOperatorEnum.IN, ConditionOperatorEnum.NOT_IN)),;

    private final ConditionFiledTypeEnum fieldTypeEnum;
    private final List<ConditionOperatorEnum> operators;

    ConditionFieldWithOperatorEnum(ConditionFiledTypeEnum fieldTypeEnum, List<ConditionOperatorEnum> operators) {
        this.fieldTypeEnum = fieldTypeEnum;
        this.operators = operators;
    }

    public static List<ConditionOperatorEnum> getByFieldTypeEnum(ConditionFiledTypeEnum fieldTypeEnum) {
        for (ConditionFieldWithOperatorEnum value : ConditionFieldWithOperatorEnum.values()) {
            if (fieldTypeEnum == value.getFieldTypeEnum()) {
                return value.getOperators();
            }
        }
        throw new CommonException("条件项配置对应的操作符为空");
    }
}
