package com.tl.rms.order.domain.enums;

import com.tl.rms.common.exception.CommonException;
import com.tl.rms.common.model.EnumInfoVo;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分货类型枚举
 */
@Getter
public enum DistributionTypeEnum {

    MATERIAL("MATERIAL", "物料"),
    SKU("SKU", "SKU"),
    ;

    private final String value;
    private final String label;

    DistributionTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static String getLabelByValue(String value) {
        return Arrays.stream(DistributionTypeEnum.values()).filter(type -> type.getValue().equals(value))
                .findFirst().map(DistributionTypeEnum::getLabel).orElse(null);
    }

    public static DistributionTypeEnum getByValue(String value) {
        DistributionTypeEnum[] values = DistributionTypeEnum.values();
        for (DistributionTypeEnum type : values) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new CommonException("未知的分货类型");
    }

    public static List<EnumInfoVo> list() {
        return Arrays.stream(DistributionTypeEnum.values()).map(
                level -> new EnumInfoVo(level.getValue(), level.getLabel())).collect(Collectors.toList());
    }
}
