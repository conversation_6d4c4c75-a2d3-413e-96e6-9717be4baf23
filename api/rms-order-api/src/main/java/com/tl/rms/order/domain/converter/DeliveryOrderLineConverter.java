package com.tl.rms.order.domain.converter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;

import com.tl.rms.order.domain.po.DeliveryOrderLine;
import com.tl.rms.order.domain.vo.QMDeliveryOrderLineVo;

@Mapper
public interface DeliveryOrderLineConverter {
    DeliveryOrderLineConverter INSTANCE = Mappers.getMapper(DeliveryOrderLineConverter.class);

    default List<DeliveryOrderLine> fromQMDeliveryOrderLineVos(List<QMDeliveryOrderLineVo> deliveryOrderLineVos,
        Long deliveryOrderId) {
        if (CollectionUtils.isEmpty(deliveryOrderLineVos)) {
            return Collections.emptyList();
        }
        List<DeliveryOrderLine> list = new ArrayList<>(deliveryOrderLineVos.size());
        for (QMDeliveryOrderLineVo deliveryOrderLineVo : deliveryOrderLineVos) {
            DeliveryOrderLine deliveryOrderLine = new DeliveryOrderLine();
            list.add(deliveryOrderLine);
            BeanUtils.copyProperties(deliveryOrderLineVo, deliveryOrderLine);
            deliveryOrderLine.setDeliveryOrderId(deliveryOrderId);
            LocalDateTime now = LocalDateTime.now();
            deliveryOrderLine.setCreateTime(now);
            deliveryOrderLine.setUpdateTime(now);
        }
        return list;
    }

    // DeliveryOrderLineVo toVo(DeliveryOrderLine po);
    //
    // DeliveryOrderLine toPo(DeliveryOrderLineVo vo);
    //
    // List<DeliveryOrderLineVo> toVoList(List<DeliveryOrderLine> poList);
    //
    // List<DeliveryOrderLine> toPoList(List<DeliveryOrderLineVo> voList);
}