package com.tl.rms.order.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class JkyOrderPurchaseDiscountRate implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 机型名称
     */
    private String modelName;

    /**
     * 点位%
     */
    private BigDecimal rate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 更新人
     */
    private String modifiedBy;

    /**
     * t_jky_order_purchase_discount_rate
     */
    private static final long serialVersionUID = 1L;

    /**
     * id
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * id
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 店铺编码
     * @return shop_code 店铺编码
     */
    public String getShopCode() {
        return shopCode;
    }

    /**
     * 店铺编码
     * @param shopCode 店铺编码
     */
    public void setShopCode(String shopCode) {
        this.shopCode = shopCode == null ? null : shopCode.trim();
    }

    /**
     * 店铺名称
     * @return shop_name 店铺名称
     */
    public String getShopName() {
        return shopName;
    }

    /**
     * 店铺名称
     * @param shopName 店铺名称
     */
    public void setShopName(String shopName) {
        this.shopName = shopName == null ? null : shopName.trim();
    }

    /**
     * 机型名称
     * @return model_name 机型名称
     */
    public String getModelName() {
        return modelName;
    }

    /**
     * 机型名称
     * @param modelName 机型名称
     */
    public void setModelName(String modelName) {
        this.modelName = modelName == null ? null : modelName.trim();
    }

    /**
     * 点位%
     * @return rate 点位%
     */
    public BigDecimal getRate() {
        return rate;
    }

    /**
     * 点位%
     * @param rate 点位%
     */
    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    /**
     * 创建时间
     * @return create_time 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 创建人
     * @return created_by 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * 创建人
     * @param createdBy 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * 更新时间
     * @return modify_time 更新时间
     */
    public Date getModifyTime() {
        return modifyTime;
    }

    /**
     * 更新时间
     * @param modifyTime 更新时间
     */
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * 更新人
     * @return modified_by 更新人
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * 更新人
     * @param modifiedBy 更新人
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }
}