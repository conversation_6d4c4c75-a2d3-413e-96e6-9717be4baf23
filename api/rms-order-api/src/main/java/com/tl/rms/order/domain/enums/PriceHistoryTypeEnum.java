package com.tl.rms.order.domain.enums;

import lombok.Getter;

@Getter
public enum PriceHistoryTypeEnum {
    DECISION("DECISION", "决策价生效"),
    DECISION_INVALID("DECISION_INVALID", "决策价失效"),
    SPECIAL("SPECIAL", "特价生效"),
    SPECIAL_INVALID("SPECIAL_INVALID", "特价失效"),
    ;

    private final String value;
    private final String desc;

    PriceHistoryTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static PriceHistoryTypeEnum getByValue(String value) {
        for (PriceHistoryTypeEnum item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}
