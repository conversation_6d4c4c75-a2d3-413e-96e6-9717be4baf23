package com.tl.rms.order.domain.response;

import com.tl.rms.order.domain.vo.JkyReturnVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 吉客云 退换补货单接口返回业务数据
 *
 * <AUTHOR>
 * @date 2023/6/1
 */
@Data
public class JkyReturnResponse implements Serializable {

    /**
     * 仅当hasTotal为1是返回，要求翻页过程中不取总数
     */
    private Integer totalResults;

    /**
     * 每页显示数据量
     */
    private Integer pageSize;

    /**
     * 当前页码
     */
    private Integer pageIndex;

    /**
     * 退换补货单
     */
    private List<JkyReturnVO> returnChangeList;

}