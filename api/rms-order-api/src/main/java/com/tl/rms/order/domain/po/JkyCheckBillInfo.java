package com.tl.rms.order.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class JkyCheckBillInfo implements Serializable {
    /**
     * 主键
     */
    private Long id;
    
    /**
     * 账单ID
     */
    private String billId;
    
    /**
     * 财务流水号
     */
    private String billAccountNo;
    
    /**
     * 交易号
     */
    private String tradeNo;
    
    /**
     * 商品名称
     */
    private String goodsName;
    
    /**
     * 预订时间
     */
    private Long bookTime;
    
    /**
     * 对账户名
     */
    private String mail;
    
    /**
     * 收入金额
     */
    private BigDecimal inAmount;
    
    /**
     * 支出金额
     */
    private BigDecimal outAmount;
    
    /**
     * 业务流水号
     */
    private String bizNo;
    
    /**
     * 交易渠道
     */
    private String tradeSource;
    
    /**
     * 交易类型
     */
    private String bizType;
    
    /**
     * 交易备注
     */
    private String tradeMemo;
    
    /**
     * 对账号
     */
    private String checkNo;
    
    /**
     * 关联吉客对账表
     */
    private Long checkId;


    /**
     * 发送EBS状态 0无需发送 1已发送 2待发送
     */
    private Integer sendEbsStatus;

    /**
     * 发送EBS时间
     */
    private Date sendEbsTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 创建日期
     */
    private Integer createDate;
    // Constructors

    // Getter and Setter methods

    /**
     * 获取主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取账单ID
     */
    public String getBillId() {
        return billId;
    }

    /**
     * 设置账单ID
     */
    public void setBillId(String billId) {
        this.billId = billId;
    }

    public Integer getSendEbsStatus() {
        return sendEbsStatus;
    }

    public void setSendEbsStatus(Integer sendEbsStatus) {
        this.sendEbsStatus = sendEbsStatus;
    }

    public Date getSendEbsTime() {
        return sendEbsTime;
    }

    public void setSendEbsTime(Date sendEbsTime) {
        this.sendEbsTime = sendEbsTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Integer createDate) {
        this.createDate = createDate;
    }

    /**
     * 获取财务流水号
     */
    public String getBillAccountNo() {
        return billAccountNo;
    }

    /**
     * 设置财务流水号
     */
    public void setBillAccountNo(String billAccountNo) {
        this.billAccountNo = billAccountNo;
    }

    /**
     * 获取交易号
     */
    public String getTradeNo() {
        return tradeNo;
    }

    /**
     * 设置交易号
     */
    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    /**
     * 获取商品名称
     */
    public String getGoodsName() {
        return goodsName;
    }

    /**
     * 设置商品名称
     */
    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    /**
     * 获取预订时间
     */
    public Long getBookTime() {
        return bookTime;
    }

    /**
     * 设置预订时间
     */
    public void setBookTime(Long bookTime) {
        this.bookTime = bookTime;
    }

    /**
     * 获取对账户名
     */
    public String getMail() {
        return mail;
    }

    /**
     * 设置对账户名
     */
    public void setMail(String mail) {
        this.mail = mail;
    }

    /**
     * 获取收入金额
     */
    public BigDecimal getInAmount() {
        return inAmount;
    }

    /**
     * 设置收入金额
     */
    public void setInAmount(BigDecimal inAmount) {
        this.inAmount = inAmount;
    }

    /**
     * 获取支出金额
     */
    public BigDecimal getOutAmount() {
        return outAmount;
    }

    /**
     * 设置支出金额
     */
    public void setOutAmount(BigDecimal outAmount) {
        this.outAmount = outAmount;
    }

    /**
     * 获取业务流水号
     */
    public String getBizNo() {
        return bizNo;
    }

    /**
     * 设置业务流水号
     */
    public void setBizNo(String bizNo) {
        this.bizNo = bizNo;
    }

    /**
     * 获取交易渠道
     */
    public String getTradeSource() {
        return tradeSource;
    }

    /**
     * 设置交易渠道
     */
    public void setTradeSource(String tradeSource) {
        this.tradeSource = tradeSource;
    }

    /**
     * 获取交易类型
     */
    public String getBizType() {
        return bizType;
    }

    /**
     * 设置交易类型
     */
    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    /**
     * 获取交易备注
     */
    public String getTradeMemo() {
        return tradeMemo;
    }

    /**
     * 设置交易备注
     */
    public void setTradeMemo(String tradeMemo) {
        this.tradeMemo = tradeMemo;
    }

    /**
     * 获取对账号
     */
    public String getCheckNo() {
        return checkNo;
    }

    /**
     * 设置对账号
     */
    public void setCheckNo(String checkNo) {
        this.checkNo = checkNo;
    }

    /**
     * 获取关联吉客对账表
     */
    public Long getCheckId() {
        return checkId;
    }

    /**
     * 设置关联吉客对账表
     */
    public void setCheckId(Long checkId) {
        this.checkId = checkId;
    }
}

