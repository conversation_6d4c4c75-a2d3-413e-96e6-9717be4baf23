package com.tl.rms.order.domain.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class JkyCheck implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 结算账户ID
     */
    private Integer settleAccountId;

    /**
     * 结算账户名称
     */
    private String settleAccountName;

    /**
     * 对账号
     */
    private String checkNo;

    /**
     * 对账人姓名
     */
    private String checkUserName;

    /**
     * 对账备注
     */
    private String checkMemo;

    /**
     * 差异金额
     */
    private BigDecimal differAmount;

    /**
     * 应收合计
     */
    private BigDecimal saleAmount;

    /**
     * 收款合计
     */
    private BigDecimal billAmount;

    /**
     * 往来单位编号
     */
    private String caNo;

    /**
     * 往来单位名称
     */
    private String caName;

    /**
     * 收据日期
     */
    private Long receiptDate;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 发送EBS状态 0无需发送 1待发送 2已发送
     */
    private Integer sendEbsStatus;

    /**
     * 发送EBS时间
     */
    private Date sendEbsTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 创建日期
     */
    private Integer createDate;

    private String ebsGlDate;

    private String ebsReceiptDate;

    /**
     * t_jky_check
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     * @return id 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 结算账户ID
     * @return settle_account_id 结算账户ID
     */
    public Integer getSettleAccountId() {
        return settleAccountId;
    }

    /**
     * 结算账户ID
     * @param settleAccountId 结算账户ID
     */
    public void setSettleAccountId(Integer settleAccountId) {
        this.settleAccountId = settleAccountId;
    }

    /**
     * 结算账户名称
     * @return settle_account_name 结算账户名称
     */
    public String getSettleAccountName() {
        return settleAccountName;
    }

    /**
     * 结算账户名称
     * @param settleAccountName 结算账户名称
     */
    public void setSettleAccountName(String settleAccountName) {
        this.settleAccountName = settleAccountName == null ? null : settleAccountName.trim();
    }

    /**
     * 对账号
     * @return check_no 对账号
     */
    public String getCheckNo() {
        return checkNo;
    }

    /**
     * 对账号
     * @param checkNo 对账号
     */
    public void setCheckNo(String checkNo) {
        this.checkNo = checkNo == null ? null : checkNo.trim();
    }

    /**
     * 对账人姓名
     * @return check_user_name 对账人姓名
     */
    public String getCheckUserName() {
        return checkUserName;
    }

    /**
     * 对账人姓名
     * @param checkUserName 对账人姓名
     */
    public void setCheckUserName(String checkUserName) {
        this.checkUserName = checkUserName == null ? null : checkUserName.trim();
    }

    /**
     * 对账备注
     * @return check_memo 对账备注
     */
    public String getCheckMemo() {
        return checkMemo;
    }

    /**
     * 对账备注
     * @param checkMemo 对账备注
     */
    public void setCheckMemo(String checkMemo) {
        this.checkMemo = checkMemo == null ? null : checkMemo.trim();
    }

    /**
     * 差异金额
     * @return differ_amount 差异金额
     */
    public BigDecimal getDifferAmount() {
        return differAmount;
    }

    /**
     * 差异金额
     * @param differAmount 差异金额
     */
    public void setDifferAmount(BigDecimal differAmount) {
        this.differAmount = differAmount;
    }

    /**
     * 应收合计
     * @return sale_amount 应收合计
     */
    public BigDecimal getSaleAmount() {
        return saleAmount;
    }

    /**
     * 应收合计
     * @param saleAmount 应收合计
     */
    public void setSaleAmount(BigDecimal saleAmount) {
        this.saleAmount = saleAmount;
    }

    /**
     * 收款合计
     * @return bill_amount 收款合计
     */
    public BigDecimal getBillAmount() {
        return billAmount;
    }

    /**
     * 收款合计
     * @param billAmount 收款合计
     */
    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    /**
     * 往来单位编号
     * @return ca_no 往来单位编号
     */
    public String getCaNo() {
        return caNo;
    }

    /**
     * 往来单位编号
     * @param caNo 往来单位编号
     */
    public void setCaNo(String caNo) {
        this.caNo = caNo == null ? null : caNo.trim();
    }

    /**
     * 往来单位名称
     * @return ca_name 往来单位名称
     */
    public String getCaName() {
        return caName;
    }

    /**
     * 往来单位名称
     * @param caName 往来单位名称
     */
    public void setCaName(String caName) {
        this.caName = caName == null ? null : caName.trim();
    }

    /**
     * 收据日期
     * @return receipt_date 收据日期
     */
    public Long getReceiptDate() {
        return receiptDate;
    }

    /**
     * 收据日期
     * @param receiptDate 收据日期
     */
    public void setReceiptDate(Long receiptDate) {
        this.receiptDate = receiptDate;
    }

    /**
     * 公司名称
     * @return company_name 公司名称
     */
    public String getCompanyName() {
        return companyName;
    }

    /**
     * 公司名称
     * @param companyName 公司名称
     */
    public void setCompanyName(String companyName) {
        this.companyName = companyName == null ? null : companyName.trim();
    }

    /**
     * 发送EBS状态 0无需发送 1待发送 2已发送
     * @return send_ebs_status 发送EBS状态 0无需发送 1待发送 2已发送
     */
    public Integer getSendEbsStatus() {
        return sendEbsStatus;
    }

    /**
     * 发送EBS状态 0无需发送 1待发送 2已发送
     * @param sendEbsStatus 发送EBS状态 0无需发送 1待发送 2已发送
     */
    public void setSendEbsStatus(Integer sendEbsStatus) {
        this.sendEbsStatus = sendEbsStatus;
    }

    /**
     * 发送EBS时间
     * @return send_ebs_time 发送EBS时间
     */
    public Date getSendEbsTime() {
        return sendEbsTime;
    }

    /**
     * 发送EBS时间
     * @param sendEbsTime 发送EBS时间
     */
    public void setSendEbsTime(Date sendEbsTime) {
        this.sendEbsTime = sendEbsTime;
    }

    /**
     * 创建时间
     * @return create_time 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     * @return modify_time 更新时间
     */
    public Date getModifyTime() {
        return modifyTime;
    }

    /**
     * 更新时间
     * @param modifyTime 更新时间
     */
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * 创建日期
     * @return create_date 创建日期
     */
    public Integer getCreateDate() {
        return createDate;
    }

    /**
     * 创建日期
     * @param createDate 创建日期
     */
    public void setCreateDate(Integer createDate) {
        this.createDate = createDate;
    }

    public String getEbsGlDate() {
        return ebsGlDate;
    }

    public void setEbsGlDate(String ebsGlDate) {
        this.ebsGlDate = ebsGlDate;
    }

    public String getEbsReceiptDate() {
        return ebsReceiptDate;
    }

    public void setEbsReceiptDate(String ebsReceiptDate) {
        this.ebsReceiptDate = ebsReceiptDate;
    }

}