package com.tl.rms.order.domain.vo;

import com.tl.rms.order.domain.po.JkyOrder;
import com.tl.rms.order.domain.po.JkyOrderGoodsSerial;
import com.tl.rms.order.domain.po.JkyOrderOtherFee;
import com.tl.rms.order.domain.po.JkyOrderPay;
import lombok.Data;

import java.util.List;

@Data
public class JkyOrderSyncVO extends JkyOrder {

    /**
     * 货品详情
     */
    private List<JkyOrderGoodsSyncVO> goodsDetail;

    /**
     * 订单支付详情
     */
    private List<JkyOrderPay> tradeOrderPayList;

    /**
     * 序列号
     */
    private List<JkyOrderGoodsSerial> goodsSerials;

    /**
     * 其他应收
     */
    private List<JkyOrderOtherFee> otherPaymentFees;

    /**
     * 销售单自定义字段
     */
    private JkyOrderExtendVO tradeOrderColumnExt;

}