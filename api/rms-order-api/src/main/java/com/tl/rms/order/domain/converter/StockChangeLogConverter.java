package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.StockChangeLog;
import com.tl.rms.order.domain.po.StockOccupyLog;
import com.tl.rms.order.domain.vo.StockChangeLogSaveReqVo;
import com.tl.rms.order.domain.response.StockChangeLogResponseVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface StockChangeLogConverter {

    StockChangeLogConverter MAPPER = Mappers.getMapper(StockChangeLogConverter.class);

    StockChangeLog toPo(StockChangeLogSaveReqVo vo);

    List<StockChangeLog> toPoList(List<StockChangeLogSaveReqVo> voList);

//    @Mapping(source = "code", target = "orderId")
//    List<StockOccupyLog> toOccupyList(List<StockChangeLogSaveReqVo> voList);

    List<StockChangeLogResponseVo> toVoList(List<StockChangeLog> stockChangeLogResponseVos);
}