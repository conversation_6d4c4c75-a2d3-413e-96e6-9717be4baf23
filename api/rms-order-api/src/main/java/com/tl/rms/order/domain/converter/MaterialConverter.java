package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.Material;
import com.tl.rms.order.domain.vo.BmcPushMaterialVo;
import com.tl.rms.order.domain.vo.MaterialVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface MaterialConverter {

    MaterialConverter MAPPER = Mappers.getMapper(MaterialConverter.class);

    MaterialVo toVo(Material po);

    List<MaterialVo> toVoList(List<Material> poList);

    Material bmcPushMaterialVoToPo(BmcPushMaterialVo bmcPushMaterialVo);
}