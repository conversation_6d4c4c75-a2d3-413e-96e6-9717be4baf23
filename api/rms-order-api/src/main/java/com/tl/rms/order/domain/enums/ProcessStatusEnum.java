package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.BaseEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum ProcessStatusEnum implements BaseEnum<String> {
    PENDING_REVIEW("待转入审核", "待转入审核"), PENDING_UPLOAD("待上传物流信息", "待上传物流信息"), UPLOADED("已上传物流信息", "已上传物流信息"),
    COMPLETED("已完成", "已完成"), CANCELLED("取消", "取消"), CLOSED("关闭", "关闭");

    private final String code;
    private final String desc;

    ProcessStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据中文code获取枚举
     */
    public static ProcessStatusEnum getByCode(String code) {
        for (ProcessStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid status code: " + code);
    }

    /**
     * 根据中文desc获取枚举
     */
    public static ProcessStatusEnum getByDesc(String desc) {
        for (ProcessStatusEnum status : values()) {
            if (status.desc.equals(desc)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid status description: " + desc);
    }
}
