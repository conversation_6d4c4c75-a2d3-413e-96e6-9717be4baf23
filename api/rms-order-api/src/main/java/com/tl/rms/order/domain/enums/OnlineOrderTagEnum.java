package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.BaseEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum OnlineOrderTagEnum implements BaseEnum<String> {
    SOP("SOP", "SOP"), RISK_CONTROL_ORDER("风控订单", "风控订单"), LIVE_COMMERCE("直播带货", "直播带货"), REFUND("退款", "退款"),
    JD_CLOUD_WAREHOUSE("京东云仓", "京东云仓"), INVALID("无效", "无效"), PROMISED_SHIPMENT("承诺发货", "承诺发货"),
    URGENT_SHIPMENT("催发货", "催发货"), SHORT_VIDEO("短视频", "短视频"), RED_FLAG("红旗", "红旗"), GREEN_FLAG("绿旗", "绿旗"),
    SHOWCASE("橱窗", "橱窗"), YELLOW_FLAG("黄旗", "黄旗"), CYAN_FLAG("青旗", "青旗"), LOGISTICS_UPLOAD_SUCCESS("物流上传成功", "物流上传成功"),
    PARTIAL_REFUND("部分退款", "部分退款"), BLUE_FLAG("蓝旗", "蓝旗"), PURPLE_FLAG("紫旗", "紫旗"), PURCHASE_PRICE("采购价", "采购价"),
    LOGISTICS_UPLOAD_FAILED("物流上传失败", "物流上传失败"), SUPPLIER_DISTRIBUTION_ORDER("由供货商发货的分销订单", "由供货商发货的分销订单"),
    PRESALE("预售", "预售"), ORANGE_FLAG("橙旗", "橙旗"), FULL_PAYMENT_PRESALE("全款预售", "全款预售"),
    NO_DISCOUNT_DETAILS("未获取优惠明细", "未获取优惠明细"), SHIP_TODAY("今日发", "今日发"), SHIPMENT_PROHIBITED("禁止发货", "禁止发货"),
    SHIP_IN_24_HOURS("24小时发", "24小时发"), JD_SHIPPING_RESTRICTION("京配的订单只能用京东快递发货", "京配的订单只能用京东快递发货"),
    SMALL_PAYMENT_ORDER("小额收款订单", "小额收款订单");

    private final String code;
    private final String desc;

    OnlineOrderTagEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     */
    public static OnlineOrderTagEnum getByCode(String code) {
        for (OnlineOrderTagEnum tag : values()) {
            if (tag.code.equals(code)) {
                return tag;
            }
        }
        return null;
    }

    /**
     * 根据desc获取枚举
     */
    public static OnlineOrderTagEnum getByDesc(String desc) {
        for (OnlineOrderTagEnum tag : values()) {
            if (tag.desc.equals(desc)) {
                return tag;
            }
        }
        return null;
    }
}
