package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.DeliveryOrderInvoiceDetail;
import com.tl.rms.order.domain.vo.QMDeliveryOrderInvoiceItemVo;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Mapper
public interface DeliveryOrderInvoiceDetailConverter {
    DeliveryOrderInvoiceDetailConverter INSTANCE = Mappers.getMapper(DeliveryOrderInvoiceDetailConverter.class);

    default List<DeliveryOrderInvoiceDetail>
        fromQMDeliveryOrderInvoiceItemVos(List<QMDeliveryOrderInvoiceItemVo> itemList, Long id) {
        if (CollectionUtils.isEmpty(itemList)) {
            return Collections.emptyList();
        }
        LocalDateTime now = LocalDateTime.now();
        List<DeliveryOrderInvoiceDetail> list = new ArrayList<>(itemList.size());
        for (QMDeliveryOrderInvoiceItemVo qmDeliveryOrderInvoiceItemVo : itemList) {
            DeliveryOrderInvoiceDetail deliveryOrderInvoiceDetail = new DeliveryOrderInvoiceDetail();
            list.add(deliveryOrderInvoiceDetail);
            BeanUtils.copyProperties(qmDeliveryOrderInvoiceItemVo, deliveryOrderInvoiceDetail);
            deliveryOrderInvoiceDetail.setInvoiceId(id);
            deliveryOrderInvoiceDetail.setCreateTime(now);
            deliveryOrderInvoiceDetail.setUpdateTime(now);
        }
        return list;
    }

    // DeliveryOrderInvoiceDetailVo toVo(DeliveryOrderInvoiceDetail po);
    //
    // DeliveryOrderInvoiceDetail toPo(DeliveryOrderInvoiceDetailVo vo);
    //
    // List<DeliveryOrderInvoiceDetailVo> toVoList(List<DeliveryOrderInvoiceDetail> poList);
    //
    // List<DeliveryOrderInvoiceDetail> toPoList(List<DeliveryOrderInvoiceDetailVo> voList);
}