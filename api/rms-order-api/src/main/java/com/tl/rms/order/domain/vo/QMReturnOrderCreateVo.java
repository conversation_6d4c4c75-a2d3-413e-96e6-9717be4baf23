package com.tl.rms.order.domain.vo;

import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Data;

/**
 * 奇门退货入库单创建VO
 * <AUTHOR>
 */
@Data
@XmlRootElement(name = "request")
@XmlAccessorType(jakarta.xml.bind.annotation.XmlAccessType.FIELD)
public class QMReturnOrderCreateVo {

    @XmlElement(name = "returnOrder")
    private QMReturnOrderVo returnOrder;

    @XmlElement(name = "orderLines")
    private QMReturnOrderLinesVo orderLines;
}
