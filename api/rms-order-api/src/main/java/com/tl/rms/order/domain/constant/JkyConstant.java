package com.tl.rms.order.domain.constant;

public class JkyConstant {

    /**
     * 来源 吉客云
     */
    public static final String SOURCE_JKY = "JKY";

    /**
     * 销售单查询 返回字段
     */
    public static final String FIELDS_ORDER = "tradeNo,checkTotal,otherFee,chargeCurrency,accountName,payType,payNo,sellerMemo,buyerMemo,appendMemo,tradeFrom,register,seller,auditor,reviewer,estimateWeight,packageWeight,tradeCount,goodsTypeCount,freezeReason,abnormalDescription,onlineTradeNo,gmtCreate,gmtModified,stockoutNo,confirmTime,departName,lastShipTime,payStatus,chargeCurrencyCode,chargeExchangeRate,tradeStatus,grossProfit,estimateVolume,customerTypeName,customerGradeName,customerTags,customerCode,customerDiscount,specialReminding,blackList,tradeTime,country,state,city,district,town,zip,payTime,countryCode,cityCode,invoiceType,payerName,payerRegno,payerBankAccount,payerPhone,auditTime,payerAddress,invoiceNo,invoiceCode,invoiceStatus,payerBankName,preTypedetail,firstPayment,finalPayment,firstPaytime,finalPaytime,reviewTime,activationTime,customerTotalFee,customerDiscountFee,notifyPickTime,consignTime,orderNo,customerPostFee,shopId,customerPayment,companyName,isBillCheck,warehouseCode,warehouseName,logisticName,logisticType,mainPostid,tradeType,totalFee,taxFee,receivedPostFee,discountFee,payment,couponFee,receivedTotal,postFee,isTableSwitch,completeTime,shopCode,signingTime,settleAuditTime,localPayment,localExchangeRate,customerAccount,localCurrencyCode,platCompleteTime,flagIds,flagNames,sysFlagIds,govSubsidy,columnExt,sourceAfterNo,chargeAccount,chargeType,goodsSerial.skuId,goodsSerial.subTradeId,goodsSerial.serialNo,expense.expenseFee,expense.expenseItemName,goodsDetail.goodsNo,goodsDetail.goodsName,goodsDetail.specName,goodsDetail.barcode,goodsDetail.sellCount,goodsDetail.unit,goodsDetail.sellPrice,goodsDetail.cost,goodsDetail.discountTotal,goodsDetail.discountPoint,goodsDetail.shareFavourableFee,goodsDetail.estimateWeight,goodsDetail.goodsMemo,goodsDetail.cateName,goodsDetail.brandName,goodsDetail.goodsTags,goodsDetail.isGift,goodsDetail.discountFee,goodsDetail.taxRate,goodsDetail.estimateGoodsVolume,goodsDetail.isPresell,goodsDetail.customerPrice,goodsDetail.customerTotal,goodsDetail.tradeGoodsNo,goodsDetail.tradeGoodsName,goodsDetail.tradeGoodsSpec,goodsDetail.tradeGoodsUnit,goodsDetail.sourceSubtradeNo,goodsDetail.platCode,goodsDetail.platGoodsId,goodsDetail.subTradeId,goodsDetail.platAuthorId,goodsDetail.platAuthorName,goodsDetail.isPlatGift,goodsDetail.shareFavourableAfterFee,goodsDetail.divideSellTotal,goodsDetail.shareOrderDiscountFee,goodsDetail.sourceTradeNo,goodsDetail.customerTradeNo,actualSendCount,goodsDetail.specId,goodsDelivery.batchNo";

}
