package com.tl.rms.order.domain.converter;

import com.tl.rms.order.domain.po.DeliveryOrderInsurance;
import com.tl.rms.order.domain.po.DeliveryOrderReceiver;
import com.tl.rms.order.domain.vo.QMDeliveryOrderInsuranceVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

@Mapper
public interface DeliveryOrderInsuranceConverter {
    DeliveryOrderInsuranceConverter INSTANCE = Mappers.getMapper(DeliveryOrderInsuranceConverter.class);

    default DeliveryOrderInsurance fromQMDeliveryOrderInsuranceVo(QMDeliveryOrderInsuranceVo insurance){
        if (insurance == null) {
            return null;
        }
        DeliveryOrderInsurance deliveryOrderInsurance = new DeliveryOrderInsurance();
        BeanUtils.copyProperties(insurance, deliveryOrderInsurance);
        LocalDateTime now = LocalDateTime.now();
        deliveryOrderInsurance.setCreateTime(now);
        deliveryOrderInsurance.setUpdateTime(now);
        return deliveryOrderInsurance;
    }


//    DeliveryOrderInsuranceVo toVo(DeliveryOrderInsurance po);
//
//    DeliveryOrderInsurance toPo(DeliveryOrderInsuranceVo vo);
//
//    List<DeliveryOrderInsuranceVo> toVoList(List<DeliveryOrderInsurance> poList);
//
//    List<DeliveryOrderInsurance> toPoList(List<DeliveryOrderInsuranceVo> voList);
}