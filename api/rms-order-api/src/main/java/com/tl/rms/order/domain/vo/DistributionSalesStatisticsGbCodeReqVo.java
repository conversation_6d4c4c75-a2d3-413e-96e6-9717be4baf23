package com.tl.rms.order.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 分货销售统计-国标码维度查询参数vo
 * <AUTHOR>
 */
@Data
public class DistributionSalesStatisticsGbCodeReqVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // 国标码
    private List<String> gbCodes;

    // 店铺编码(销售单中店铺编码可以对应上)
    private List<String> shopCodes;

    // 统计时间范围-开始时间
    private Date startTime;

    // 统计时间范围-结束时间
    private Date endTime;
}