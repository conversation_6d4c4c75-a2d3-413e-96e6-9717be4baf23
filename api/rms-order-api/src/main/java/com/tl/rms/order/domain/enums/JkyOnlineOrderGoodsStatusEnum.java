package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.BaseEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum JkyOnlineOrderGoodsStatusEnum implements BaseEnum<String> {
    /**
     * 线上订单状态：等待买家付款
     */
    WAIT_BUYER_PAY("WAIT_BUYER_PAY", "等待买家付款"),
    /**
     * 等待卖家发货,即:买家已付款
     */
    WAIT_SELLER_SEND_GOODS("WAIT_SELLER_SEND_GOODS", "等待卖家发货"),
    /**
     * 等待买家确认收货,即:卖家已发货
     */
    WAIT_BUYER_CONFIRM_GOODS("WAIT_BUYER_CONFIRM_GOODS", "等待买家确认收货"),
    /**
     * 交易成功
     */
    TRADE_FINISHED("TRADE_FINISHED", "交易成功"),
    /**
     * 交易关闭
     */
    TRADE_CLOSED("TRADE_CLOSED", "交易关闭"),
    
    OTHER("OTHER","其他");

    private final String code;
    private final String desc;
}
