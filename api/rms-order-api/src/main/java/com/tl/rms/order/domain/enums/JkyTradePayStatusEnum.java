package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.EnumInfoVo;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 吉客云付款状态
 */
@Getter
public enum JkyTradePayStatusEnum {

    UNPAID("0", "未付款"),
    PARTIAL_PAYMENT("5", "部分付款"),
    PAID("9", "已付款");

    private final String value;
    private final String label;

    JkyTradePayStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static String getLabelByValue(String value) {
        return Arrays.stream(JkyTradePayStatusEnum.values()).filter(type -> type.getValue().equals(value))
                .findFirst().map(JkyTradePayStatusEnum::getLabel).orElse(null);
    }

    public static List<EnumInfoVo> list() {
        return Arrays.stream(JkyTradePayStatusEnum.values()).map(
                level -> new EnumInfoVo(level.getValue(), level.getLabel())).collect(Collectors.toList());
    }
}
