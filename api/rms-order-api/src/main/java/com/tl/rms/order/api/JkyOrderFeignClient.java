package com.tl.rms.order.api;


import com.tl.rms.lib.beans.web.restful.ResponseMessage;
import com.tl.rms.order.domain.po.JkyOrder;
import com.tl.rms.order.domain.po.JkyOrderShip;
import com.tl.rms.order.domain.po.extend.JkyOrderShipExtend;
import com.tl.rms.order.domain.vo.JkyOrderEbsVO;
import com.tl.rms.order.domain.vo.JkyOrderGoodsEbsVO;
import com.tl.rms.order.domain.vo.JkyOrderSyncVO;
import com.tl.rms.order.domain.vo.JkyReturnVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

@FeignClient(contextId = "JkyOrderFeignClient", value = "rms-order", path = "/jky")
public interface JkyOrderFeignClient {

    @PostMapping(value = "/syncJkyOrder")
    ResponseMessage syncJkyOrder(@RequestBody List<JkyOrderSyncVO> orderVOs, @RequestParam("userId") String userId);

    @PostMapping(value = "/syncJkyOrderStatus")
    ResponseMessage syncJkyOrderStatus(@RequestBody List<JkyOrderSyncVO> orderVOs, @RequestParam("userId") String userId);

    @PostMapping(value = "/queryJkyOrderMaxAuditTime")
    ResponseMessage<String> queryJkyOrderMaxAuditTime(@RequestParam(value = "type", required = false) String type);

    @PostMapping(value = "/listTradeNo2Update")
    ResponseMessage<List<String>> listTradeNo2Update();

    @PostMapping(value = "/listTradeNoByDate")
    ResponseMessage<List<String>> listTradeNoByDate(@RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate);

    @PostMapping(value = "/queryJkyOrderEbsVOByTradeId")
    ResponseMessage<JkyOrderEbsVO> queryJkyOrderEbsVOByTradeId(@RequestParam("tradeId") Long tradeId);

    @PostMapping(value = "/queryJkyOrderGoodsEbsVOByTradeId")
    ResponseMessage<List<JkyOrderGoodsEbsVO>> queryJkyOrderGoodsEbsVOByTradeId(@RequestParam("tradeId") Long tradeId);

    @PostMapping(value = "/updateJkyOrderSendEbsTimeCustBranch")
    ResponseMessage updateJkyOrderSendEbsTimeCustBranch(@RequestBody JkyOrderEbsVO order);

    @PostMapping(value = "/updateJkyOrderSendEbsStatusError")
    ResponseMessage updateJkyOrderSendEbsStatusError(@RequestParam("tradeId") Long tradeId, @RequestParam("errorMsg") String errorMsg);

    @PostMapping(value = "/queryJkyOrderShopCodeByTradeNo")
    ResponseMessage<String> queryJkyOrderShopCodeByTradeNo(@RequestParam("tradeNo") String tradeNo);

    @PostMapping(value = "/queryJkyOrderByBillTradeNo")
    ResponseMessage<List<JkyOrder>> queryJkyOrderByBillTradeNo(@RequestParam("billTradeNo") String billTradeNo);

    @PostMapping(value = "/syncJkyReturn")
    ResponseMessage syncJkyReturn(@RequestBody List<JkyReturnVO> returnVOs, @RequestParam("userId") String userId);

    @PostMapping(value = "/queryJkyReturnMaxAuditTime")
    ResponseMessage<String> queryJkyReturnMaxAuditTime();

    @PostMapping(value = "/saveJkyOrderShip")
    ResponseMessage saveJkyOrderShip(@RequestBody List<JkyOrderShip> jkyOrderShips);

    @PostMapping(value = "/queryByOrderId")
    List<JkyOrderShipExtend> queryByOrderId(@RequestParam("orderId") String orderId);

    @PostMapping(value = "/updateOut2EbsStatus")
    void updateOut2EbsStatus(@RequestBody List<Long> ids);

    @PostMapping(value = "/updateSign2EbsStatus")
    void updateSign2EbsStatus(@RequestBody List<Long> ids);

    @PostMapping(value = "/updateIn2EbsStatus")
    void updateIn2EbsStatus(@RequestBody List<Long> ids);

    @PostMapping(value = "/updateCancelLine2EbsStatus")
    void updateCancelLine2EbsStatus(@RequestBody List<Long> ids);

    @PostMapping(value = "/getRateByShopAndModel")
    ResponseMessage<BigDecimal> getRateByShopAndModel(@RequestParam("shopCode") String shopCode, @RequestParam("modelName") String modelName);

}
