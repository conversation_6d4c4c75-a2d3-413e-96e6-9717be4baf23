package com.tl.rms.order.domain.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 吉客云 退换补货单收件人信息
 *
 * <AUTHOR>
 * @date 2023/5/31
 */
@Data
public class JkyReturnReceiver implements Serializable {
    /**
     * 订单行ID
     */
    private Long id;

    /**
     * 售后id
     */
    private Long tradeAfterId;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户账户
     */
    private String customerAccount;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 邮编
     */
    private String zip;

    /**
     * 国家
     */
    private String country;

    /**
     * 省
     */
    private String state;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县
     */
    private String district;

    /**
     * 街道
     */
    private String town;

    /**
     * 退换单修改时间
     */
    private Date gmtModified;

    /**
     * 退换单创建时间
     */
    private Date gmtCreate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 更新人
     */
    private String modifiedBy;

}