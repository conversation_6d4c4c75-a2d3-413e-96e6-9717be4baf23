package com.tl.rms.order.domain.enums;

import com.tl.rms.common.model.EnumInfoVo;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分货页面保存类型枚举
 */
@Getter
public enum DistributionPageSaveTypeEnum {

    SAVE("save", "保存"),
    DISTRIBUTION("distribution", "确认分货"),
    ;

    private final String value;
    private final String label;

    DistributionPageSaveTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static String getLabelByValue(String value) {
        return Arrays.stream(DistributionPageSaveTypeEnum.values()).filter(type -> type.getValue().equals(value))
                .findFirst().map(DistributionPageSaveTypeEnum::getLabel).orElse(null);
    }

    public static List<EnumInfoVo> list() {
        return Arrays.stream(DistributionPageSaveTypeEnum.values()).map(
                level -> new EnumInfoVo(level.getValue(), level.getLabel())).collect(Collectors.toList());
    }
}
