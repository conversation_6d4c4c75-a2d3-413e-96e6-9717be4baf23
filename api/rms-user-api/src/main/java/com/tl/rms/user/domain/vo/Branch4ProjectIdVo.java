package com.tl.rms.user.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分公司信息 与项目id相关联的信息
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "分公司信息 与项目id相关联的信息")
public class Branch4ProjectIdVo {

    @Schema(description = "分公司ID")
    private String branchId;

    @Schema(description = "分公司名称")
    private String branchName;

    @Schema(description = "店铺名称")
    private String channelName;

    @Schema(description = "项目id")
    private String projectId;
}