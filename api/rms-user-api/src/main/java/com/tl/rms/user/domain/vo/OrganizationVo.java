package com.tl.rms.user.domain.vo;

import com.tl.rms.common.model.validator.group.Create;
import com.tl.rms.common.model.validator.group.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "组织机构视图对象")
public class OrganizationVo implements Serializable {

    @Schema(description = "主键")
    @NotNull(message = "主键不能为空", groups = {Update.class})
    private Long id;

    @Schema(description = "组织编码")
    @NotBlank(message = "组织编码不能为空", groups = {Create.class, Update.class})
    private String code;

    @Schema(description = "组织名称")
    @NotBlank(message = "组织名称不能为空", groups = {Create.class, Update.class})
    private String name;

    @Schema(description = "组织简介")
    private String description;

    @Schema(description = "上级组织ID")
    private Long parentId;

    @Schema(description = "是否总部 0-否 1-是")
    private Integer isHq;

    @Schema(description = "组织层级 1-总公司 2-分公司 3-事业部")
    @NotNull(message = "组织层级不能为空", groups = {Create.class, Update.class})
    private Integer level;

    @Schema(description = "状态 1-启用 0-停用")
    @NotNull(message = "状态不能为空", groups = {Create.class, Update.class})
    private Integer status;

    @Schema(description = "创建人ID", hidden = true)
    private Long createBy;

    @Schema(description = "更新人ID", hidden = true)
    private Long updateBy;

    @Schema(description = "子组织")
    private List<OrganizationVo> children;

}