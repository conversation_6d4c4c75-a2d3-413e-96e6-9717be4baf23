package com.tl.rms.user.domain.converter;

import com.tl.rms.user.domain.po.Role;
import com.tl.rms.user.domain.vo.RoleExportVo;
import com.tl.rms.user.domain.vo.RoleVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface RoleConverter {

    RoleConverter MAPPER = Mappers.getMapper(RoleConverter.class);

    RoleVo toVo(Role po);

    Role toPo(RoleVo vo);

    List<RoleVo> toVoList(List<Role> poList);

    List<Role> toPoList(List<RoleVo> voList);

    @Mapping(source = "status", target = "statusDesc", qualifiedByName = "statusToDesc")
    RoleExportVo toExportVo(RoleVo vo);

    List<RoleExportVo> toExportVoList(List<RoleVo> voList);

    @Named("statusToDesc")
    default String statusToDesc(Integer status) {
        return status != null && status == 1 ? "启用" : "禁用";
    }
} 