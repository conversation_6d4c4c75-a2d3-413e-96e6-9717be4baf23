package com.tl.rms.user.domain.enums;

import com.tl.rms.common.model.EnumInfoVo;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据权限模块 枚举
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum DataPermissionModuleEnum {

    ORGANIZATION(1L, "organization", "组织架构"),
    MATERIAL(2L, "material", "物料"),
    PRICE(3L, "price", "价格"),
    SHOP(4L, "shop", "店铺"),
    STOCK(5L, "stock", "库存"),
    ;

    private final Long moduleId;
    private final String moduleCode;
    private final String moduleName;

    public static DataPermissionModuleEnum getByModuleCode(String value) {
        return Arrays.stream(DataPermissionModuleEnum.values()).filter(
                level -> level.getModuleCode().equals(value)).findAny().orElse(null);
    }

    public static List<EnumInfoVo> list() {
        return Arrays.stream(DataPermissionModuleEnum.values()).map(
                level -> new EnumInfoVo(level.getModuleCode(), level.getModuleName())).collect(Collectors.toList());
    }
}