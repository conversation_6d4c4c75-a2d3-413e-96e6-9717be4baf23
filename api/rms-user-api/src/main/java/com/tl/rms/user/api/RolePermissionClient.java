package com.tl.rms.user.api;

import com.tl.rms.user.domain.vo.PermissionVo;
import com.tl.rms.user.domain.vo.RolePermissionVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(contextId = "RolePermissionClient", value = "rms-user")
public interface RolePermissionClient {

    String PATH = "/role/permission";

    @PostMapping(PATH + "/config")
    boolean configRolePermissions(@RequestBody @Validated RolePermissionVo rolePermissionVo);

    @GetMapping(PATH + "/{roleId}")
    List<PermissionVo> getPermissionsByRoleId(@PathVariable("roleId") Long roleId);

    @PostMapping(PATH + "/batch")
    List<PermissionVo> getPermissionsByRoleIds(@RequestBody List<Long> roleIds);

    @GetMapping(PATH + "/user/{userId}")
    List<PermissionVo> getPermissionsByUserId(@PathVariable("userId") Long userId);
}