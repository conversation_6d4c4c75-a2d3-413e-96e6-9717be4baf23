package com.tl.rms.user.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "角色数据权限配置请求对象")
public class RoleDataPermissionConfigReqVo implements Serializable {

    @Schema(description = "角色ID")
    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    @Schema(description = "数据权限状态 0-不开启 1-开启")
    @NotNull(message = "数据权限状态不能为空")
    private Integer dataPermissionStatus;

    @Schema(description = "数据权限配置列表")
    private List<DataPermissionConfigItem> dataPermissionConfigs;

    @Schema(description = "操作人ID", hidden = true)
    private Long operatorId;

    @Data
    public static class DataPermissionConfigItem {
        @Schema(description = "模块ID")
        @NotNull(message = "模块ID不能为空")
        private Long moduleId;

        @Schema(description = "权限级别")
        @NotNull(message = "权限级别不能为空")
        private String permissionLevel;
    }
}