package com.tl.rms.user.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("t_shop_branch")
public class ShopBranch {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("shop_id")
    private Long shopId;

    @TableField("branch_id")
    private String branchId;

    @TableField("branch_name")
    private String branchName;

    @TableField("is_primary")
    private Boolean isPrimary;

    @TableField("create_by")
    private Long createBy;

    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField("update_by")
    private Long updateBy;

    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    @TableField("deleted")
    private Boolean deleted;
} 