package com.tl.rms.user.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "仓库导出视图对象")
public class WarehouseExportVo {

    public static final String EXPORT_EXCEL_NAME = "仓库列表";

    @ExcelProperty(value = "仓库编号")
    @ColumnWidth(20)
    private String id;

    @ExcelProperty(value = "仓库名称")
    @ColumnWidth(20)
    private String name;

    @ExcelProperty(value = "使用分公司")
    @ColumnWidth(60)
    private String branchName;

    @ExcelProperty(value = "物权")
    @ColumnWidth(40)
    private String orgName;

    @ExcelProperty(value = "地址")
    @ColumnWidth(100)
    private String address;

    @ExcelProperty(value = "联系人")
    @ColumnWidth(60)
    private String contactName;

    @ExcelProperty(value = "联系电话")
    @ColumnWidth(60)
    private String contactWay;
}