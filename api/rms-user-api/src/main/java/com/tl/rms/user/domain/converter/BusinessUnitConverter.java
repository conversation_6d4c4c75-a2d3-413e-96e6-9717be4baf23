package com.tl.rms.user.domain.converter;

import com.tl.rms.user.domain.po.BusinessUnit;
import com.tl.rms.user.domain.vo.BusinessUnitVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BusinessUnitConverter {

    BusinessUnitConverter MAPPER = Mappers.getMapper(BusinessUnitConverter.class);

    BusinessUnitVo toVo(BusinessUnit po);

    BusinessUnit toPo(BusinessUnitVo vo);

    List<BusinessUnitVo> toVoList(List<BusinessUnit> poList);

    List<BusinessUnit> toPoList(List<BusinessUnitVo> voList);
}