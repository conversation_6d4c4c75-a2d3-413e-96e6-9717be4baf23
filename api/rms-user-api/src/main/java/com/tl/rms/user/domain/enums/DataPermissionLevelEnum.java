package com.tl.rms.user.domain.enums;

import com.tl.rms.common.model.EnumInfoVo;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum DataPermissionLevelEnum {

    ALL_DATA("ALL_DATA", "全部数据"),
    PROJECT_DATA("PROJECT_DATA", "本项目数据"),
    COMPANY_DATA("COMPANY_DATA", "本公司数据"),
    DEPARTMENT_DATA("DEPARTMENT_DATA", "本部门数据");

    private final String value;
    private final String label;

    public static DataPermissionLevelEnum getByValue(String value) {
        return Arrays.stream(DataPermissionLevelEnum.values()).filter(
                level -> level.getValue().equals(value)).findAny().orElse(null);
    }

    public static List<EnumInfoVo> list() {
        return Arrays.stream(DataPermissionLevelEnum.values()).map(
                level -> new EnumInfoVo(level.getValue(), level.getLabel())).collect(Collectors.toList());
    }
}