package com.tl.rms.user.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tl.rms.common.model.validator.group.Create;
import com.tl.rms.common.model.validator.group.Update;
import com.tl.rms.user.domain.vo.ShopChannelIdUpdateReqVo;
import com.tl.rms.user.domain.vo.ShopQueryReqVo;
import com.tl.rms.user.domain.vo.ShopSyncStatusUpdateReqVo;
import com.tl.rms.user.domain.vo.ShopVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.List;

@FeignClient(contextId = "ShopClient", value = "rms-user")
public interface ShopClient {

    String PATH = "/shop";

    @PostMapping(PATH + "/page")
    Page<ShopVo> page(@RequestBody ShopQueryReqVo queryVo);

    @GetMapping(PATH + "/list")
    List<ShopVo> list(@RequestParam(value = "isDistributionSupport", required = false) Integer isDistributionSupport);

    @GetMapping(PATH + "/listByPalletGoods")
    List<ShopVo> listByPalletGoods(@RequestParam(value = "isPalletGoods", required = false) Integer isPalletGoods);

    @PostMapping(PATH + "/listWithDataPermission")
    List<ShopVo> listWithDataPermission(@RequestBody ShopQueryReqVo queryVo);

    @GetMapping(PATH + "/{id}")
    ShopVo getById(@PathVariable("id") Long id);

    @GetMapping(PATH + "/getByCode/{code}")
    ShopVo getByCode(@PathVariable("code") String code);

    @GetMapping(PATH + "/existsByCode/{code}")
    boolean existsByCode(@PathVariable("code") String code);

    @GetMapping(PATH + "/existsByCodeExcludeSelf")
    boolean existsByCodeExcludeSelf(@RequestParam(value = "id", required = false) Long id, @RequestParam("code") String code);

    @GetMapping(PATH + "/existsByNameExcludeSelf")
    boolean existsByNameExcludeSelf(@RequestParam(value = "id", required = false) Long id, @RequestParam("name") String name);

    @PostMapping(PATH)
    boolean create(@RequestBody @Validated(Create.class) ShopVo shopVo);

    @PutMapping(PATH)
    boolean update(@RequestBody @Validated(Update.class) ShopVo shopVo);

    @PutMapping(PATH + "/updateChannelIdAndSyncStatus")
    boolean updateChannelIdAndSyncStatus(@RequestBody @Validated(Update.class) ShopChannelIdUpdateReqVo shopChannelIdUpdateReqVo);

    @PutMapping(PATH + "/updateSyncStatus")
    boolean updateSyncStatus(@RequestBody @Validated(Update.class) ShopSyncStatusUpdateReqVo shopSyncStatusUpdateReqVo);

    @PostMapping(PATH + "/getByIds")
    List<ShopVo> getByIds(@RequestBody List<Long> shopIdList);

    /**
     * 获取门店Map
     * @return
     */
    @GetMapping(PATH + "/getShopMap")
    HashMap<String, ShopVo> getShopMap();
}