package com.tl.rms.user.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "项目信息")
public class Project4ShopVo {

    @Schema(description = "项目ID")
    private String id;

    @Schema(description = "项目名称")
    private String name;

    @Schema(description = "事业部ID")
    private String buId;

    @Schema(description = "事业部名称")
    private String buName;

    @Schema(description = "是否一盘货 0-否 1-是")
    private Integer isPalletGoods;

}