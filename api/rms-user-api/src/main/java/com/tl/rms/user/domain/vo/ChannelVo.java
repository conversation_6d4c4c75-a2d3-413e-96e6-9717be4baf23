package com.tl.rms.user.domain.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 销售渠道信息 VO 类，用于封装销售渠道的相关信息
 *
 * <AUTHOR>
 * @date 2025/4/1
 */
@Getter
@Setter
public class ChannelVo implements Serializable {

    /**
     * 序列化版本号，用于保证序列化和反序列化过程中的兼容性
     */
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 销售渠道的唯一标识符
     */
    private Long channelId;

    /**
     * 销售渠道的编码
     */
    private String channelCode;

    /**
     * 销售渠道的名称
     */
    private String channelName;

    /**
     * 渠道类型，取值含义如下：
     * 0：分销办公室；
     * 1：直营网店；
     * 2：直营门店；
     * 3：销售办公室；
     * 4：货主虚拟店；
     * 5：分销虚拟店；
     * 6：加盟门店；
     * 7：内部交易渠道
     */
    private String channelType;

    /**
     * 店铺所在平台的编码
     */
    private String onlinePlatTypeCode;

    /**
     * 店铺所在平台的名称
     */
    private String onlinePlatTypeName;

    /**
     * 销售渠道所属公司的唯一标识符
     */
    private Long companyId;

    /**
     * 销售渠道所属公司的编码
     */
    private String companyCode;

    /**
     * 销售渠道所属公司的名称
     */
    private String companyName;

    /**
     * 负责该销售渠道的部门的唯一标识符
     */
    private Long channelDepartId;

    /**
     * 负责该销售渠道的部门的编码
     */
    private String departCode;

    /**
     * 负责该销售渠道的部门的名称
     */
    private String channelDepartName;

    /**
     * 销售渠道的联系人姓名
     */
    private String linkMan;

    /**
     * 销售渠道的联系电话
     */
    private String linkTel;

    /**
     * 销售渠道的办公地址
     */
    private String officeAddress;

    /**
     * 销售商品所属分组的唯一标识符
     */
    private Long groupId;

    /**
     * 销售渠道的联系邮箱
     */
    private String email;

    /**
     * 销售渠道所在地址的邮编
     */
    private String postcode;

    /**
     * 销售渠道所在国家的唯一标识符
     */
    private Integer countryId;

    /**
     * 销售渠道所在国家的名称
     */
    private String countryName;

    /**
     * 销售渠道所在省份的唯一标识符
     */
    private Integer provinceId;

    /**
     * 销售渠道所在省份的名称
     */
    private String provinceName;

    /**
     * 销售渠道所在城市的唯一标识符
     */
    private Integer cityId;

    /**
     * 销售渠道所在城市的名称
     */
    private String cityName;

    /**
     * 销售渠道所在区的唯一标识符
     */
    private Integer townId;

    /**
     * 销售渠道所在的镇或区的名称
     */
    private String townName;

    /**
     * 销售渠道所在街道的唯一标识符
     */
    private Integer streetId;

    /**
     * 销售渠道所在的街道名称
     */
    private String streetName;

    /**
     * 关于该销售渠道的备注信息
     */
    private String memo;

    /**
     * 销售渠道默认使用的仓库编码
     */
    private String warehouseCode;

    /**
     * 销售渠道默认使用的仓库名称
     */
    private String warehouseName;

    /**
     * 销售渠道的结算方式，取值含义如下：
     * 1：担保交易；
     * 2：银行收款；
     * 3：现金收款；
     * 4：货到付款；
     * 5：欠款计应收；
     * 6：客户预存款；
     * 7：多种结算；
     * 8：退换货冲抵；
     * 9：电子钱包
     */
    private String chargeType;

    /**
     * 销售渠道分类的唯一标识符
     */
    private Long cateId;

    /**
     * 销售渠道的分类名称
     */
    private String cateName;

    /**
     * 渠道自定义字段 1
     */
    private String field1;

    /**
     * 渠道自定义字段 2
     */
    private String field2;

    /**
     * 渠道自定义字段 3
     */
    private String field3;

    /**
     * 渠道自定义字段 4
     */
    private String field4;

    /**
     * 渠道自定义字段 5
     */
    private String field5;

    /**
     * 渠道自定义字段 6
     */
    private String field6;

    /**
     * 渠道自定义字段 7
     */
    private String field7;

    /**
     * 渠道自定义字段 8
     */
    private String field8;

    /**
     * 渠道自定义字段 9
     */
    private String field9;

    /**
     * 渠道自定义字段 10
     */
    private String field10;

    /**
     * 渠道自定义字段 11
     */
    private String field11;

    /**
     * 渠道自定义字段 12
     */
    private String field12;

    /**
     * 渠道自定义字段 13
     */
    private String field13;

    /**
     * 渠道自定义字段 14
     */
    private String field14;

    /**
     * 渠道自定义字段 15
     */
    private String field15;

    /**
     * 渠道自定义字段 16
     */
    private String field16;

    /**
     * 渠道自定义字段 17
     */
    private String field17;

    /**
     * 渠道自定义字段 18
     */
    private String field18;

    /**
     * 渠道自定义字段 19
     */
    private String field19;

    /**
     * 渠道自定义字段 20
     */
    private String field20;

    /**
     * 渠道自定义字段 21
     */
    private String field21;

    /**
     * 渠道自定义字段 22
     */
    private String field22;

    /**
     * 渠道自定义字段 23
     */
    private String field23;

    /**
     * 渠道自定义字段 24
     */
    private String field24;

    /**
     * 渠道自定义字段 25
     */
    private String field25;

    /**
     * 渠道自定义字段 26
     */
    private String field26;

    /**
     * 渠道自定义字段 27
     */
    private String field27;

    /**
     * 渠道自定义字段 28
     */
    private String field28;

    /**
     * 渠道自定义字段 29
     */
    private String field29;

    /**
     * 渠道自定义字段 30
     */
    private String field30;

}