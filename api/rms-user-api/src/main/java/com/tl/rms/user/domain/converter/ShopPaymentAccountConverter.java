package com.tl.rms.user.domain.converter;

import com.tl.rms.user.domain.po.ShopPaymentAccount;
import com.tl.rms.user.domain.vo.ShopPaymentAccountVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ShopPaymentAccountConverter {

    ShopPaymentAccountConverter MAPPER = Mappers.getMapper(ShopPaymentAccountConverter.class);

    ShopPaymentAccountVo toVo(ShopPaymentAccount po);

    ShopPaymentAccount toPo(ShopPaymentAccountVo vo);

    List<ShopPaymentAccountVo> toVoList(List<ShopPaymentAccount> poList);

    List<ShopPaymentAccount> toPoList(List<ShopPaymentAccountVo> voList);
}