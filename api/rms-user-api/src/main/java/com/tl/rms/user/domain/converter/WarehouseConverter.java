package com.tl.rms.user.domain.converter;

import com.tl.rms.user.domain.po.Warehouse;
import com.tl.rms.user.domain.vo.WarehouseBranchVo;
import com.tl.rms.user.domain.vo.WarehouseExportVo;
import com.tl.rms.user.domain.vo.WarehouseVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface WarehouseConverter {

    WarehouseConverter MAPPER = Mappers.getMapper(WarehouseConverter.class);

    WarehouseVo toVo(Warehouse po);

    Warehouse toPo(WarehouseVo vo);

    List<WarehouseVo> toVoList(List<Warehouse> poList);

    List<Warehouse> toPoList(List<WarehouseVo> voList);

    @Mapping(source = "warehouseBranchVoList", target = "branchName", qualifiedByName = "joinBranchName")
    WarehouseExportVo toExportVo(WarehouseVo vo);

    List<WarehouseExportVo> toExportVoList(List<WarehouseVo> voList);

    @Named("joinBranchName")
    default String joinBranchName(List<WarehouseBranchVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        return list.stream().map(WarehouseBranchVo::getBranchName)
                .filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
    }

}