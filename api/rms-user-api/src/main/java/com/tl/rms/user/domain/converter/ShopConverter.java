package com.tl.rms.user.domain.converter;

import com.tl.rms.user.domain.po.Shop;
import com.tl.rms.user.domain.vo.ChannelVo;
import com.tl.rms.user.domain.vo.ShopExportVo;
import com.tl.rms.user.domain.vo.ShopVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ShopConverter {

    ShopConverter MAPPER = Mappers.getMapper(ShopConverter.class);

    ShopVo toVo(Shop po);

    Shop toPo(ShopVo vo);

    /**
     * 吉客云的部门按照太力项目来设置
     */
    // 单个属性
    // @Mapping(source = "channelId",target = "id")
    // 多个属性
    @Mappings(value = {
            @Mapping(source = "companyCode", target = "companyId"),
            @Mapping(source = "departCode", target = "projectId"),
            @Mapping(source = "warehouseCode", target = "warehouseId")
    })
    ShopVo voToVo(ChannelVo vo);

    List<ShopVo> toVoList(List<Shop> poList);

    List<Shop> toPoList(List<ShopVo> voList);

    @Mapping(target = "onlinePlatTypeName", expression = "java(com.tl.rms.user.domain.enums.OnlinePlatTypeEnum.getNameByValue(vo.getOnlinePlatTypeCode()))")
    ShopExportVo toExportVo(ShopVo vo);

    List<ShopExportVo> toExportVoList(List<ShopVo> voList);
}