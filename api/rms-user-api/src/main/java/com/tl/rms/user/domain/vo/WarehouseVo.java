package com.tl.rms.user.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "仓库信息")
public class WarehouseVo {

    @Schema(description = "仓库ID")
    private String id;

    @Schema(description = "仓库名称")
    private String name;

    @Schema(description = "所属分公司编码")
    private String orgCode;

    @Schema(description = "所属分公司")
    private String orgName;

    @Schema(description = "仓库物权")
    private String whOrgName;

    @Schema(description = "联系人")
    private String contactName;

    @Schema(description = "联系方式")
    private String contactWay;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "是否活跃 0-否 1-是")
    private Integer isActive;

    @Schema(description = "关联的分公司列表")
    List<WarehouseBranchVo> warehouseBranchVoList;
}