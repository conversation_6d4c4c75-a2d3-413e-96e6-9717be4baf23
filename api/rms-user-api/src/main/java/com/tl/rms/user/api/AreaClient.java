package com.tl.rms.user.api;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.tl.rms.user.domain.vo.AreaVo;

/**
 * <AUTHOR>
 */
@FeignClient(name = "rms-user", contextId = "AreaClient", path = "area")
public interface AreaClient {

    @GetMapping("list")
    List<AreaVo> list(String parentCode, @RequestParam("level") String level);
}
