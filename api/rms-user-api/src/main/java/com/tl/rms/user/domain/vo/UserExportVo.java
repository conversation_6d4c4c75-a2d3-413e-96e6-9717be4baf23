package com.tl.rms.user.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "用户导出视图对象")
public class UserExportVo {

    public static final String EXPORT_EXCEL_NAME = "用户列表";

    @ExcelProperty(value = "用户工号")
    @ColumnWidth(40)
    private String userCode;

    @ExcelProperty(value = "用户姓名")
    @ColumnWidth(50)
    private String userName;

    @ExcelProperty(value = "事业部")
    @ColumnWidth(50)
    private String buName;

    @ExcelProperty(value = "公司")
    @ColumnWidth(50)
    private String branchName;

    @ExcelProperty(value = "项目/店铺")
    @ColumnWidth(50)
    private String shopName;

    @ExcelProperty(value = "用户类型")
    @ColumnWidth(20)
    private String userTypeStr;

    @ExcelProperty(value = "角色")
    @ColumnWidth(50)
    private String roleName;

    @Schema(description = "状态")
    @ColumnWidth(20)
    private String dutyStatusStr;

    @ExcelProperty(value = "创建时间")
    @ColumnWidth(30)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}