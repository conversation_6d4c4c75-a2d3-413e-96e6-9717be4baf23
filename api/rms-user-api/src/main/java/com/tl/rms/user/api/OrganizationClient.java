package com.tl.rms.user.api;

import com.tl.rms.common.model.validator.group.Create;
import com.tl.rms.common.model.validator.group.Update;
import com.tl.rms.user.domain.vo.OrganizationVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "OrganizationClient", value = "rms-user")
public interface OrganizationClient {

    String PATH = "/organization";

    @GetMapping(PATH + "/list/tree")
    List<OrganizationVo> listTree();

    @GetMapping(PATH + "/{id}")
    OrganizationVo getById(@PathVariable("id") Long id);

    @PostMapping(PATH + "/create")
    boolean create(@RequestBody @Validated(Create.class) OrganizationVo organization);

    @PutMapping(PATH + "/update")
    boolean update(@RequestBody @Validated(Update.class) OrganizationVo organization);

    @DeleteMapping(PATH + "/{id}")
    boolean delete(@PathVariable("id") Long id, @RequestParam("userId") Long userId);

    @PutMapping(PATH + "/status/{id}")
    boolean changeStatus(@PathVariable("id") Long id, @RequestParam("status") Integer status, @RequestParam("userId") Long userId);
}