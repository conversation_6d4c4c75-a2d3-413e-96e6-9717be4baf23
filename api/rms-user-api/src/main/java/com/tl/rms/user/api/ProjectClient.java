package com.tl.rms.user.api;

import com.tl.rms.user.domain.vo.Project4ShopVo;
import com.tl.rms.user.domain.vo.ProjectVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "ProjectClient", value = "rms-user")
public interface ProjectClient {

    String PATH = "/project";

    @GetMapping(PATH + "/list")
    List<ProjectVo> list();

    @GetMapping(PATH + "/getProjectByShopName")
    ProjectVo getProjectByShopName(@RequestParam String shopNick);

    @GetMapping(PATH + "/getProjectByShopId")
    Project4ShopVo getProjectByShopId(@RequestParam(value = "shopId") Long shopId);

    @GetMapping(PATH + "/list4Shop")
    List<Project4ShopVo> list4Shop();
}