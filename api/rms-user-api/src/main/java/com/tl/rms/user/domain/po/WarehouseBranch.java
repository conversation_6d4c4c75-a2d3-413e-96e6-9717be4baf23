package com.tl.rms.user.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("t_warehouse_branch")
public class WarehouseBranch {

    /**
     * 主键
     */
    private Long id;

    /**
     * 仓库ID
     */
    private String warehouseId;

    /**
     * 分公司ID
     */
    private String branchId;

    /**
     * 分公司名称
     */
    private String branchName;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Integer deleted;
}