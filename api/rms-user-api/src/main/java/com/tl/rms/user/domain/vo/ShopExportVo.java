package com.tl.rms.user.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "店铺导出视图对象")
public class ShopExportVo {

    public static final String EXPORT_EXCEL_NAME = "店铺列表";

    @ExcelProperty(value = "店铺编号")
    @ColumnWidth(20)
    private String channelCode;

    @ExcelProperty(value = "店铺简称")
    @ColumnWidth(20)
    private String channelName;

    @ExcelProperty(value = "店铺全称")
    @ColumnWidth(40)
    private String channelFullName;

    @ExcelProperty(value = "平台")
    @ColumnWidth(20)
    private String onlinePlatTypeName;

    @ExcelProperty(value = "公司")
    @ColumnWidth(50)
    private String companyName;

    @ExcelProperty(value = "店铺负责人")
    @ColumnWidth(20)
    private String linkMan;

    @ExcelProperty(value = "授权到期时间")
    @ColumnWidth(30)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime authorizationExpireDate;

    @ExcelProperty(value = "创建时间")
    @ColumnWidth(30)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}