package com.tl.rms.user.domain.vo;

import com.tl.rms.common.model.validator.group.Create;
import com.tl.rms.common.model.validator.group.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "用户视图对象")
public class UserVo implements Serializable {

    @NotNull(message = "用户ID不能为空", groups = {Update.class})
    @Schema(description = "主键ID")
    private Long id;

    @NotBlank(message = "用户工号不能为空", groups = {Create.class, Update.class})
    @Schema(description = "用户工号")
    private String userCode;

    @NotBlank(message = "用户姓名不能为空", groups = {Create.class, Update.class})
    @Schema(description = "用户姓名")
    private String userName;

    @NotBlank(message = "手机号码不能为空", groups = {Create.class, Update.class})
    @Schema(description = "手机号码")
    private String phone;

    @NotBlank(message = "电子邮箱不能为空", groups = {Create.class, Update.class})
    @Schema(description = "电子邮箱")
    private String email;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "盐值")
    private String salt;

    @NotNull(message = "用户类型不能为空", groups = {Create.class, Update.class})
    @Schema(description = "用户类型 1-内部 2-外部")
    private Integer userType;

    @Schema(description = "用户类型 1-内部 2-外部")
    private String userTypeStr;

    @NotNull(message = "用户状态不能为空", groups = {Create.class, Update.class})
    @Schema(description = "用户状态 1-启用 0-停用")
    private Integer userStatus;

    @NotNull(message = "在职状态不能为空", groups = {Create.class, Update.class})
    @Schema(description = "在职状态 1-启用 0-停用")
    private Integer dutyStatus;

    @Schema(description = "在职状态 1-启用 0-停用")
    private String dutyStatusStr;

    @NotNull(message = "分公司ID不能为空", groups = {Create.class, Update.class})
    @Schema(description = "分公司ID")
    private String branchId;

    @Schema(description = "分公司名称")
    private String branchName;

    @NotNull(message = "事业部ID不能为空", groups = {Create.class, Update.class})
    @Schema(description = "事业部ID")
    private String buId;

    @Schema(description = "事业部名称")
    private String buName;

    @Schema(description = "关联店铺")
    private List<Long> shopIds;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "关联角色")
    private List<Long> roleIds;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @NotNull(message = "创建人不能为空", groups = {Create.class})
    @Schema(description = "创建人")
    private Long createBy;

    @NotNull(message = "更新人不能为空", groups = {Update.class})
    @Schema(description = "更新人")
    private Long updateBy;

}