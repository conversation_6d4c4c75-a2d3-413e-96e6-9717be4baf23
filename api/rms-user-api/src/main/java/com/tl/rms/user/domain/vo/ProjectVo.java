package com.tl.rms.user.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "项目信息")
public class ProjectVo {

    @Schema(description = "项目ID")
    private String id;

    @Schema(description = "项目名称")
    private String name;

    @Schema(description = "事业部ID")
    private String buId;

    @Schema(description = "事业部名称")
    private String buName;

}