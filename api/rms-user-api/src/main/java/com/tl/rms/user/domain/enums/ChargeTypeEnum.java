package com.tl.rms.user.domain.enums;

import com.tl.rms.common.model.EnumInfoVo;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 结算方式枚举
 */
@Getter
@RequiredArgsConstructor
public enum ChargeTypeEnum {
    GUARANTEE("1", "担保交易"),
    BANK_RECEIPT("2", "银行收款"),
    CASH_RECEIPT("3", "现金收款"),
    COD("4", "货到付款"),
    DEBT_RECEIPT("5", "欠款计应收"),
    CUSTOMER_PRE_DEPOSIT("6", "客户预存款"),
    MULTI_SETTLEMENT("7", "多种结算"),
    RETURN_AND_REFUND("8", "退换货冲抵"),
    ELECTRONIC_WALLET("9", "电子钱包");

    private final String value;
    private final String label;

    public static List<EnumInfoVo> list() {
        return Arrays.stream(values())
                .map(e -> new EnumInfoVo(e.getValue(), e.getLabel()))
                .collect(Collectors.toList());
    }

    public static ChargeTypeEnum getByValue(String value) {
        return Arrays.stream(values())
                .filter(e -> e.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
}