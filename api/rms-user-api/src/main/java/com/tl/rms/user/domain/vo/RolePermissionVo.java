package com.tl.rms.user.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "角色权限关联视图对象")
public class RolePermissionVo implements Serializable {

    @NotNull(message = "角色ID不能为空")
    @Schema(description = "角色ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long roleId;

    @NotNull(message = "权限ID列表不能为空")
    @Size(min = 1, message = "至少需要一个权限ID")
    @Schema(description = "权限ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> permissionIds;

    @Schema(description = "操作人ID", hidden = true)
    private Long operatorId;
}