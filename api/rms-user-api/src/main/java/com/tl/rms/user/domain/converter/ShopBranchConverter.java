package com.tl.rms.user.domain.converter;

import com.tl.rms.user.domain.po.ShopBranch;
import com.tl.rms.user.domain.vo.ShopBranchVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ShopBranchConverter {

    ShopBranchConverter MAPPER = Mappers.getMapper(ShopBranchConverter.class);

    ShopBranchVo toVo(ShopBranch po);

    ShopBranch toPo(ShopBranchVo vo);

    List<ShopBranchVo> toVoList(List<ShopBranch> poList);

    List<ShopBranch> toPoList(List<ShopBranchVo> voList);
}