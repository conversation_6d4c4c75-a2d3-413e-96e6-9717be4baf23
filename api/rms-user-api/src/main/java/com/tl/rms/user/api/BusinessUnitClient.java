package com.tl.rms.user.api;

import com.tl.rms.user.domain.vo.BusinessUnitVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "BusinessUnitClient", value = "rms-user")
public interface BusinessUnitClient {

    String PATH = "/business-unit";

    @GetMapping(PATH + "/list")
    List<BusinessUnitVo> list();

    @GetMapping(PATH + "/getBuNameByProjectId")
    String getBuNameByProjectId(@RequestParam String projectId);

}