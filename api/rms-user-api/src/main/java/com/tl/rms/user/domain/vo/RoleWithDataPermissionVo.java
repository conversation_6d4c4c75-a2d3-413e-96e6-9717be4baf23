package com.tl.rms.user.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "角色与数据权限视图对象")
public class RoleWithDataPermissionVo implements Serializable {

    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "数据权限状态 0-不开启 1-开启")
    private Integer dataPermissionStatus;

    @Schema(description = "数据权限配置列表")
    private List<RoleDataPermissionVo> dataPermissionConfigs;
}