package com.tl.rms.user.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "店铺分公司关联信息")
public class ShopBranchVo {

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "分公司ID")
    private String branchId;

    @Schema(description = "分公司名称")
    private String branchName;

    @Schema(description = "是否主要分公司")
    private Boolean isPrimary;
}