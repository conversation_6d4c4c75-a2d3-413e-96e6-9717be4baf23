package com.tl.rms.user.domain.vo;

import com.tl.rms.common.model.PageParamsVo;
import com.tl.rms.user.domain.po.User;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户查询请求对象")
public class UserQueryReqVo extends PageParamsVo<User> implements Serializable {

    @Schema(description = "用户编码")
    private String userCode;

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "分公司")
    private String branchId;

    @Schema(description = "事业部")
    private String buId;

    @Schema(description = "店铺")
    private Long shopId;

    @Schema(description = "用户状态 1-启用 0-停用")
    private Integer userStatus;

    @Schema(description = "在职状态 1-在职 0-离职")
    private Integer dutyStatus;

    @Schema(description = "创建时间开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间结束")
    private LocalDateTime createTimeEnd;
}