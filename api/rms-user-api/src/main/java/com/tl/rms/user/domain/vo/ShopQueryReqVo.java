package com.tl.rms.user.domain.vo;

import com.tl.rms.common.model.PageParamsVo;
import com.tl.rms.user.domain.po.Shop;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "店铺查询请求对象")
public class ShopQueryReqVo extends PageParamsVo<Shop> implements Serializable {

    @Schema(description = "店铺编码(渠道编码)")
    private String channelCode;

    @Schema(description = "店铺简称(销售渠道名称)")
    private String channelName;

    @Schema(description = "店铺全称")
    private String channelFullName;

    @Schema(description = "渠道平台")
    private String onlinePlatTypeCode;

    @Schema(description = "所属公司ID")
    private String companyId;

    @Schema(description = "联系人ID")
    private Long linkManId;

    @Schema(description = "创建时间开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "是否支持分货 0-否 1-是")
    private Integer isDistributionSupport;

    @Schema(description = "本项目", hidden = true)
    private List<String> dataPermissionProjectIds;

    @Schema(description = "本公司", hidden = true)
    private String dataPermissionBranchId;

    @Schema(description = "本部门", hidden = true)
    private String dataPermissionBusinessUnitId;
}