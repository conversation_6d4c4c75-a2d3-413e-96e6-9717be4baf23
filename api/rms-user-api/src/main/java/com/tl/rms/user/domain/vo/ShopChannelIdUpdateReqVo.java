package com.tl.rms.user.domain.vo;

import com.tl.rms.common.model.validator.group.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "更新渠道ID请求")
public class ShopChannelIdUpdateReqVo {

    @Schema(description = "ID")
    @NotNull(message = "ID不能为空", groups = {Update.class})
    private Long id;

    @Schema(description = "渠道ID")
    @NotNull(message = "渠道ID不能为空", groups = {Update.class})
    private Long channelId;
}