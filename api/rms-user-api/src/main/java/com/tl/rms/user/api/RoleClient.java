package com.tl.rms.user.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tl.rms.common.model.validator.group.Create;
import com.tl.rms.common.model.validator.group.Update;
import com.tl.rms.user.domain.vo.RoleQueryReqVo;
import com.tl.rms.user.domain.vo.RoleVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(contextId = "RoleClient", value = "rms-user")
public interface RoleClient {

    String PATH = "/role";

    @PostMapping(PATH + "/page")
    Page<RoleVo> page(@RequestBody RoleQueryReqVo queryVo);

    @GetMapping(PATH + "/list")
    List<RoleVo> list();

    @GetMapping(PATH + "/{id}")
    RoleVo getRoleById(@PathVariable("id") Long id);

    @PostMapping(PATH + "/create")
    boolean create(@RequestBody @Validated(Create.class) RoleVo role);

    @PutMapping(PATH + "/update")
    boolean update(@RequestBody @Validated(Update.class) RoleVo role);
}