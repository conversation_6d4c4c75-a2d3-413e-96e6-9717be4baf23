package com.tl.rms.user.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tl.rms.common.model.validator.group.Create;
import com.tl.rms.common.model.validator.group.Update;
import com.tl.rms.user.domain.po.User;
import com.tl.rms.user.domain.vo.UserDataPermissionReqVo;
import com.tl.rms.user.domain.vo.UserDataPermissionVo;
import com.tl.rms.user.domain.vo.UserQueryReqVo;
import com.tl.rms.user.domain.vo.UserVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "UserClient", value = "rms-user")
public interface UserClient {

    String PATH = "/user";

    @PostMapping(PATH + "/page")
    Page<UserVo> page(@RequestBody UserQueryReqVo queryVo);

    @GetMapping(PATH + "/list")
    List<UserVo> list();

    @GetMapping(PATH + "/getUserById/{id}")
    UserVo getUserById(@PathVariable("id") Long id);

    @GetMapping(PATH + "/getUserByCode/{userCode}")
    UserVo getUserByCode(@PathVariable("userCode") String userCode);

    @GetMapping(PATH + "/getUserByPhone/{phone}")
    User getUserByPhone(@PathVariable("phone") String phone);

    @PostMapping(PATH + "/create")
    boolean create(@RequestBody @Validated(Create.class) UserVo role);

    @PutMapping(PATH + "/update")
    boolean update(@RequestBody @Validated(Update.class) UserVo role);

    @PostMapping(PATH + "/resetPwd")
    boolean resetPwd(@RequestParam("id") Long id, @RequestParam("pwd") String pwd);

    @PostMapping(PATH + "/getUserByCodes")
    List<UserVo> getUserByCodes(@RequestBody List<String> userCodeList);

    @PostMapping(PATH + "/getUserDataPermission")
    UserDataPermissionVo getUserDataPermission(@RequestBody UserDataPermissionReqVo reqVo);

    @PostMapping(PATH + "/getUserByIds")
    List<UserVo> getUserByIds(@RequestBody List<Long> userIdList);
}