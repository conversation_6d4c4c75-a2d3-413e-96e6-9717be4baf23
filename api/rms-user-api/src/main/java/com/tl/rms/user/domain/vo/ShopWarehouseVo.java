package com.tl.rms.user.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "店铺发货仓库信息")
public class ShopWarehouseVo {

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "发货仓库ID")
    private String warehouseId;

    @Schema(description = "发货仓库名称")
    private String warehouseName;

    @Schema(description = "发货地址")
    private String deliveryAddress;

    @Schema(description = "是否默认仓库")
    private Boolean isPrimary;
} 