package com.tl.rms.user.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "角色导出视图对象")
public class RoleExportVo {

    public static final String EXPORT_EXCEL_NAME = "角色列表";

    @ExcelProperty(value = "角色编码")
    @ColumnWidth(20)
    private String roleCode;

    @ExcelProperty(value = "角色名称")
    @ColumnWidth(20)
    private String roleName;

    @ExcelProperty(value = "角色描述")
    @ColumnWidth(40)
    private String description;

    @ExcelProperty(value = "状态")
    @ColumnWidth(10)
    private String statusDesc;

    @ExcelProperty(value = "创建时间")
    @ColumnWidth(30)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}