package com.tl.rms.user.domain.converter;

import com.tl.rms.user.domain.po.RoleDataPermission;
import com.tl.rms.user.domain.vo.RoleDataPermissionVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface RoleDataPermissionConverter {

    RoleDataPermissionConverter MAPPER = Mappers.getMapper(RoleDataPermissionConverter.class);

    RoleDataPermissionVo toVo(RoleDataPermission po);

    RoleDataPermission toPo(RoleDataPermissionVo vo);

    List<RoleDataPermissionVo> toVoList(List<RoleDataPermission> poList);

    List<RoleDataPermission> toPoList(List<RoleDataPermissionVo> voList);
}
