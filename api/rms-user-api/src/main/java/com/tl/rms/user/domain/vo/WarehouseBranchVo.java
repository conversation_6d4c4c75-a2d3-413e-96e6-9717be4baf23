package com.tl.rms.user.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "仓库分公司关联信息")
public class WarehouseBranchVo {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "仓库ID")
    private String warehouseId;

    @Schema(description = "分公司ID")
    private String branchId;

    @Schema(description = "分公司名称")
    private String branchName;
}