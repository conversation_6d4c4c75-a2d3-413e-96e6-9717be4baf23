package com.tl.rms.user.domain.vo;

import com.tl.rms.common.model.validator.group.Create;
import com.tl.rms.common.model.validator.group.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

@Data
@Schema(description = "店铺信息")
public class ShopVo {

    @Schema(description = "店铺ID")
    @NotNull(message = "店铺ID不能为空", groups = {Update.class})
    private Long id;

    @Schema(description = "渠道ID")
    private Long channelId;

    @Schema(description = "店铺编码(渠道编码)")
    @NotBlank(message = "店铺编码不能为空", groups = {Create.class, Update.class})
    private String channelCode;

    @Schema(description = "店铺简称(销售渠道名称)")
    @NotBlank(message = "店铺简称不能为空", groups = {Create.class, Update.class})
    private String channelName;

    @Schema(description = "店铺全称")
    private String channelFullName;

    @Schema(description = "渠道类型(店铺类型) 0-分销办公室 1-直营网店 2-直营门店 3-销售办公室 4-货主虚拟店 5-分销虚拟店 6-加盟门店 7-内部交易渠道")
    @NotBlank(message = "渠道类型不能为空", groups = {Create.class, Update.class})
    private String channelType;

    @Schema(description = "部门ID")
    private String channelDepartId;

    @Schema(description = "部门编码")
    private String channelDepartCode;

    @Schema(description = "部门名称")
    private String channelDepartName;

    @Schema(description = "联系人ID")
    private Long linkManId;

    @Schema(description = "联系人")
    private String linkMan;

    @Schema(description = "联系电话")
    private String linkTel;

    @Schema(description = "店铺平台编码")
    @NotBlank(message = "店铺平台不能为空", groups = {Create.class, Update.class})
    private String onlinePlatTypeCode;

    @Schema(description = "店铺平台名称")
    private String onlinePlatTypeName;

    @Schema(description = "公司id(吉客云公司Code)")
    @NotBlank(message = "公司编码不能为空", groups = {Create.class, Update.class})
    private String companyId;

    @Schema(description = "公司名称(吉客云)")
    @NotBlank(message = "公司名称不能为空", groups = {Create.class, Update.class})
    private String companyName;

    @Schema(description = "渠道分类id")
    private Long cateId;

    @Schema(description = "渠道分类")
    private String cateName;

    @Schema(description = "客户编码")
    private String customerId;

    @Schema(description = "所属中心ID")
    private String centerId;

    @Schema(description = "所属项目ID(吉客云部门Code)")
    @NotBlank(message = "所属项目ID不能为空", groups = {Create.class, Update.class})
    private String projectId;

    @Schema(description = "所属项目名称(吉客云部门名称)")
    private String projectName;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "销售商品分组id")
    private Long groupId;

    @Schema(description = "店铺授权截止日期")
    private LocalDateTime authorizationExpireDate;

    @Schema(description = "是否授权过期")
    private Boolean isAuthorizationExpired;

    @Schema(description = "授权过期天数")
    private Integer authorizationExpireDays;

    @Schema(description = "是否允许手动销售")
    private Boolean isAutoSales;

    @Schema(description = "销售单接单开始时间")
    private LocalDateTime salesOrderStartTime;

    @Schema(description = "销售单接单结束时间")
    private LocalDateTime salesOrderEndTime;

    @Schema(description = "默认仓库ID")
    private String warehouseId;

    @Schema(description = "默认仓库编码")
    private String warehouseCode;

    @Schema(description = "默认仓库名称")
    private String warehouseName;

    @Schema(description = "结算方式 1-担保交易 2-银行收款 3-现金收款 4-货到付款 5-欠款计应收 6-客户预存款 7-多种结算 8-退换货冲抵 9-电子钱包")
    private String chargeType;

    @Schema(description = "国家id")
    private Integer countryId;

    @Schema(description = "国家")
    private String countryName;

    @Schema(description = "省id")
    private Integer provinceId;

    @Schema(description = "省")
    private String provinceName;

    @Schema(description = "市id")
    private Integer cityId;

    @Schema(description = "市")
    private String cityName;

    @Schema(description = "区id")
    private Integer townId;

    @Schema(description = "镇，区")
    private String townName;

    @Schema(description = "街道id")
    private Integer streetId;

    @Schema(description = "街道")
    private String streetName;

    @Schema(description = "办公地址")
    private String officeAddress;

    @Schema(description = "邮编")
    private String postcode;

    @Schema(description = "备注")
    private String memo;

    @Schema(description = "状态 1-启用 0-禁用")
    private Boolean status;

    @Schema(description = "同步吉客云状态 1-新增未同步 2-更新未同步 3-同步成功")
    private Integer syncJkyStatus;

    @Schema(description = "同步吉客云时间")
    private LocalDateTime syncJkyTime;

    @Schema(description = "是否支持分货 0-否 1-是")
    private Integer isDistributionSupport;

    @Schema(description = "是否一盘货 0-否 1-是")
    private Integer isPalletGoods;

    @Schema(description = "创建人ID", hidden = true)
    private Long createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人ID", hidden = true)
    private Long updateBy;

    @Schema(description = "关联的分公司列表")
    private List<ShopBranchVo> shopBranchList;

    @Schema(description = "发货仓库列表")
    private List<ShopWarehouseVo> shopWarehouseList;

    @Schema(description = "收款账户列表")
    private List<ShopPaymentAccountVo> shopPaymentAccountList;

    public Boolean getIsAuthorizationExpired() {
        return authorizationExpireDate != null && authorizationExpireDate.isBefore(LocalDateTime.now());
    }

    public Integer getAuthorizationExpireDays() {
        if (authorizationExpireDate == null) {
            return null;
        }

        // 获取当前日期时间
        LocalDateTime currentDateTime = LocalDateTime.now();

        // 计算相差的秒数
        long secondsDifference = ChronoUnit.SECONDS.between(currentDateTime, authorizationExpireDate);

        if (secondsDifference > 0) {
            // 未过期，不足一天也按一天算
            return (int) (secondsDifference / (24 * 60 * 60) + (secondsDifference % (24 * 60 * 60) > 0 ? 1 : 0));
        } else if (secondsDifference < 0) {
            // 已过期，不足一天也按一天算
            return (int) (secondsDifference / (24 * 60 * 60) - (secondsDifference % (24 * 60 * 60) != 0 ? 1 : 0));
        } else {
            return 0;
        }
    }
}