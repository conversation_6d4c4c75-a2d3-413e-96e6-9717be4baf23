package com.tl.rms.user.domain.vo;

import com.tl.rms.common.model.validator.group.Create;
import com.tl.rms.common.model.validator.group.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Schema(description = "角色视图对象")
public class RoleVo implements Serializable {

    @NotNull(message = "角色ID不能为空", groups = {Update.class})
    @Schema(description = "主键ID")
    private Long id;

    @NotBlank(message = "角色编码不能为空", groups = {Create.class, Update.class})
    @Schema(description = "角色编码")
    private String roleCode;

    @NotBlank(message = "角色名称不能为空", groups = {Create.class, Update.class})
    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色描述")
    private String description;

    @NotNull(message = "状态不能为空", groups = {Create.class, Update.class})
    @Schema(description = "状态 1-启用 0-停用")
    private Integer status;

    @Schema(description = "数据权限状态 0-不开启 1-开启")
    private Integer dataPermissionStatus;

    @Schema(description = "创建人ID", hidden = true)
    private Long createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人ID", hidden = true)
    private Long updateBy;
}