package com.tl.rms.user.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "分公司信息")
public class BranchVo {

    @Schema(description = "分公司ID")
    private String id;

    @Schema(description = "分公司名称")
    private String name;

    @Schema(description = "分公司简称")
    private String shortName;

    @Schema(description = "分公司全称")
    private String fullName;
}