package com.tl.rms.user.domain.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("t_user")
public class User {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户工号
     */
    @TableField("user_code")
    private String userCode;

    /**
     * 用户姓名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 电子邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 盐值
     */
    @TableField("salt")
    private String salt;

    /**
     * 用户类型 1-内部 2-外部
     */
    @TableField("user_type")
    private Integer userType;

    /**
     * 用户状态 1-启用 0-停用
     */
    @TableField("user_status")
    private Integer userStatus;

    /**
     * 在职状态 1-在职 0-离职
     */
    @TableField("duty_status")
    private Integer dutyStatus;

    /**
     * 分公司ID
     */
    @TableField("branch_id")
    private String branchId;

    /**
     * 事业部ID
     */
    @TableField("bu_id")
    private String buId;

    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

}