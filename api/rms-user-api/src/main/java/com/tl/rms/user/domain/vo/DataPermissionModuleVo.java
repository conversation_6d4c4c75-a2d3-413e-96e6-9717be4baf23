package com.tl.rms.user.domain.vo;

import com.tl.rms.common.model.EnumInfoVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "数据权限模块")
public class DataPermissionModuleVo implements Serializable {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "模块编码")
    private String moduleCode;

    @Schema(description = "模块名称")
    private String moduleName;

    @Schema(description = "创建人ID")
    private Long createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人ID")
    private Long updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "数据权限级别")
    private List<EnumInfoVo> dataPermissionLevels;
} 