package com.tl.rms.user.domain.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("t_project")
public class Project {

    /**
     * 主键 项目ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 项目名称
     */
    @TableField("name")
    private String name;

    /**
     * 主键 分公司ID
     */
    @TableField("bu_id")
    private String buId;

    /**
     * 创建人
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

}