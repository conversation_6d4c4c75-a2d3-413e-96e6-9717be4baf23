package com.tl.rms.user.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("t_organization")
public class Organization {
    /**
     * 主键
     */
    private Long id;

    /**
     * 组织编码
     */
    private String code;

    /**
     * 组织名称
     */
    private String name;

    /**
     * 组织简介
     */
    private String description;

    /**
     * 上级组织ID
     */
    private Long parentId;

    /**
     * 是否总部 0-否 1-是
     */
    private Integer isHq;

    /**
     * 组织层级 1-总公司 2-分公司 3-事业部
     */
    private Integer level;

    /**
     * 状态 1-启用 0-停用
     */
    private Integer status;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Integer deleted;
}