package com.tl.rms.user.domain.vo;

import com.tl.rms.common.model.validator.group.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "吉客云店铺同步状态更新请求")
public class ShopSyncStatusUpdateReqVo {

    @Schema(description = "店铺ID")
    @NotNull(message = "店铺ID不能为空", groups = {Update.class})
    private Long id;

    @Schema(description = "同步状态")
    @NotNull(message = "同步状态不能为空", groups = {Update.class})
    private Integer syncJkyStatus;
}