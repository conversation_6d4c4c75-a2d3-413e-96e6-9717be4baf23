package com.tl.rms.user.domain.converter;

import com.tl.rms.user.domain.po.Permission;
import com.tl.rms.user.domain.vo.PermissionVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PermissionConverter {

    PermissionConverter MAPPER = Mappers.getMapper(PermissionConverter.class);

    PermissionVo toVo(Permission po);

    Permission toPo(PermissionVo vo);

    List<PermissionVo> toVoList(List<Permission> poList);

    List<Permission> toPoList(List<PermissionVo> voList);
} 