package com.tl.rms.user.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "店铺收款账户信息")
public class ShopPaymentAccountVo {

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "银行账户ID")
    private String bankAccountId;

    @Schema(description = "银行账户")
    private String bankAccount;

    @Schema(description = "银行名称")
    private String bankName;

    @Schema(description = "银行地址")
    private String bankAddress;

    @Schema(description = "是否主要账户")
    private Boolean isPrimary;
}