package com.tl.rms.user.domain.converter;

import com.tl.rms.user.domain.po.Branch;
import com.tl.rms.user.domain.vo.BranchVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BranchConverter {

    BranchConverter MAPPER = Mappers.getMapper(BranchConverter.class);

    BranchVo toVo(Branch po);

    Branch toPo(BranchVo vo);

    List<BranchVo> toVoList(List<Branch> poList);

    List<Branch> toPoList(List<BranchVo> voList);
}