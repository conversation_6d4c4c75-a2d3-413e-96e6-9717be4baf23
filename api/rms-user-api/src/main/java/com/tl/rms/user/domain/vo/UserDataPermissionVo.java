package com.tl.rms.user.domain.vo;

import java.util.List;

import com.tl.rms.user.domain.enums.DataPermissionLevelEnum;
import com.tl.rms.user.domain.enums.DataPermissionModuleEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "用户数据权限")
public class UserDataPermissionVo {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "数据权限模块")
    private DataPermissionModuleEnum dataPermissionModuleEnum;

    @Schema(description = "数据权限级别")
    private DataPermissionLevelEnum dataPermissionLevelEnum;

    @Schema(description = "分公司ID")
    private String branchId;

    @Schema(description = "部门ID")
    private String businessUnitId;

    @Schema(description = "项目ID")
    private List<String> projectIds;
}
