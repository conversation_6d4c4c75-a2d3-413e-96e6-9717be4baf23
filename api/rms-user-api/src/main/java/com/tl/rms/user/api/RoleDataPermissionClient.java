package com.tl.rms.user.api;

import com.tl.rms.user.domain.vo.DataPermissionModuleVo;
import com.tl.rms.user.domain.vo.RoleDataPermissionConfigReqVo;
import com.tl.rms.user.domain.vo.RoleDataPermissionVo;
import com.tl.rms.user.domain.vo.RoleWithDataPermissionVo;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

@FeignClient(contextId = "RoleDataPermissionClient", value = "rms-user")
public interface RoleDataPermissionClient {

    String PATH = "/role/data/permission";

    @GetMapping(PATH + "/modules")
    List<DataPermissionModuleVo> listAllModules();

    @GetMapping(PATH + "/{roleId}")
    RoleWithDataPermissionVo getRoleWithDataPermissionByRoleId(@PathVariable Long roleId);

    @PostMapping(PATH + "/config")
    void config(@RequestBody @Validated RoleDataPermissionConfigReqVo roleDataPermissionConfigReqVo);

    @GetMapping(PATH + "/listDataPermissions/{userId}")
    List<RoleDataPermissionVo> listDataPermissionsByUserId(@PathVariable Long userId);
}