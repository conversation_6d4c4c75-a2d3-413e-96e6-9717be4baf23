package com.tl.rms.user.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("t_warehouse")
public class Warehouse {

    /**
     * 主键 仓库ID
     */
    private String id;

    /**
     * 仓库名称
     */
    private String name;

    /**
     * 所属分公司编码
     */
    private String orgCode;

    /**
     * 所属分公司
     */
    private String orgName;

    /**
     * 仓库物权
     */
    private String whOrgName;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 联系方式
     */
    private String contactWay;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 是否活跃 0-否 1-是
     */
    private Integer isActive;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Integer deleted;

}