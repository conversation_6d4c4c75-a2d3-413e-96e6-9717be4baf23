package com.tl.rms.user.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "用户信息视图对象")
public class UserWithPermissionVo implements Serializable {

    @Schema(description = "用户ID, 匿名用户也存在用户ID")
    private Long userId;

    @Schema(description = "登录名")
    private String userCode;

    @Schema(description = "用户姓名")
    private String userName;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "是否需要修改密码")
    private Boolean needUpdatePassword = false;

    @Schema(description = "上一次登录的时间")
    private String lastLoginTime;

    @Schema(description = "权限列表")
    private List<PermissionVo> permissions;
}