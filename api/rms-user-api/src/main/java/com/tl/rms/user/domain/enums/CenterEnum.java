package com.tl.rms.user.domain.enums;

import com.tl.rms.common.exception.CommonException;
import com.tl.rms.common.model.EnumInfoVo;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 所属中心枚举
 */
@Getter
@RequiredArgsConstructor
public enum CenterEnum {
    ONLINE_RETAIL_CENTER("ONLINE_RETAIL_CENTER", "线上零售中心"),
    HW_SMART_SELECT_CENTER("HW_SMART_SELECT_CENTER", "智选产品中心"),
    ;

    private final String value;
    private final String label;

    public static CenterEnum getByValue(String value) {
        return Arrays.stream(values())
                .filter(e -> e.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }

    /**
     * 将枚举信息转为下拉列表
     * @return
     */
    public static List<EnumInfoVo> list() {
        return Arrays.stream(values())
                .map(e -> new EnumInfoVo(e.getValue(), e.getLabel()))
                .collect(Collectors.toList());
    }

    public static void check(String industryCategoryCenter) {
        if (getByValue(industryCategoryCenter) == null) {
            throw new CommonException("industryCategoryCenter is illegal");
        }
    }

    public static CenterEnum getByLabel(String center) {
        return Arrays.stream(values())
                .filter(e -> e.getLabel().equals(center))
                .findFirst()
                .orElse(null);
    }
}