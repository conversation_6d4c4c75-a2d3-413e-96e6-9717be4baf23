package com.tl.rms.user.domain.enums;

import com.tl.rms.common.model.EnumInfoVo;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 店铺平台枚举
 */
@Getter
@RequiredArgsConstructor
public enum OnlinePlatTypeEnum {
    TMALL("TMALL", "天猫商城"),
    JD("JD", "京东"),
    DOUYIN("DOUYIN", "抖音"),
    FXG("FXG", "放心购（抖音小店）"),
    PDD("PDD", "拼多多"),
    KSXD("KSXD", "快手小店"),
    TB("TB", "淘宝"),
    DouYinXiaoShiDa("DouYinXiaoShiDa", "抖店即时零售"),
    WXSPHXD("WXSPHXD", "微信小店"),
    WXXSD("WXXSD", "微信小商店"),
    XHS("XHS", "小红书"),
    OTHER("OTHER", "其他");

    private final String value;
    private final String label;

    public static List<EnumInfoVo> list() {
        return Arrays.stream(values())
                .map(e -> new EnumInfoVo(e.getValue(), e.getLabel()))
                .collect(Collectors.toList());
    }

    public static OnlinePlatTypeEnum getByValue(String value) {
        return Arrays.stream(values())
                .filter(e -> e.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }

    public static String getNameByValue(String value) {
        OnlinePlatTypeEnum onlinePlatTypeEnum = getByValue(value);
        if (onlinePlatTypeEnum == null) {
            return null;
        }
        return onlinePlatTypeEnum.getLabel();
    }
} 