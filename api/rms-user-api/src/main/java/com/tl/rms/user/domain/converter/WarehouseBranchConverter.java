package com.tl.rms.user.domain.converter;

import com.tl.rms.user.domain.po.WarehouseBranch;
import com.tl.rms.user.domain.vo.WarehouseBranchVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface WarehouseBranchConverter {

    WarehouseBranchConverter MAPPER = Mappers.getMapper(WarehouseBranchConverter.class);

    WarehouseBranchVo toVo(WarehouseBranch po);

    WarehouseBranch toPo(WarehouseBranchVo vo);

    List<WarehouseBranchVo> toVoList(List<WarehouseBranch> poList);

    List<WarehouseBranch> toPoList(List<WarehouseBranchVo> voList);
}