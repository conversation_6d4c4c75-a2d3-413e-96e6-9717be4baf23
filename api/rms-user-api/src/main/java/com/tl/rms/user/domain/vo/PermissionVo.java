package com.tl.rms.user.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "权限视图对象")
public class PermissionVo implements Serializable {

    @Schema(description = "权限ID")
    private Long id;

    @Schema(description = "权限编码")
    private String permissionCode;

    @Schema(description = "权限名称")
    private String permissionName;

    @Schema(description = "权限类型 1-菜单 2-按钮 3-接口")
    private Integer permissionType;

    @Schema(description = "父权限ID")
    private Long parentId;

    @Schema(description = "权限路径")
    private String path;

    @Schema(description = "权限图标")
    private String icon;

    @Schema(description = "状态 1-启用 0-停用")
    private Integer status;

    @Schema(description = "排序(控制菜单显示顺序，值越大越靠后)")
    private Integer sort;

    @Schema(description = "创建人ID", hidden = true)
    private Long createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人ID", hidden = true)
    private Long updateBy;

    @Schema(description = "子权限列表")
    private List<PermissionVo> children;
} 