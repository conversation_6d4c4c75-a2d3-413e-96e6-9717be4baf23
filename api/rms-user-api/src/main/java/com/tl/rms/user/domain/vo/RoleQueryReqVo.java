package com.tl.rms.user.domain.vo;

import com.tl.rms.common.model.PageParamsVo;
import com.tl.rms.user.domain.po.Role;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "角色查询请求对象")
public class RoleQueryReqVo extends PageParamsVo<Role> implements Serializable {

    @Schema(description = "角色编码")
    private String roleCode;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "状态 1-启用 0-停用")
    private Integer status;

    @Schema(description = "创建时间开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间结束")
    private LocalDateTime createTimeEnd;
}