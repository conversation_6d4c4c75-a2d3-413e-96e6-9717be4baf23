package com.tl.rms.user.domain.converter;

import com.tl.rms.user.domain.po.Project;
import com.tl.rms.user.domain.vo.ProjectVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ProjectConverter {

    ProjectConverter MAPPER = Mappers.getMapper(ProjectConverter.class);

    ProjectVo toVo(Project po);

    Project toPo(ProjectVo vo);

    List<ProjectVo> toVoList(List<Project> poList);

    List<Project> toPoList(List<ProjectVo> voList);
}