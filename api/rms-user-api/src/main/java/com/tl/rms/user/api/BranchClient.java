package com.tl.rms.user.api;

import com.tl.rms.user.domain.vo.Branch4ProjectIdVo;
import com.tl.rms.user.domain.vo.BranchVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Set;

@FeignClient(contextId = "BranchClient", value = "rms-user")
public interface BranchClient {

    String PATH = "/branch";

    @GetMapping(PATH + "/list")
    List<BranchVo> list();

    @PostMapping(PATH + "/listByProjectIds")
    List<Branch4ProjectIdVo> listByProjectIds(@RequestBody Set<String> projectIdSet);
}