package com.tl.rms.user.domain.vo;

import com.tl.rms.common.model.PageParamsVo;
import com.tl.rms.user.domain.po.Warehouse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "仓库查询请求对象")
public class WarehouseQueryReqVo extends PageParamsVo<Warehouse> implements Serializable {

    @Schema(description = "仓库ID(仓库编码)")
    private String id;

    @Schema(description = "仓库名称")
    private String name;

    @Schema(description = "所属分公司编码(货主)")
    private String orgCode;

    @Schema(description = "联系人")
    private String contactName;

    @Schema(description = "联系方式")
    private String contactWay;

    @Schema(description = "创建时间开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间结束")
    private LocalDateTime createTimeEnd;
}