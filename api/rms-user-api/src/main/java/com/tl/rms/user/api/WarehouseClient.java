package com.tl.rms.user.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tl.rms.user.domain.po.Warehouse;
import com.tl.rms.user.domain.vo.WarehouseQueryReqVo;
import com.tl.rms.user.domain.vo.WarehouseVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "WarehouseClient", value = "rms-user")
public interface WarehouseClient {

    String PATH = "/warehouse";

    @PostMapping(PATH + "/page")
    Page<WarehouseVo> page(@RequestBody WarehouseQueryReqVo queryVo);

    @GetMapping(PATH + "/list")
    List<WarehouseVo> list();

    @GetMapping(PATH + "/getWarehouseById")
    WarehouseVo getWarehouseById(@RequestParam String id);

    @GetMapping(PATH + "/getByName")
    Warehouse getByName(@RequestParam String name);

    @PostMapping(PATH + "/queryByNames")
    List<WarehouseVo> queryByNames(@RequestBody List<String> names);

    @PostMapping(PATH + "/queryByIds")
    List<WarehouseVo> queryByIds(@RequestBody List<String> ids);

    @PostMapping(PATH + "/sync")
    Boolean sync(@RequestBody WarehouseVo warehouseVo);
}