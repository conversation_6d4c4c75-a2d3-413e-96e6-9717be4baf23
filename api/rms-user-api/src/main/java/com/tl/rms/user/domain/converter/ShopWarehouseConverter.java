package com.tl.rms.user.domain.converter;

import com.tl.rms.user.domain.po.ShopWarehouse;
import com.tl.rms.user.domain.vo.ShopWarehouseVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ShopWarehouseConverter {

    ShopWarehouseConverter MAPPER = Mappers.getMapper(ShopWarehouseConverter.class);

    ShopWarehouseVo toVo(ShopWarehouse po);

    ShopWarehouse toPo(ShopWarehouseVo vo);

    List<ShopWarehouseVo> toVoList(List<ShopWarehouse> poList);

    List<ShopWarehouse> toPoList(List<ShopWarehouseVo> voList);
}