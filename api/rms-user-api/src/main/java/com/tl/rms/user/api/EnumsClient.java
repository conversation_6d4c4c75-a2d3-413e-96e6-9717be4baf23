package com.tl.rms.user.api;

import com.tl.rms.common.model.EnumInfoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;
import java.util.Map;

@FeignClient(contextId = "EnumsClient", value = "rms-user")
public interface EnumsClient {

    String PATH = "/enums";

    @GetMapping(PATH + "/all")
    Map<String, List<EnumInfoVo>> getEnums();
}