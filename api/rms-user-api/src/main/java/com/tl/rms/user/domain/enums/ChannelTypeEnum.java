package com.tl.rms.user.domain.enums;

import com.tl.rms.common.model.EnumInfoVo;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 店铺类型枚举
 */
@Getter
@RequiredArgsConstructor
public enum ChannelTypeEnum {
    DISTRIBUTION_OFFICE("0", "分销办公室"),
    DIRECT_ONLINE_SHOP("1", "直营网店"),
    DIRECT_STORE("2", "直营门店"),
    SALES_OFFICE("3", "销售办公室"),
    VIRTUAL_STORE_OWNER("4", "货主虚拟店"),
    VIRTUAL_DISTRIBUTION_STORE("5", "分销虚拟店"),
    JOIN_STORE("6", "加盟门店"),
    INTERNAL_TRADE_CHANNEL("7", "内部交易渠道");

    private final String value;
    private final String label;

    public static List<EnumInfoVo> list() {
        return Arrays.stream(values())
                .map(e -> new EnumInfoVo(e.getValue(), e.getLabel()))
                .collect(Collectors.toList());
    }

    public static ChannelTypeEnum getByValue(String value) {
        return Arrays.stream(values())
                .filter(e -> e.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }
}