package com.tl.rms.user.domain.converter;

import com.tl.rms.user.domain.po.User;
import com.tl.rms.user.domain.vo.UserExportVo;
import com.tl.rms.user.domain.vo.UserVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface UserConverter {

    UserConverter MAPPER = Mappers.getMapper(UserConverter.class);

    UserVo toVo(User po);

    User toPo(UserVo vo);

    List<UserVo> toVoList(List<User> poList);

    List<User> toPoList(List<UserVo> voList);

    List<UserExportVo> toExportVoList(List<UserVo> voList);
} 