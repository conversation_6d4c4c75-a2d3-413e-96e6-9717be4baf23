package com.tl.rms.user.domain.converter;

import com.tl.rms.user.domain.po.Organization;
import com.tl.rms.user.domain.vo.OrganizationVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface OrganizationConverter {

    OrganizationConverter MAPPER = Mappers.getMapper(OrganizationConverter.class);

    OrganizationVo toVo(Organization po);

    List<OrganizationVo> toVoList(List<Organization> poList);

    Organization toPo(OrganizationVo vo);

    List<Organization> toPoList(List<OrganizationVo> voList);
}
