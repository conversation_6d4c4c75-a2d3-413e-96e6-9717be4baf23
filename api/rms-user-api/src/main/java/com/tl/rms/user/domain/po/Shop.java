package com.tl.rms.user.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("t_shop")
public class Shop {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("channel_id")
    private Long channelId;

    @TableField("channel_code")
    private String channelCode;

    @TableField("channel_name")
    private String channelName;

    @TableField("channel_full_name")
    private String channelFullName;

    @TableField("channel_type")
    private String channelType;

    @TableField("channel_depart_id")
    private String channelDepartId;

    @TableField("channel_depart_code")
    private String channelDepartCode;

    @TableField("channel_depart_name")
    private String channelDepartName;

    @TableField("link_man_id")
    private Long linkManId;

    @TableField("link_man")
    private String linkMan;

    @TableField("link_tel")
    private String linkTel;

    @TableField("online_plat_type_code")
    private String onlinePlatTypeCode;

    @TableField("online_plat_type_name")
    private String onlinePlatTypeName;

    @TableField("company_id")
    private String companyId;

    @TableField("company_name")
    private String companyName;

    @TableField("cate_id")
    private Long cateId;

    @TableField("cate_name")
    private String cateName;

    @TableField("customer_id")
    private String customerId;

    @TableField("center_id")
    private String centerId;

    @TableField("project_id")
    private String projectId;

    @TableField("email")
    private String email;

    @TableField("group_id")
    private Long groupId;

    @TableField("authorization_expire_date")
    private LocalDateTime authorizationExpireDate;

    @TableField("is_auto_sales")
    private Boolean isAutoSales;

    @TableField("sales_order_start_time")
    private LocalDateTime salesOrderStartTime;

    @TableField("sales_order_end_time")
    private LocalDateTime salesOrderEndTime;

    @TableField("warehouse_id")
    private String warehouseId;

    @TableField("warehouse_code")
    private String warehouseCode;

    @TableField("warehouse_name")
    private String warehouseName;

    @TableField("charge_type")
    private String chargeType;

    @TableField("country_id")
    private Integer countryId;

    @TableField("country_name")
    private String countryName;

    @TableField("province_id")
    private Integer provinceId;

    @TableField("province_name")
    private String provinceName;

    @TableField("city_id")
    private Integer cityId;

    @TableField("city_name")
    private String cityName;

    @TableField("town_id")
    private Integer townId;

    @TableField("town_name")
    private String townName;

    @TableField("street_id")
    private Integer streetId;

    @TableField("street_name")
    private String streetName;

    @TableField("office_address")
    private String officeAddress;

    @TableField("postcode")
    private String postcode;

    @TableField("memo")
    private String memo;

    @TableField("status")
    private Boolean status;

    @TableField("sync_jky_status")
    private Integer syncJkyStatus;

    @TableField("sync_jky_time")
    private LocalDateTime syncJkyTime;

    @TableField("is_distribution_support")
    private Integer isDistributionSupport;

    /**
     * 是否一盘货 0-否 1-是
     */
    @TableField("is_pallet_goods")
    private Integer isPalletGoods;
    
    @TableField("create_by")
    private Long createBy;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_by")
    private Long updateBy;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("deleted")
    private Boolean deleted;
} 