package com.tl.rms.user.domain.converter;

import com.tl.rms.user.domain.po.DataPermissionModule;
import com.tl.rms.user.domain.vo.DataPermissionModuleVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface DataPermissionModuleConverter {

    DataPermissionModuleConverter MAPPER = Mappers.getMapper(DataPermissionModuleConverter.class);

    DataPermissionModuleVo toVo(DataPermissionModule po);

    List<DataPermissionModuleVo> toVoList(List<DataPermissionModule> poList);

    DataPermissionModule toPo(DataPermissionModuleVo vo);

    List<DataPermissionModule> toPoList(List<DataPermissionModuleVo> voList);
}
