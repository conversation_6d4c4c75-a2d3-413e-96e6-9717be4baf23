package com.tl.rms.user.api;

import com.tl.rms.user.domain.vo.PermissionVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "PermissionClient", value = "rms-user")
public interface PermissionClient {

    String PATH = "/permission";

    @GetMapping(PATH + "/list/tree")
    List<PermissionVo> listTree();

    @GetMapping(PATH + "/{id}")
    PermissionVo getById(@PathVariable("id") Long id);

    @PostMapping(PATH + "/create")
    boolean create(@RequestBody @Validated PermissionVo permissionVo);

    @PutMapping(PATH)
    boolean update(@RequestBody @Validated PermissionVo permissionVo);

    @DeleteMapping(PATH + "/{id}")
    boolean delete(@PathVariable("id") Long id, @RequestParam("userId") Long userId);

    @PutMapping(PATH + "/status/{id}")
    boolean updateStatus(@PathVariable("id") Long id, @RequestParam("status") Integer status, @RequestParam("userId") Long userId);
}