package com.tl.rms.lib.generator;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2021/7/22
 */
@Slf4j
public class BatchSize {
    private AtomicLong min;
    private Long max;
    private Integer size = 0;
    private AtomicLong count = new AtomicLong(0L);
    private String prefix = "";

    public BatchSize(long min, long max, Integer size, String prefix) {
        this.min = new AtomicLong(min);
        this.max = max;
        this.size = size;
        this.prefix = prefix;
    }

    public BatchSize(long max, Integer size, String prefix) {
        this.min = new AtomicLong(max - size);
        this.max = max;
        this.size = size;
        this.prefix = prefix;
    }

    public String getNextGeneratorId() {
        if (count.get() >= size) {
            throw new RuntimeException("您获取的key,已经达到上限，请勿再次获取");
        }
        count.getAndIncrement();
        return prefix + (min.incrementAndGet());
    }

    public String getNextGeneratorId(int length) {
        if (count.get() >= size) {
            throw new RuntimeException("您获取的key,已经达到上限，请勿再次获取");
        }
        count.getAndIncrement();
        return prefix + String.format("%0" + length + "d", (min.incrementAndGet()));
    }

    @Override
    public String toString() {
        return "BatchSize{" +
                "min=" + min +
                ", max=" + max +
                ", size=" + size +
                ", count=" + count +
                ", prefix='" + prefix + '\'' +
                '}';
    }
}
