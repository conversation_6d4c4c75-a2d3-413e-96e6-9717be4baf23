package com.tl.rms.lib.generator.service.impl;



import com.tl.rms.lib.generator.BatchSize;
import com.tl.rms.lib.generator.dao.PTMPCommonMapper;
import com.tl.rms.lib.generator.service.IdGeneratorService;
import com.pttl.tlmall.lib.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class IdGeneratorServiceImpl implements IdGeneratorService {

    @Autowired
    PTMPCommonMapper commonMapper;
    @Autowired
    RedisUtil redisUtil;


    @Override
    public String getNextKeyInternal(String seqName, String table,String prefix,String primaryKey) {
        Long nextSeq = getNextSeq(seqName, table, prefix,primaryKey, 0);
        return prefix + nextSeq;
    }

    @Override
    public String getNextKeyInternal(String seqName, String table, String prefix, String primaryKey, Long startId) {
        Long nextSeq = getNextSeq(seqName, table, prefix,primaryKey, startId.intValue());
        return prefix + nextSeq;
    }

    @Override
    public String getNextKeyInternal(String seqName, String table, String prefix,String primaryKey, Integer length) {
        Long nextSeq = getNextSeq(seqName, table, prefix,primaryKey, 0);
        return prefix + String.format("%0" + length + "d", nextSeq);
    }

    @Override
    @Deprecated
    public BatchSize getNextKeyInternalBatch(String seqName, String table, String prefix, String primaryKey, Integer size) {
        BatchSize batchSize = getNextSeqBatch( seqName,  table,  prefix, primaryKey, 1, size);
        log.info("BatchSize seqName:{},size:{},result:{}",seqName,size,batchSize);
        return batchSize;
    }
    @Deprecated
    private  BatchSize  getNextSeqBatch(String seqName, String table, String prefix,String primaryKey, Integer startId,Integer size) {
        synchronized(this){
            Long currentSeq = (Long) redisUtil.getLong(seqName);
            if (currentSeq == null) {
                Integer maxId = getMaxIdFromDb(table, prefix,primaryKey, startId);
                Long max = Long.parseLong(maxId.toString());
                redisUtil.set(seqName, max, null);
                currentSeq = max;
            }
            if(size == null || size.intValue() == 0){
                size = Integer.valueOf(1);
            }
            Long nextSeq = redisUtil.incrLong(seqName, size.longValue());
            BatchSize  batchSize = new BatchSize(currentSeq,nextSeq,size,prefix);
            return batchSize;
        }
    }

    @Override
    public BatchSize getNextKeyInternalBatchSafe(String seqName, String table, String prefix, String primaryKey, Integer size) {
        Long currentSeq = redisUtil.getLong(seqName);
        if (currentSeq == null) {
            Integer maxId = getMaxIdFromDb(table, prefix, primaryKey, 1);
            Long max = Long.parseLong(maxId.toString());
            redisUtil.setNX(seqName, max);
        }
        if (size == null || size.intValue() == 0) {
            size = Integer.valueOf(1);
        }
        Long nextSeq = redisUtil.incrLong(seqName, size.longValue());
        BatchSize batchSize = new BatchSize(nextSeq, size, prefix);
        log.info("BatchSize seqName: {}, size: {}, result: {}", seqName, size, batchSize);
        return batchSize;
    }

    private Long getNextSeq(String seqName, String table, String prefix,String primaryKey, Integer startId) {
        Long currentSeq = (Long) redisUtil.getLong(seqName);
        if (currentSeq == null) {
            Integer maxId = getMaxIdFromDb(table, prefix,primaryKey, startId);
            Long max = Long.parseLong(maxId.toString());
            redisUtil.set(seqName, max, null);
        }
        Long nextSeq = redisUtil.incrLong(seqName, 1L);
        return nextSeq;
    }

    private Integer getMaxIdFromDb(String pTableName, String pPrefix,String primaryKey, Integer startId) {
        //默认主键列名称为 id
        if(StringUtils.isBlank(primaryKey)){
            primaryKey = "id";
        }
        // 如果前缀是数值，需要处理为空字符串，以免mysql的replace函数得到的结果不对
        if (StringUtils.isNumeric(pPrefix)) {
            pPrefix = "";
        }
        Integer maxId = commonMapper.selectMaxId(pTableName, pPrefix,primaryKey);

        if (maxId == null) {
            return startId;
        }
        return maxId;
    }
}
