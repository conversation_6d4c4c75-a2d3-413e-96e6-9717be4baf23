package com.tl.rms.lib.generator;


import com.tl.rms.lib.generator.service.IdGeneratorService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * id生产器
 * 需要在application.yml
 * 中配置
 * IdGenerator:
 *      keyMap:
 *      prefixMap:
 * <AUTHOR>
 * @date 2018.07.09
 *
 */
@Data
@ConfigurationProperties(prefix = "id-generator")
@Component
public class PttlIdGenerator {
    private Map<String,String> keyMap;
    private Map<String,String> prefixMap;
    private Map<String,String> tableMap;
    private Map<String,String> primaryKeyMap;
    @Autowired
    private IdGeneratorService redisService;

    public String generatorId(String name){

        Object seqName = keyMap.get(name);
        Object prefix = (prefixMap == null?"":prefixMap.get(name));
        if(prefix == null){
            prefix = "";
        }
        Object table = tableMap.get(name);
        if(table == null){
               table = "";
        }
        Object primaryKey = (primaryKeyMap == null?"":primaryKeyMap.get(name));
        if(primaryKey == null){
            primaryKey = "";
        }
        return redisService.getNextKeyInternal((String)seqName,(String)table,(String)prefix,(String)primaryKey);
    }
    public BatchSize generatorIdBatch(String name, Integer size){

        Object seqName = keyMap.get(name);
        Object prefix = (prefixMap == null?"":prefixMap.get(name));
        if(prefix == null){
            prefix = "";
        }
        Object table = tableMap.get(name);
        if(table == null){
            table = "";
        }
        Object primaryKey = (primaryKeyMap == null?"":primaryKeyMap.get(name));
        if(primaryKey == null){
            primaryKey = "";
        }
        return redisService.getNextKeyInternalBatchSafe((String)seqName,(String)table,(String)prefix,(String)primaryKey,size);
    }

    public BatchSize generatorIdBatchSafe(String name, Integer size) {

        Object seqName = keyMap.get(name);
        Object prefix = (prefixMap == null ? "" : prefixMap.get(name));
        if (prefix == null) {
            prefix = "";
        }
        Object table = tableMap.get(name);
        if (table == null) {
            table = "";
        }
        Object primaryKey = (primaryKeyMap == null ? "" : primaryKeyMap.get(name));
        if (primaryKey == null) {
            primaryKey = "";
        }
        return redisService.getNextKeyInternalBatchSafe((String) seqName, (String) table, (String) prefix, (String) primaryKey, size);
    }

    public String generatorId(String name, Long startId){

        Object seqName = keyMap.get(name);
        Object prefix = (prefixMap == null?"":prefixMap.get(name));
        if(prefix == null){
            prefix = "";
        }
        Object table = tableMap.get(name);
        if(table == null){
            table = "";
        }
        Object primaryKey = (primaryKeyMap == null?"":primaryKeyMap.get(name));
        if(primaryKey == null){
            primaryKey = "";
        }
        return redisService.getNextKeyInternal((String)seqName,(String)table,(String)prefix, (String)primaryKey, startId);
    }

    public String generatorId(String name, int length){

        Object seqName = keyMap.get(name);
        Object prefix = (prefixMap == null?"":prefixMap.get(name));
        if(prefix == null){
            prefix = "";
        }
        Object table = tableMap.get(name);
        if(table == null){
            table = "";
        }
        Object primaryKey = (primaryKeyMap == null?"":primaryKeyMap.get(name));
        if(primaryKey == null){
            primaryKey = "";
        }
        return redisService.getNextKeyInternal((String)seqName,(String)table,(String)prefix, (String)primaryKey,length);
    }
}
