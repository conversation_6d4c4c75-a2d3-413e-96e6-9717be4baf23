package com.tl.rms.lib.generator.service;

import com.tl.rms.lib.generator.BatchSize;

/**
 * ID 生成服务
 *
 * <AUTHOR>
 */
public interface IdGeneratorService {

    String getNextKeyInternal(String seqName, String table, String prefix, String primaryKey);

    String getNextKeyInternal(String seqName, String table, String prefix, String primaryKey, Long startId);

    String getNextKeyInternal(String seqName, String table, String prefix, String primaryKey, Integer length);

    /**
     * 批量获取ID
     * @param seqName
     * @param table
     * @param prefix
     * @param primaryKey
     * @param size ID 数量
     * @return
     */
    @Deprecated
    BatchSize getNextKeyInternalBatch(String seqName, String table, String prefix, String primaryKey, Integer size);

    BatchSize getNextKeyInternalBatchSafe(String seqName, String table, String prefix, String primaryKey, Integer size);

}
