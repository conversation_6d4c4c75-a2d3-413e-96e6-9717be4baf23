package com.tl.rms.lib.fulfillment.client.service;

import com.tl.rms.lib.fulfillment.message.CancelLineResponse;
import com.tl.rms.lib.fulfillment.message.TWMessage;
import com.tl.rms.lib.fulfillment.message.TwAdjustMessage;

import java.util.List;

/**
 * <AUTHOR>
 * @date 8/25/2019 1:48 PM
 */
public interface FulfillmentMessageSender {

    /**
     * 发送吉客云销售单到EBS
     *
     * <AUTHOR>
     * @date 2023/5/22
     */
    void sendJkyOrder2EBS(Long tradeId);

    /**
     * 发送吉客云订单出库
     */
    void sendJkyOrderOutbound(TWMessage twMessage);

    /**
     * 发送吉客云订单签收
     */
    void sendJkyOrderSign(TWMessage twMessage);

    /**
     * 发送吉客云订单入库
     */
//    void sendJkyOrderInbound(TWMessage twMessage);

    /**
     * 发送吉客云订单行取消
     */
    void sendJkyCancelLine(CancelLineResponse message);

    /**
     * 发送吉客云订单出库到EBS
     */
    void sendJkyOrderOutbound2Ebs(String orderId);

    /**
     * 发送吉客云订单签收到EBS
     */
    void sendJkyOrderSign2Ebs(String orderId);

    /**
     * 发送吉客云订单入库到EBS
     */
    void sendJkyOrderInbound2Ebs(String orderId);

    /**
     * 发送吉客云订单行取消到EBS
     */
    void sendJkyCancelLine2Ebs(String orderId);

    /**
     * 发送吉客云对账单到EBS
     */
    void sendJkyCheck2Ebs(Long checkId);

    /**
     * 发送订单入库
     */
    void sendTwInbound(TWMessage twMessage);

    /**
     * 发送库存异动
     */
    void sendTwAdjust(List<TwAdjustMessage> list);

    /**
     * 发送TW库存异动通知到奇门
     */
    void sendTwAdjustToQimen(List<TwAdjustMessage> list);

    void sendDeliveryOrderCreateMessage(Object message);

    void confirmDeliveryOrder(String message);

    /**
     * 发送退货入库确认消息
     */
    void confirmReturnOrder(String message);
}
