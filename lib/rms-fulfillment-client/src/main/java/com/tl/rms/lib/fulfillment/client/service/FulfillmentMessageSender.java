package com.tl.rms.lib.fulfillment.client.service;

import com.tl.rms.lib.fulfillment.message.TWMessage;
import com.tl.rms.lib.fulfillment.message.TwAdjustMessage;

import java.util.List;

/**
 * <AUTHOR>
 * @date 8/25/2019 1:48 PM
 */
public interface FulfillmentMessageSender {

    /**
     * 发送吉客云订单出库
     */
    void sendJkyOrderOutbound(TWMessage twMessage);

    /**
     * 发送订单入库
     */
    void sendTwInbound(TWMessage twMessage);

    /**
     * 发送库存异动
     */
    void sendTwAdjust(List<TwAdjustMessage> list);

    /**
     * 发送TW库存异动通知到奇门
     */
    void sendTwAdjustToQimen(List<TwAdjustMessage> list);

    void sendDeliveryOrderCreateMessage(Object message);

    void confirmDeliveryOrder(String message);
}
