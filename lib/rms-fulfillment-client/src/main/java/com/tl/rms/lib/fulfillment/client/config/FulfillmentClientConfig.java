package com.tl.rms.lib.fulfillment.client.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 队列名称配置
 *
 * <AUTHOR>
 * @date 8/25/2019 1:54 PM
 */
@Data
@Component
public class FulfillmentClientConfig {

    /**
     * 发送吉客云销售单到EBS
     */
    @Value("${mq.queue.jkyOrder2EBS}")
    private String jkyOrder2EBS;

    /**
     * 发送吉客云对账单到EBS
     */
    @Value("${mq.queue.jkyCheck2EBS}")
    private String jkyCheck2EBS;

    /**
     * TW->RMS 订单出库
     */
    @Value("${mq.queue.jkyOrderOutboundRms}")
    private String jkyOrderOutbound;

    @Value("${mq.queue.jkyOrderSign}")
    private String jkyOrderSign;

    @Value("${mq.queue.jkyOrderInbound}")
    private String jkyOrderInbound;

    @Value("${mq.queue.jkyCancelLine}")
    private String jkyCancelLine;

    @Value("${mq.queue.jkyOrderOutbound2Ebs}")
    private String jkyOrderOutbound2Ebs;

    @Value("${mq.queue.jkyOrderSign2Ebs}")
    private String jkyOrderSign2Ebs;

    @Value("${mq.queue.jkyOrderInbound2Ebs}")
    private String jkyOrderInbound2Ebs;

    @Value("${mq.queue.jkyCancelLine2Ebs}")
    private String jkyCancelLine2Ebs;

    /**
     * TW->RMS 订单入库
     */
    @Value("${mq.queue.rmsTwInbound:rmsTwInbound.uat}")
    private String rmsTwInbound;

    /**
     * TW->RMS 库存异动
     */
    @Value("${mq.queue.rmsTwAdjust:rmsTwAdjust.uat}")
    private String rmsTwAdjust;

    /**
     * 发送TW库存异动通知到奇门(TW->QIMEN)
     */
    @Value("${mq.queue.rms.twAdjust2Qimen:twAdjust2Qimen.uat}")
    private String twAdjust2Qimen;

    /**
     * JKY->RMS 发货单创建
     */
    @Value("${mq.queue.rmsJkyDeliveryOrder:rmsJkyDeliveryOrder.uat}")
    private String rmsJkyDeliveryOrder;

    /**
     * TW->RMS 发货单创建结果回调
     */
    @Value("${mq.queue.twRmsConfirmOrder:twRmsConfirmOrder.uat}")
    private String twRmsConfirmOrder;

    /**
     * TW->RMS 退货入库单确认
     */
    @Value("${mq.queue.twRmsConfirmReturnOrder:twRmsConfirmReturnOrder.uat}")
    private String twRmsConfirmReturnOrder;
}
