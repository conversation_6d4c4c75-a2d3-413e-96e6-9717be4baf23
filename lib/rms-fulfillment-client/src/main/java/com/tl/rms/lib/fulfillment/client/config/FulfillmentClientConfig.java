package com.tl.rms.lib.fulfillment.client.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 队列名称配置
 *
 * <AUTHOR>
 * @date 8/25/2019 1:54 PM
 */
@Data
@Component
public class FulfillmentClientConfig {

    /**
     * TW->RMS 订单出库
     */
    @Value("${mq.queue.jkyOrderOutboundRms}")
    private String jkyOrderOutbound;

    /**
     * TW->RMS 订单出库
     */
    @Value("${mq.queue.rmsTwInbound:rmsTwInbound.uat}")
    private String rmsTwInbound;

    /**
     * TW->RMS 库存异动
     */
    @Value("${mq.queue.rmsTwAdjust:rmsTwAdjust.uat}")
    private String rmsTwAdjust;

    /**
     * 发送TW库存异动通知到奇门(TW->QIMEN)
     */
    @Value("${mq.queue.rms.twAdjust2Qimen:twAdjust2Qimen.uat}")
    private String twAdjust2Qimen;

    /**
     * JKY->RMS 发货单创建
     */
    @Value("${mq.queue.rmsJkyDeliveryOrder:rmsJkyDeliveryOrder.uat}")
    private String rmsJkyDeliveryOrder;

    /**
     * TW->RMS 发货单创建结果回调
     */
    @Value("${mq.queue.twRmsConfirmOrder:twRmsConfirmOrder.uat}")
    private String twRmsConfirmOrder;
}
