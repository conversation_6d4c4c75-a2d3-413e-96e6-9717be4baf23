package com.tl.rms.lib.fulfillment.client.service.impl;

import com.tl.rms.lib.fulfillment.client.config.FulfillmentClientConfig;
import com.tl.rms.lib.fulfillment.client.service.FulfillmentMessageSender;
import com.tl.rms.lib.fulfillment.message.TWMessage;
import com.tl.rms.lib.fulfillment.message.TwAdjustMessage;
import com.tl.rms.lib.mq.producer.MqProducer;
import com.tl.rms.util.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 所有订单的FulfillmentMessage发送端
 *
 * <AUTHOR>
 * @date 8/25/2019 1:49 PM
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FulfillmentMessageSenderImpl implements FulfillmentMessageSender {

    private final MqProducer mqProducer;
    private final FulfillmentClientConfig config;

    @Override
    public void sendJkyOrderOutbound(TWMessage twMessage) {
        mqProducer.sendMessage(config.getJkyOrderOutbound(), twMessage);
    }

    @Override
    public void sendTwInbound(TWMessage twMessage) {
        mqProducer.sendMessage(config.getRmsTwInbound(), twMessage);
    }

    @Override
    public void sendTwAdjust(List<TwAdjustMessage> list) {
        mqProducer.sendMessage(config.getRmsTwAdjust(), list);
    }

    @Override
    public void sendTwAdjustToQimen(List<TwAdjustMessage> list) {
        mqProducer.sendMessage(config.getTwAdjust2Qimen(), list);
    }

    @Override
    public void sendDeliveryOrderCreateMessage(Object message) {
        mqProducer.sendMessage(config.getRmsJkyDeliveryOrder(), JsonUtil.toJson(message));
    }

    @Override
    public void confirmDeliveryOrder(String message) {
        mqProducer.sendMessage(config.getTwRmsConfirmOrder(), message);
    }
}
