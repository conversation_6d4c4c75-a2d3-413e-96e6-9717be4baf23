package com.tl.rms.lib.fulfillment.client.service.impl;

import com.tl.rms.lib.fulfillment.client.config.FulfillmentClientConfig;
import com.tl.rms.lib.fulfillment.client.service.FulfillmentMessageSender;
import com.tl.rms.lib.fulfillment.message.CancelLineResponse;
import com.tl.rms.lib.fulfillment.message.TWMessage;
import com.tl.rms.lib.fulfillment.message.TwAdjustMessage;
import com.tl.rms.lib.mq.producer.MqProducer;
import com.tl.rms.util.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 所有订单的FulfillmentMessage发送端
 *
 * <AUTHOR>
 * @date 8/25/2019 1:49 PM
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FulfillmentMessageSenderImpl implements FulfillmentMessageSender {

    private final MqProducer mqProducer;

    private final FulfillmentClientConfig config;

    @Override
    public void sendJkyOrder2EBS(Long tradeId) {
        mqProducer.sendMessage(config.getJkyOrder2EBS(), tradeId);
    }

    @Override
    public void sendJkyOrderOutbound(TWMessage twMessage) {
        mqProducer.sendMessage(config.getJkyOrderOutbound(), twMessage);
    }

    @Override
    public void sendJkyOrderSign(TWMessage twMessage) {
        mqProducer.sendMessage(config.getJkyOrderSign(), twMessage);
    }

    /*@Override
    public void sendJkyOrderInbound(TWMessage twMessage) {
        mqProducer.sendMessage(config.getJkyOrderInbound(), twMessage);
    }*/

    @Override
    public void sendJkyCancelLine(CancelLineResponse message) {
        mqProducer.sendMessage(config.getJkyCancelLine(), message);
    }

    @Override
    public void sendJkyOrderOutbound2Ebs(String orderId) {
        mqProducer.sendMessage(config.getJkyOrderOutbound2Ebs(), orderId);
    }

    @Override
    public void sendJkyOrderSign2Ebs(String orderId) {
        mqProducer.sendMessage(config.getJkyOrderSign2Ebs(), orderId);
    }

    @Override
    public void sendJkyOrderInbound2Ebs(String orderId) {
        mqProducer.sendMessage(config.getJkyOrderInbound2Ebs(), orderId);
    }

    @Override
    public void sendJkyCheck2Ebs(Long checkId) {
        mqProducer.sendMessage(config.getJkyCheck2EBS(), checkId);
    }

    @Override
    public void sendJkyCancelLine2Ebs(String orderId) {
        mqProducer.sendMessage(config.getJkyCancelLine2Ebs(), orderId);
    }

    @Override
    public void sendTwInbound(TWMessage twMessage) {
        mqProducer.sendMessage(config.getRmsTwInbound(), twMessage);
    }

    @Override
    public void sendTwAdjust(List<TwAdjustMessage> list) {
        mqProducer.sendMessage(config.getRmsTwAdjust(), list);
    }

    @Override
    public void sendTwAdjustToQimen(List<TwAdjustMessage> list) {
        mqProducer.sendMessage(config.getTwAdjust2Qimen(), list);
    }

    @Override
    public void sendDeliveryOrderCreateMessage(Object message) {
        mqProducer.sendMessage(config.getRmsJkyDeliveryOrder(), JsonUtil.toJson(message));
    }

    @Override
    public void confirmDeliveryOrder(String message) {
        mqProducer.sendMessage(config.getTwRmsConfirmOrder(), message);
    }

    @Override
    public void confirmReturnOrder(String message) {
        mqProducer.sendMessage(config.getTwRmsConfirmReturnOrder(), message);
    }
}
