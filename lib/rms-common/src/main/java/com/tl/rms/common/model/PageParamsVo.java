package com.tl.rms.common.model;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Data
public class PageParamsVo<T> {

    private int page = 1;
    private int size = 10;

    private List<SortConditionVo> sortConditions;

    public Page<T> toPage() {
        return new Page<>(this.getPage(), this.getSize());
    }

    public String toSortSql() {
        if (CollectionUtils.isEmpty(sortConditions)) {
            return null;
        }
        StringBuilder sb = new StringBuilder("order by ");
        for (SortConditionVo sortCondition : sortConditions) {
            sb.append(sortCondition.getField()).append(" ").append(sortCondition.getAsc() ? "asc" : "desc").append(",");
        }
        return sb.substring(0, sb.length() - 1);
    }
}
