package com.tl.rms.common.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Objects;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ResponseVo<T> implements Serializable {

    public static final int SUCCESS_CODE = 200;
    public static final int FAILURE_CODE = 500;
    public static final String SUCCESS_MSG = "success";
    public static final String FAILURE_MSG = "failure";

    private Integer code;
    private String msg;
    private T data;
    private String exception;


    public static <T> ResponseVo<T> suc() {
        return suc(null, SUCCESS_MSG);
    }

    public static <T> ResponseVo<T> suc(T data) {
        return suc(data, SUCCESS_MSG);
    }

    public static <T> ResponseVo<T> suc(T data, String message) {
        return new ResponseVo<T>()
                .setCode(SUCCESS_CODE)
                .setData(data)
                .setMsg(Objects.requireNonNullElse(message, SUCCESS_MSG));
    }

    public static <T> ResponseVo<T> failure(String message) {
        return new ResponseVo<T>()
                .setCode(FAILURE_CODE)
                .setMsg(message);
    }

    public static <T> ResponseVo<T> failure(Integer code, String message) {
        return new ResponseVo<T>()
                .setCode(code)
                .setMsg(message);
    }

    public static <T> ResponseVo<T> failure(Integer code, String message, T data) {
        return new ResponseVo<T>()
                .setCode(code)
                .setMsg(message)
                .setData(data);
    }
}


