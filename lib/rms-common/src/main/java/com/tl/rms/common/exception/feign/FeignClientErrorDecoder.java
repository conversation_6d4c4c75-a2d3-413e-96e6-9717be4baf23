package com.tl.rms.common.exception.feign;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tl.rms.common.exception.AccessDeniedException;
import com.tl.rms.common.exception.AuthenticationException;
import com.tl.rms.common.exception.CommonException;
import com.tl.rms.common.exception.NotSupportedMethodException;
import com.tl.rms.common.exception.ResourceNotFoundException;
import com.tl.rms.common.exception.SystemException;
import com.tl.rms.common.model.ResponseVo;
import feign.Request;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import java.io.IOException;
import java.nio.charset.Charset;

@Slf4j
public class FeignClientErrorDecoder implements ErrorDecoder {

    private static final ErrorDecoder DEFAULT_DECODER = new Default();
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Override
    public Exception decode(String methodKey, Response response) {
        Request request = response.request();
        try {
            int status = response.status();
            if (status >= 500) {
                // system error
                log.error("System error from {}, method is {}, uri is {} status is {}",
                        methodKey, request.httpMethod(), request.url(), status);
                return DEFAULT_DECODER.decode(methodKey, response);
            } else if (status >= 400) {
                // business error
                final String bodyContent = IOUtils.toString(response.body().asInputStream(), Charset.defaultCharset());
                ResponseVo<?> responseVo = OBJECT_MAPPER.readValue(bodyContent, ResponseVo.class);
                return switch (status) {
                    case 401 -> new AuthenticationException(responseVo.getCode(), responseVo.getMsg());
                    case 403 -> new AccessDeniedException(responseVo.getCode(), responseVo.getMsg());
                    case 404 -> new ResourceNotFoundException(responseVo.getCode(), responseVo.getMsg());
                    case 405 -> new NotSupportedMethodException(responseVo.getCode(), responseVo.getMsg());
                    default -> new CommonException(responseVo.getCode(), responseVo.getMsg());
                };
            } else if (status >= 300) {
                return new NotSupportedMethodException(String.format("Response status %s not supported for method %s", status, methodKey));
            }
            // unknown error
            return new SystemException(String.format("Unknown error from %s, status is %s",
                    methodKey, status));
        } catch (IOException e) {
            log.error("IOException occurs where decode error response", e);
            return new SystemException(String.format("Decode error occurs when read %s", methodKey), e);
        }
    }
}
