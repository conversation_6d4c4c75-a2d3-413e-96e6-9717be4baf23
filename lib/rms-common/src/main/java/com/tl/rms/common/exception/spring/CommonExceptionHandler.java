package com.tl.rms.common.exception.spring;

import com.tl.rms.common.exception.AccessDeniedException;
import com.tl.rms.common.exception.AuthenticationException;
import com.tl.rms.common.exception.CommonException;
import com.tl.rms.common.exception.SystemException;
import com.tl.rms.common.model.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public abstract class CommonExceptionHandler {

    // spring boot invalid parameter exceptions

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public ResponseVo<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        List<ObjectError> allErrors = ex.getBindingResult().getAllErrors();
        String message = allErrors.stream().map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.joining(";"));
        log.error("参数验证异常：{}", message);
        return ResponseVo.failure(message);
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(BindException.class)
    @ResponseBody
    public ResponseVo<?> handleBindException(BindException ex) {
        List<ObjectError> allErrors = ex.getBindingResult().getAllErrors();
        String message = allErrors.stream()
                .map(error -> {
                    if (error instanceof FieldError fieldError) {
                        return fieldError.getField() + ": " + fieldError.getDefaultMessage();
                    }
                    return error.getDefaultMessage();
                })
                .collect(Collectors.joining(";"));

        log.error("参数绑定异常：{}", message);
        return ResponseVo.failure(message);
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseBody
    public ResponseVo<?> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException ex) {
        log.error("参数类型不匹配异常：{}", ex.getMessage(), ex);
        return ResponseVo.failure(ex.getMessage());
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseBody
    public ResponseVo<?> handleHttpMessageNotReadableException(HttpMessageNotReadableException ex) {
        log.error("请求体解析异常：{}", ex.getMessage(), ex);
        return ResponseVo.failure(ex.getMessage());
    }

    // common exceptions

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(CommonException.class)
    @ResponseBody
    public ResponseVo<?> handleCommonException(CommonException ex) {
        log.error("通用异常：{}", ex.getMessage(), ex);
        return ResponseVo.failure(400, ex.getMessage());
    }

    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @ExceptionHandler(AuthenticationException.class)
    @ResponseBody
    public ResponseVo<?> handleAuthenticationException(AuthenticationException ex) {
        return ResponseVo.failure(401, ex.getMessage());
    }

    @ResponseStatus(HttpStatus.FORBIDDEN)
    @ExceptionHandler(AccessDeniedException.class)
    @ResponseBody
    public ResponseVo<?> handleAccessDeniedException(AccessDeniedException ex) {
        return ResponseVo.failure(403, ex.getMessage());
    }

    // system exceptions

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(SystemException.class)
    @ResponseBody
    public ResponseVo<?> exception(SystemException e) {
        log.error("系统异常：{}", e.getMessage(), e);
        return ResponseVo.failure(e.getMessage());
    }

    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(Throwable.class)
    @ResponseBody
    public ResponseVo<?> handleUnknownException(Exception e) {
        log.error("未知异常：{}", e.getMessage(), e);
        return ResponseVo.failure("系统发生未知异常");
    }
}
