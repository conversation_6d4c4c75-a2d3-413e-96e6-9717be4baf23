package com.tl.rms.common.exception;


public class NotSupportedMethodException extends CommonException {

    public NotSupportedMethodException(Integer code, String message) {
        super(code, message);
    }

    public NotSupportedMethodException(Integer code, String message, Throwable cause) {
        super(code, message, cause);
    }

    public NotSupportedMethodException(String message) {
        super(message);
    }
}