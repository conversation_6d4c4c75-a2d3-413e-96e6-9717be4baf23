package com.tl.rms.common.exception;

public class CommonException extends RuntimeException implements BaseException {

    private Integer code;

    public CommonException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    public CommonException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public CommonException(String message) {
        super(message);
    }
}