package com.tl.rms.common.utils;

import com.tl.rms.common.model.BaseEnum;
import com.tl.rms.common.model.EnumInfoVo;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class AbstractCommonEnumUtils {

    public static <T extends BaseEnum<C>, C> T getEnum(Class<T> clazz, C code) {
        for (T t : clazz.getEnumConstants()) {
            if (t.getCode().equals(code)) {
                return t;
            }
        }
        return null;
    }

    public static <T extends BaseEnum<C>, C> String getDescOrDefault(Class<T> clazz, C code, String v) {
        String desc = getDesc(clazz, code);
        return desc == null ? v : desc;
    }

    public static <T extends BaseEnum<C>, C> String getDesc(Class<T> clazz, C code) {
        if (code == null) {
            return null;
        }
        for (T t : clazz.getEnumConstants()) {
            if (t.getCode().equals(code)) {
                return t.getDesc();
            }
        }
        return null;
    }

    @SuppressWarnings("rawtypes")
    public static <T extends BaseEnum> List<EnumInfoVo> toEnumInfos(Class<T> clazz) {
        T[] enumConstants = clazz.getEnumConstants();
        List<EnumInfoVo> list = new ArrayList<>(enumConstants.length);
        for (T t : enumConstants) {
            list.add(new EnumInfoVo(t.getCode().toString(), t.getDesc().toString()));
        }
        return list;
    }
}
