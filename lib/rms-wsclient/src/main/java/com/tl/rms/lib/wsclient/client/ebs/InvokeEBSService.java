package com.tl.rms.lib.wsclient.client.ebs;


import com.tl.rms.lib.wsclient.client.ebs.inboundsync.InboundResponseInfo;
import com.tl.rms.lib.wsclient.client.ebs.linecancelsync.OMCancelLineService;
import com.tl.rms.lib.wsclient.client.ebs.linecancelsync.OMCancelLineServicePortType;
import com.tl.rms.lib.wsclient.client.ebs.ordersync.LoadOrderService;
import com.tl.rms.lib.wsclient.client.ebs.ordersync.LoadOrderServicePortType;
import com.tl.rms.lib.wsclient.client.ebs.ordersync.MessageRequestType;
import com.tl.rms.lib.wsclient.client.ebs.ordersync.MessageResponseType;
import com.tl.rms.lib.wsclient.client.ebs.outboundsync.OutboundResponseInfoType;
import com.tl.rms.lib.wsclient.client.ebs.outboundsync.OutboundResponseService;
import com.tl.rms.lib.wsclient.client.ebs.outboundsync.OutboundResponseServicePortType;
import com.tl.rms.lib.wsclient.client.ebs.signsync.ResultInfo;
import com.tl.rms.lib.wsclient.client.ebs.signsync.SignInfo;
import com.tl.rms.lib.wsclient.client.ebs.signsync.SignService;
import com.tl.rms.lib.wsclient.client.ebs.signsync.SignServicePortType;
import com.tl.rms.lib.wsclient.client.tw.response.Response;
import com.tl.rms.lib.wsclient.common.LoggingHandler;
import com.tl.rms.lib.wsclient.config.WebServiceClientConfig;
import com.tl.rms.util.json.JsonProcessUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.net.URL;

/**
 * 调用EBS系统的接口
 */
@Slf4j
@Component
public class InvokeEBSService {

    // 订单同步
    private static final String ORDER_SYNC_WSDL = "/LoadOrderService?wsdl";

    // 订单出库
    private static final String ORDER_OUTBOUND_WSDL = "/TLSI_0004/processes/OutboundResponseService?wsdl";

    // 订单签收
    private static final String ORDER_SIGN_WSDL = "/TLSI_0005/processes/SignService?wsdl";

    // 订单改签
    private static final String ORDER_RESIGN_WSDL = "/TLSI_0006/processes/ReSignService?wsdl";

    // 订单入库rest方式url
    private static final String ORDER_INBOUND_REST = "/jky/returnToWarehouse";

    // 行取消
    private static final String LINE_CANCEL_WSDL = "/OMCancelLineService?wsdl";

    @Autowired
    private WebServiceClientConfig webServiceClientConfig;

    /**
     * 调用EBS的销售订单和红字折扣订单同步接口
     */
    @SneakyThrows
    public Response invokeEBSOrderSyncService(MessageRequestType request) {

        log.info("invokeEBSOrderSyncService request: {}",
                request == null ? null : JsonProcessUtil.beanToJson(request));
        if (request == null) {
            return Response.badRequest();
        }

        URL wsdl = new URL(webServiceClientConfig.getEsbWsAddress() + ORDER_SYNC_WSDL);
        LoadOrderService ss = new LoadOrderService(wsdl);
        LoadOrderServicePortType port = ss.getLoadOrderServiceSOAP11PortHttp();
        MessageResponseType response = port.load(request);
        log.info("invokeEBSOrderSyncService response: {}",
                response == null ? null : JsonProcessUtil.beanToJson(response));
        if (response != null) {
            return new Response(response.getReturnCode(), response.getErrorMsg(), response.getDateTime());
        } else {
            throw new RuntimeException(Response.noResponse().getErrorMsg());
        }
    }

    /**
     * 调用EBS订单出库接口
     *
     * <AUTHOR>
     * @date 2019/9/5
     */
    @SneakyThrows
    public Response invokeEBSOrderOutboundService(OutboundResponseInfoType request) {
        log.info("invokeEBSOrderOutboundService request: {}",
                request == null ? null : JsonProcessUtil.beanToJson(request));
        if (request == null) {
            return Response.badRequest();
        }

        URL wsdl = new URL(webServiceClientConfig.getEsbWsAddress() + ORDER_OUTBOUND_WSDL);
        OutboundResponseService ss = new OutboundResponseService(wsdl);
        OutboundResponseServicePortType port = ss.getOutboundResponseServiceSOAP11PortHttp();
        log.info(wsdl.toString());
        LoggingHandler.handleMessage(port);

        com.tl.rms.lib.wsclient.client.ebs.outboundsync.MessageRequestType messageRequestType = new com.tl.rms.lib.wsclient.client.ebs.outboundsync.MessageRequestType();
        messageRequestType.setOutboundResponseInfo(request);
        com.tl.rms.lib.wsclient.client.ebs.outboundsync.MessageResponseType response = port.outboundResponse(messageRequestType);
        log.info("invokeEBSOrderOutboundService response: {}",
                response == null ? null : JsonProcessUtil.beanToJson(response));
        if (response != null) {
            return new Response(response.getReturnCode(), response.getErrorMsg(), response.getDateTime());
        } else {
            throw new RuntimeException(Response.noResponse().getErrorMsg());
        }
    }

    /**
     * 调用EBS订单签收接口
     *
     * <AUTHOR>
     * @date 2019/9/5
     */
    @SneakyThrows
    public Response invokeEBSOrderSignService(SignInfo request) {

        log.info("invokeEBSOrderSignService request: {}",
                request == null ? null : JsonProcessUtil.beanToJson(request));
        if (request == null) {
            return Response.badRequest();
        }

        URL wsdl = new URL(webServiceClientConfig.getEsbWsAddress() + ORDER_SIGN_WSDL);
        SignService ss = new SignService(wsdl);
        SignServicePortType port = ss.getSignServiceSOAP11PortHttp();
        log.info(wsdl.toString());
        LoggingHandler.handleMessage(port);

        ResultInfo response = port.sign(request);
        log.info("invokeEBSOrderSignService response: {}",
                response == null ? null : JsonProcessUtil.beanToJson(response));
        if (response != null) {
            return new Response(response.getReturnCode(), response.getErrorMsg(), response.getDateTime());
        } else {
            throw new RuntimeException(Response.noResponse().getErrorMsg());
        }
    }

    /**
     * 调用EBS订单入库接口(rest方式)
     *
     * @param request 发送吉客云订单入库给EBS参数
     * @return {@link Response}
     * <AUTHOR>
     * @date 2023/10/12
     */
    @SneakyThrows
    public Response invokeEBSOrderInboundServiceByRest(InboundResponseInfo request) {
        log.info("invokeEBSOrderInboundServiceByRest request: {}",
                request == null ? null : JsonProcessUtil.beanToJson(request));
        if (request == null) {
            return Response.badRequest();
        }

        // 构建请求
        String url = webServiceClientConfig.getEsbWsAddress() + ORDER_INBOUND_REST;
        Response response = new RestTemplate().postForObject(url, request, Response.class);
        log.info("invokeEBSOrderInboundServiceByRest response: {}",
                response == null ? null : JsonProcessUtil.beanToJson(response));
        if (response != null) {
            return response;
        }

        // 否则主动抛出异常
        throw new RuntimeException(Response.noResponse().getErrorMsg());
    }

    /**
     * 调用EBS行取消接口
     *
     * <AUTHOR>
     * @date 2019/9/5
     */
    @SneakyThrows
    public Response invokeEBSLineCancelService(com.tl.rms.lib.wsclient.client.ebs.linecancelsync.MessageRequestType request) {
        log.info("invokeEBSLineCancelService request: {}",
                request == null ? null : JsonProcessUtil.beanToJson(request));
        if (request == null) {
            return Response.badRequest();
        }

        URL wsdl = new URL(webServiceClientConfig.getEsbWsAddress() + LINE_CANCEL_WSDL);
        OMCancelLineService ss = new OMCancelLineService(wsdl);
        OMCancelLineServicePortType port = ss.getOMCancelLineServiceSOAP11PortHttp();

        com.tl.rms.lib.wsclient.client.ebs.linecancelsync.MessageResponseType response = port.cancelLine(request);
        log.info("invokeEBSLineCancelService response: {}",
                response == null ? null : JsonProcessUtil.beanToJson(response));
        if (response != null) {
            return new Response(response.getReturnCode(), response.getErrorMsg(), response.getDateTime());
        } else {
            throw new RuntimeException(Response.noResponse().getErrorMsg());
        }
    }

}
