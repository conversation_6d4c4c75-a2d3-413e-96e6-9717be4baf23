package com.tl.rms.lib.wsclient.client.tw;

import com.tl.rms.lib.wsclient.client.tw.request.inbound.PtInboundOrderInfo;
import com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder.ResultInfo;
import jakarta.jws.WebMethod;
import jakarta.jws.WebParam;
import jakarta.jws.WebResult;
import jakarta.jws.WebService;
import jakarta.xml.ws.RequestWrapper;
import jakarta.xml.ws.ResponseWrapper;

/**
 * TW入库服务接口
 * <AUTHOR>
 */
@WebService(name = "InboundServicePortType", targetNamespace = "InboundService")
public interface InboundServicePortType {

    /**
     * 新增入库订单
     * 
     * @param ptInboundOrderInfo
     */
    @WebMethod(operationName = "AddInboundOrder", action = "InboundService/AddInboundOrder")
    @WebResult(name = "AddInboundOrderResult", targetNamespace = "InboundService")
    @RequestWrapper(localName = "AddInboundOrder", targetNamespace = "InboundService")
    @ResponseWrapper(localName = "AddInboundOrderResponse", targetNamespace = "InboundService")
    public ResultInfo addInboundOrder(@WebParam(name = "ptInboundOrderInfo",
        targetNamespace = "InboundService") PtInboundOrderInfo ptInboundOrderInfo);
}
