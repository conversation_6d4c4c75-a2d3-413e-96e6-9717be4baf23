
package com.tl.rms.lib.wsclient.client.tw;

import com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder.ObjectFactory;
import com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder.PtOutboundOrderInfo;
import com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder.ResultInfo;
import jakarta.jws.WebMethod;
import jakarta.jws.WebParam;
import jakarta.jws.WebResult;
import jakarta.jws.WebService;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.ws.RequestWrapper;
import jakarta.xml.ws.ResponseWrapper;

/**
 * This class was generated by the JAX-WS RI. JAX-WS RI 2.2.9-b130926.1035 Generated source version: 2.2
 * 
 */
@WebService(name = "OutboundServicePortType", targetNamespace = "OutboundService")
@XmlSeeAlso({ObjectFactory.class})
public interface OutboundServicePortType {

    /**
     * 新增采购入库
     * 
     * @param ptOutboundOrderInfo
     */
    @WebMethod(operationName = "AddOutboundOrder", action = "OutboundService/AddOutboundOrder")
    @WebResult(name = "AddOutboundOrderResult", targetNamespace = "OutboundService")
    @RequestWrapper(localName = "AddOutboundOrder", targetNamespace = "OutboundService")
    @ResponseWrapper(localName = "AddOutboundOrderResponse", targetNamespace = "OutboundService")
    public ResultInfo addOutboundOrder(@WebParam(name = "ptOutboundOrderInfo",
        targetNamespace = "OutboundService") PtOutboundOrderInfo ptOutboundOrderInfo);

}
