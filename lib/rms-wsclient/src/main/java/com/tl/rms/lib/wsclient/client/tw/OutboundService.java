
package com.tl.rms.lib.wsclient.client.tw;

import java.net.MalformedURLException;
import java.net.URL;

import javax.xml.namespace.QName;

import jakarta.xml.ws.*;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebServiceClient(name = "OutboundService", targetNamespace = "OutboundService", wsdlLocation = "http://192.168.220.90:7001/TLSI_0049/processes/OutboundService?wsdl")
public class OutboundService
    extends Service
{

    private final static URL OUTBOUNDSERVICE_WSDL_LOCATION;
    private final static WebServiceException OUTBOUNDSERVICE_EXCEPTION;
    private final static QName OUTBOUNDSERVICE_QNAME = new QName("OutboundService", "OutboundService");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://192.168.220.90:7001/TLSI_0049/processes/OutboundService?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        OUTBOUNDSERVICE_WSDL_LOCATION = url;
        OUTBOUNDSERVICE_EXCEPTION = e;
    }

    public OutboundService() {
        super(__getWsdlLocation(), OUTBOUNDSERVICE_QNAME);
    }

    public OutboundService(WebServiceFeature... features) {
        super(__getWsdlLocation(), OUTBOUNDSERVICE_QNAME, features);
    }

    public OutboundService(URL wsdlLocation) {
        super(wsdlLocation, OUTBOUNDSERVICE_QNAME);
    }

    public OutboundService(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, OUTBOUNDSERVICE_QNAME, features);
    }

    public OutboundService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public OutboundService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return
     *     returns OutboundServicePortType
     */
    @WebEndpoint(name = "OutboundServiceSOAP11port_http")
    public OutboundServicePortType getOutboundServiceSOAP11PortHttp() {
        return super.getPort(new QName("OutboundService", "OutboundServiceSOAP11port_http"), OutboundServicePortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns OutboundServicePortType
     */
    @WebEndpoint(name = "OutboundServiceSOAP11port_http")
    public OutboundServicePortType getOutboundServiceSOAP11PortHttp(WebServiceFeature... features) {
        return super.getPort(new QName("OutboundService", "OutboundServiceSOAP11port_http"), OutboundServicePortType.class, features);
    }

    /**
     * 
     * @return
     *     returns OutboundServicePortType
     */
    @WebEndpoint(name = "OutboundServiceSOAP12port_http")
    public OutboundServicePortType getOutboundServiceSOAP12PortHttp() {
        return super.getPort(new QName("OutboundService", "OutboundServiceSOAP12port_http"), OutboundServicePortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns OutboundServicePortType
     */
    @WebEndpoint(name = "OutboundServiceSOAP12port_http")
    public OutboundServicePortType getOutboundServiceSOAP12PortHttp(WebServiceFeature... features) {
        return super.getPort(new QName("OutboundService", "OutboundServiceSOAP12port_http"), OutboundServicePortType.class, features);
    }

    /**
     * 
     * @return
     *     returns OutboundServicePortType
     */
    @WebEndpoint(name = "OutboundServiceHttpport")
    public OutboundServicePortType getOutboundServiceHttpport() {
        return super.getPort(new QName("OutboundService", "OutboundServiceHttpport"), OutboundServicePortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns OutboundServicePortType
     */
    @WebEndpoint(name = "OutboundServiceHttpport")
    public OutboundServicePortType getOutboundServiceHttpport(WebServiceFeature... features) {
        return super.getPort(new QName("OutboundService", "OutboundServiceHttpport"), OutboundServicePortType.class, features);
    }

    private static URL __getWsdlLocation() {
        if (OUTBOUNDSERVICE_EXCEPTION!= null) {
            throw OUTBOUNDSERVICE_EXCEPTION;
        }
        return OUTBOUNDSERVICE_WSDL_LOCATION;
    }

}
