package com.tl.rms.lib.wsclient.client.tw.request.inbound;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * TW入库订单明细信息数组
 * <AUTHOR>
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ArrayOfPtInboundOrderDetailInfo", propOrder = {
    "ptInboundOrderDetailInfo"
})
public class ArrayOfPtInboundOrderDetailInfo {

    @XmlElement(name = "PtInboundOrderDetailInfo", nillable = true)
    protected List<PtInboundOrderDetailInfo> ptInboundOrderDetailInfo;

    public List<PtInboundOrderDetailInfo> getPtInboundOrderDetailInfo() {
        if (ptInboundOrderDetailInfo == null) {
            ptInboundOrderDetailInfo = new ArrayList<>();
        }
        return this.ptInboundOrderDetailInfo;
    }
}
