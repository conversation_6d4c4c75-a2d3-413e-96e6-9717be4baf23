package com.tl.rms.lib.wsclient.vo;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 奇门接口通用返回结果
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "response")
@AllArgsConstructor
@NoArgsConstructor
public class QiMenResponse {

    @XmlElement(name = "flag")
    private String flag;

    /**
     * 响应码，成功为0，失败为1
     */
    @XmlElement(name = "code")
    private Integer code;

    /**
     * 响应信息
     */
    @XmlElement(name = "message")
    private String message;


    public static QiMenResponse success() {
        return new QiMenResponse("success", 0, null);
    }

    public static QiMenResponse success(String message) {
        return new QiMenResponse("success", 0, message);
    }

    public static QiMenResponse failure(String message) {
        return new QiMenResponse("failure", 1, message);
    }
}
