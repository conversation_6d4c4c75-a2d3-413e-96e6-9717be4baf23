
package com.tl.rms.lib.wsclient.client.ebs.signsync;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>SignInfo complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="SignInfo">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="OrderReleaseClass" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CustomerOrderId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="DestLocationGid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ErpDeliveryNo" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CustomerId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="OwnerId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="TransactionDate" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ExternalOrderId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OrderId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="DetailItems">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="SignInfoItem" type="{http://www.putiantaili.com/soa/esb/}SignInfoItem" maxOccurs="unbounded"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SignInfo", propOrder = {
    "orderReleaseClass",
    "customerOrderId",
    "destLocationGid",
    "erpDeliveryNo",
    "customerId",
    "ownerId",
    "transactionDate",
    "externalOrderId",
    "orderId",
    "detailItems"
})
public class SignInfo {

    @XmlElement(name = "OrderReleaseClass", required = true)
    protected String orderReleaseClass;
    @XmlElement(name = "CustomerOrderId", required = true)
    protected String customerOrderId;
    @XmlElement(name = "DestLocationGid", required = true)
    protected String destLocationGid;
    @XmlElement(name = "ErpDeliveryNo", required = true)
    protected String erpDeliveryNo;
    @XmlElement(name = "CustomerId", required = true)
    protected String customerId;
    @XmlElement(name = "OwnerId", required = true)
    protected String ownerId;
    @XmlElement(name = "TransactionDate")
    protected String transactionDate;
    @XmlElement(name = "ExternalOrderId")
    protected String externalOrderId;
    @XmlElement(name = "OrderId")
    protected String orderId;
    @XmlElement(name = "DetailItems", required = true)
    protected DetailItems detailItems;

    /**
     * 获取orderReleaseClass属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOrderReleaseClass() {
        return orderReleaseClass;
    }

    /**
     * 设置orderReleaseClass属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOrderReleaseClass(String value) {
        this.orderReleaseClass = value;
    }

    /**
     * 获取customerOrderId属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getCustomerOrderId() {
        return customerOrderId;
    }

    /**
     * 设置customerOrderId属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCustomerOrderId(String value) {
        this.customerOrderId = value;
    }

    /**
     * 获取destLocationGid属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getDestLocationGid() {
        return destLocationGid;
    }

    /**
     * 设置destLocationGid属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setDestLocationGid(String value) {
        this.destLocationGid = value;
    }

    /**
     * 获取erpDeliveryNo属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getErpDeliveryNo() {
        return erpDeliveryNo;
    }

    /**
     * 设置erpDeliveryNo属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setErpDeliveryNo(String value) {
        this.erpDeliveryNo = value;
    }

    /**
     * 获取customerId属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getCustomerId() {
        return customerId;
    }

    /**
     * 设置customerId属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCustomerId(String value) {
        this.customerId = value;
    }

    /**
     * 获取ownerId属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOwnerId() {
        return ownerId;
    }

    /**
     * 设置ownerId属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOwnerId(String value) {
        this.ownerId = value;
    }

    /**
     * 获取transactionDate属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getTransactionDate() {
        return transactionDate;
    }

    /**
     * 设置transactionDate属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setTransactionDate(String value) {
        this.transactionDate = value;
    }

    /**
     * 获取externalOrderId属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getExternalOrderId() {
        return externalOrderId;
    }

    /**
     * 设置externalOrderId属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setExternalOrderId(String value) {
        this.externalOrderId = value;
    }

    /**
     * 获取orderId属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * 设置orderId属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOrderId(String value) {
        this.orderId = value;
    }

    /**
     * 获取detailItems属性的值。
     *
     * @return
     *     possible object is
     *     {@link DetailItems }
     *
     */
    public DetailItems getDetailItems() {
        return detailItems;
    }

    /**
     * 设置detailItems属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link DetailItems }
     *
     */
    public void setDetailItems(DetailItems value) {
        this.detailItems = value;
    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="SignInfoItem" type="{http://www.putiantaili.com/soa/esb/}SignInfoItem" maxOccurs="unbounded"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "signInfoItem"
    })
    public static class DetailItems {

        @XmlElement(name = "SignInfoItem", required = true)
        protected List<SignInfoItem> signInfoItem;

        /**
         * Gets the value of the signInfoItem property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the signInfoItem property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getSignInfoItem().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link SignInfoItem }
         * 
         * 
         */
        public List<SignInfoItem> getSignInfoItem() {
            if (signInfoItem == null) {
                signInfoItem = new ArrayList<SignInfoItem>();
            }
            return this.signInfoItem;
        }

    }

}
