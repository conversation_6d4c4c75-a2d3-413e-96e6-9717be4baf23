package com.tl.rms.lib.wsclient.client.tw.response;

import lombok.Data;
import org.springframework.http.HttpStatus;

@Data
public class Response {

    /**
     * 0：调用成功；非0：失败
     */
    protected Integer returnCode;

    protected String errorMsg;

    /**
     * 时间戳
     */
    protected String dateTime;

    private static final int NO_RESPONSE_CODE = -1;
    public static final String NO_RESPONSE = "No Response";

    private static final int UNDO_RESPONSE_CODE = -2;
    public static final String UNDO_RESPONSE = "Undo";

    public Response() {
    }

    public Response(Integer returnCode, String errorMsg) {
        this.returnCode = returnCode;
        this.errorMsg = errorMsg;
    }

    public Response(Integer returnCode, String errorMsg, String dateTime) {
        this.returnCode = returnCode;
        this.errorMsg = errorMsg;
        this.dateTime = dateTime;
    }

    public static Response badRequest() {
        return new Response(HttpStatus.BAD_REQUEST.value(), HttpStatus.BAD_REQUEST.getReasonPhrase());
    }

    public static Response noResponse() {
        return new Response(NO_RESPONSE_CODE, NO_RESPONSE);
    }

    public static Response undo() {
        return new Response(UNDO_RESPONSE_CODE, UNDO_RESPONSE);
    }

}
