package com.tl.rms.lib.wsclient.client.tw.request.inbound;

import com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder.PotevioObj;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TW入库订单信息
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PtInboundOrderInfo", propOrder = {
    "whID",
    "customerOrderID",
    "externalOrderID",
    "tmsTask",
    "orderType",
    "orderDate",
    "requireDeliveryDate",
    "requirementsArrivalDate",
    "ownerID",
    "customerID",
    "vendorName",
    "deliveryToName",
    "deliveryToContact",
    "deliveryToPhone",
    "deliveryToProvince",
    "deliveryToCity",
    "deliveryToAddress",
    "udf01",
    "udf05",
    "udf08",
    "trackingNumber",
    "carrierName",
    "realDepartureTime",
    "estimatedArrivalTime",
    "remark",
    "ediSyncFlag",
    "ptInboundOrderDetailInfos",
    "source1",
    "source2",
    "source3"
})
@XmlRootElement(name = "ptInboundOrderInfo", namespace = "InboundService")
public class PtInboundOrderInfo extends PotevioObj {

    @XmlElement(name = "WhID")
    protected String whID;

    @XmlElement(name = "CustomerOrderID")
    protected String customerOrderID;

    @XmlElement(name = "ExternalOrderID")
    protected String externalOrderID;

    @XmlElement(name = "TmsTask")
    protected String tmsTask;

    @XmlElement(name = "OrderType")
    protected String orderType;

    @XmlElement(name = "OrderDate")
    protected String orderDate;

    @XmlElement(name = "RequireDeliveryDate")
    protected String requireDeliveryDate;

    @XmlElement(name = "RequirementsArrivalDate")
    protected String requirementsArrivalDate;

    @XmlElement(name = "OwnerID")
    protected String ownerID;

    @XmlElement(name = "CustomerID")
    protected String customerID;

    @XmlElement(name = "VendorName")
    protected String vendorName;

    @XmlElement(name = "DeliveryToName")
    protected String deliveryToName;

    @XmlElement(name = "DeliveryToContact")
    protected String deliveryToContact;

    @XmlElement(name = "DeliveryToPhone")
    protected String deliveryToPhone;

    @XmlElement(name = "DeliveryToProvince")
    protected String deliveryToProvince;

    @XmlElement(name = "DeliveryToCity")
    protected String deliveryToCity;

    @XmlElement(name = "DeliveryToAddress")
    protected String deliveryToAddress;

    @XmlElement(name = "Udf01")
    protected String udf01;

    @XmlElement(name = "Udf05")
    protected String udf05;

    @XmlElement(name = "Udf08")
    protected String udf08;

    @XmlElement(name = "TrackingNumber")
    protected String trackingNumber;

    @XmlElement(name = "CarrierName")
    protected String carrierName;

    @XmlElement(name = "RealDepartureTime")
    protected String realDepartureTime;

    @XmlElement(name = "EstimatedArrivalTime")
    protected String estimatedArrivalTime;

    @XmlElement(name = "Remark")
    protected String remark;

    @XmlElement(name = "EdiSyncFlag")
    protected String ediSyncFlag;

    @XmlElement(name = "PtInboundOrderDetailInfos")
    protected ArrayOfPtInboundOrderDetailInfo ptInboundOrderDetailInfos;

    @XmlElement(name = "Source1")
    protected String source1;

    @XmlElement(name = "Source2")
    protected String source2;

    @XmlElement(name = "Source3")
    protected String source3;
}
