
package com.tl.rms.lib.wsclient.client.ebs.ordersync;

import jakarta.xml.ws.*;

import javax.xml.namespace.QName;
import java.net.MalformedURLException;
import java.net.URL;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebServiceClient(name = "LoadOrderService", targetNamespace = "http://www.putiantaili.com/soa/esb/", wsdlLocation = "http://192.168.220.90:7001/TLSI_0027/processes/LoadOrderService?wsdl")
public class LoadOrderService
    extends Service
{

    private final static URL LOADORDERSERVICE_WSDL_LOCATION;
    private final static WebServiceException LOADORDERSERVICE_EXCEPTION;
    private final static QName LOADORDERSERVICE_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "LoadOrderService");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://192.168.220.90:7001/TLSI_0027/processes/LoadOrderService?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        LOADORDERSERVICE_WSDL_LOCATION = url;
        LOADORDERSERVICE_EXCEPTION = e;
    }

    public LoadOrderService() {
        super(__getWsdlLocation(), LOADORDERSERVICE_QNAME);
    }

    public LoadOrderService(WebServiceFeature... features) {
        super(__getWsdlLocation(), LOADORDERSERVICE_QNAME, features);
    }

    public LoadOrderService(URL wsdlLocation) {
        super(wsdlLocation, LOADORDERSERVICE_QNAME);
    }

    public LoadOrderService(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, LOADORDERSERVICE_QNAME, features);
    }

    public LoadOrderService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public LoadOrderService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return
     *     returns LoadOrderServicePortType
     */
    @WebEndpoint(name = "LoadOrderServiceSOAP11port_http")
    public LoadOrderServicePortType getLoadOrderServiceSOAP11PortHttp() {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "LoadOrderServiceSOAP11port_http"), LoadOrderServicePortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns LoadOrderServicePortType
     */
    @WebEndpoint(name = "LoadOrderServiceSOAP11port_http")
    public LoadOrderServicePortType getLoadOrderServiceSOAP11PortHttp(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "LoadOrderServiceSOAP11port_http"), LoadOrderServicePortType.class, features);
    }

    /**
     *
     * @return
     *     returns LoadOrderServicePortType
     */
    @WebEndpoint(name = "LoadOrderServiceSOAP12port_http")
    public LoadOrderServicePortType getLoadOrderServiceSOAP12PortHttp() {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "LoadOrderServiceSOAP12port_http"), LoadOrderServicePortType.class);
    }

    /**
     *
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns LoadOrderServicePortType
     */
    @WebEndpoint(name = "LoadOrderServiceSOAP12port_http")
    public LoadOrderServicePortType getLoadOrderServiceSOAP12PortHttp(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "LoadOrderServiceSOAP12port_http"), LoadOrderServicePortType.class, features);
    }

    /**
     *
     * @return
     *     returns LoadOrderServicePortType
     */
    @WebEndpoint(name = "LoadOrderServiceHttpport")
    public LoadOrderServicePortType getLoadOrderServiceHttpport() {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "LoadOrderServiceHttpport"), LoadOrderServicePortType.class);
    }

    /**
     *
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns LoadOrderServicePortType
     */
    @WebEndpoint(name = "LoadOrderServiceHttpport")
    public LoadOrderServicePortType getLoadOrderServiceHttpport(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "LoadOrderServiceHttpport"), LoadOrderServicePortType.class, features);
    }

    private static URL __getWsdlLocation() {
        if (LOADORDERSERVICE_EXCEPTION!= null) {
            throw LOADORDERSERVICE_EXCEPTION;
        }
        return LOADORDERSERVICE_WSDL_LOCATION;
    }

}
