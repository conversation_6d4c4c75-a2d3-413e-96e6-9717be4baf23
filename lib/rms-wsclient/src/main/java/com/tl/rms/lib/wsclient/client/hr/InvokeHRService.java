package com.tl.rms.lib.wsclient.client.hr;

import com.tl.rms.lib.wsclient.client.hr.login.InterfaceRequest;
import com.tl.rms.lib.wsclient.config.WebServiceClientConfig;
import com.tl.rms.lib.wsclient.client.tw.response.Response;
import com.tl.rms.util.AesSecurityPlus;
import com.tl.rms.util.json.JsonProcessUtil;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.Unmarshaller;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamReader;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * 调用HR接口
 *
 * <AUTHOR>
 * @date 2025/3/20
 */
@Slf4j
@Service
public class InvokeHRService {

    /**
     * IHR登录接口 双向加密
     */
    private static final String LOGIN_WSDL = "/PSIGW/PeopleSoftServiceListeningConnector/PSFT_HR/HPS_CK_LOGIN.1.wsdl";

    @Autowired
    private WebServiceClientConfig webServiceClientConfig;

    private static final String IHR_LOGIN_KEY = "pptt-ihr-PSlogin";


    public static void main(String[] args) {
        InterfaceRequest request = new InterfaceRequest();
        request.setFLAG("VCRM");
        request.setOPRID("TL000076");
        request.setPASSWORD("test123");
        InvokeHRService invokeHRService = new InvokeHRService();
        invokeHRService.ihrLogin(request);
    }

    @SneakyThrows
    public Response ihrLogin(InterfaceRequest request) {

        log.info("ihrLogin request: {}", request == null ? null : JsonProcessUtil.beanToJson(request));
        if (request == null) {
            return Response.badRequest();
        }
        Response response = new Response();

        try {
            AesSecurityPlus aesSecurityPlus = AesSecurityPlus.getInstance(IHR_LOGIN_KEY);
            request.setOPRID(aesSecurityPlus.encrypt(request.getOPRID()));
            request.setPASSWORD(aesSecurityPlus.encrypt(request.getPASSWORD()));
        } catch (Exception e) {
            log.error("IHR登录参数加密失败", e);

            return response;
        }

        StringBuilder sb = new StringBuilder("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:sch=\"http://xmlns.oracle.com/Enterprise/Tools/schemas\"><soapenv:Header/><soapenv:Body><sch:InterfaceRequest><sch:OPRID>");
        sb.append(request.getOPRID()).append("</sch:OPRID><sch:PASSWORD>").append(request.getPASSWORD()).append("</sch:PASSWORD><sch:FLAG>");
        sb.append(request.getFLAG()).append("</sch:FLAG></sch:InterfaceRequest></soapenv:Body></soapenv:Envelope>");
        String wsXml = sb.toString();

        IHR_InterfaceResponse ihrResponse;
        HttpURLConnection con = null;
        OutputStream out = null;
        InputStream in = null;
        try {
            URL url = new URL(webServiceClientConfig.getEsbWsAddress() + LOGIN_WSDL);
            con = (HttpURLConnection) url.openConnection();
            con.setDoOutput(true);
            con.setDoInput(true);
            con.setRequestMethod("POST");
            con.setUseCaches(false);
            con.setRequestProperty("Content-type", "text/xml");
            con.setRequestProperty("SOAPAction", "\"HPS_LOGIN.v1\"");
            con.setConnectTimeout(5000);
            con.setReadTimeout(5000);
            con.connect();
            out = con.getOutputStream();
            out.write(wsXml.getBytes());
            //接收报文
            in = con.getInputStream();
            //解析报文
            ihrResponse = parseXML(in);
            log.info("ihrLogin response: {}", JsonProcessUtil.beanToJson(ihrResponse));

            if (ihrResponse != null) {
                if (StringUtils.isEmpty(ihrResponse.getHps_inf_status()) && StringUtils.isEmpty(ihrResponse.getHps_inf_info())) {
                    response.setReturnCode(2);
                    response.setErrorMsg("IHR接口无数据返回");
                } else if ("Sucess".equals(ihrResponse.getHps_inf_status())) {
                    response.setReturnCode(0);
                } else if ("Error".equals(ihrResponse.getHps_inf_status())) {
                    response.setReturnCode(1);
                    response.setErrorMsg("HR系统登录错误：" + ihrResponse.getHps_inf_info());
                }
            } else {
                response.setReturnCode(2);
                response.setErrorMsg("IHR接口无数据返回");
            }
        } catch (Exception e) {
            log.error("调用IHR登录接口失败", e);
            response.setReturnCode(2);
            response.setErrorMsg("调用IHR接口登录失败：" + e.getMessage().substring(0, Math.min(e.getMessage().length(), 258)));
        } finally {
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(out);
            if (con != null) {
                con.disconnect();
            }
        }
        return response;

    }

    private IHR_InterfaceResponse parseXML(InputStream in) {
        XMLInputFactory xif = XMLInputFactory.newFactory();
        XMLStreamReader xsr = null;
        IHR_InterfaceResponse ihrResponse = null;
        try {
            xsr = xif.createXMLStreamReader(in);
            xsr.nextTag();
            while (!"InterfaceResponse".equals(xsr.getLocalName())) {
                xsr.nextTag();
            }

            JAXBContext jc = JAXBContext.newInstance(IHR_InterfaceResponse.class);
            Unmarshaller unmarshaller = jc.createUnmarshaller();
            JAXBElement<IHR_InterfaceResponse> jb = unmarshaller.unmarshal(xsr, IHR_InterfaceResponse.class);
            ihrResponse = jb.getValue();
        } catch (Exception e) {
            log.error("IHR parseXML", e);
        } finally {
            if (xsr != null) {
                try {
                    xsr.close();
                } catch (Exception e) {
                    log.error("IHR parseXML close", e);
                }
            }
        }

        return ihrResponse;
    }

}
