package com.tl.rms.lib.wsclient.client.jky.constant;

public class JackyunConstant {

    /**
     * 方法名 查询销售渠道
     */
    public static final String METHOD_CHANNEL_QUERY = "erp.sales.get";

    /**
     * 方法名 新增销售渠道
     */
    public static final String METHOD_CHANNEL_CREATE = "erp.salechannel.create.v2";

    /**
     * 方法名 更新销售渠道
     */
    public static final String METHOD_CHANNEL_UPDATE = "erp.salechannel.update";

    /**
     * 方法名 查询公司
     */
    public static final String METHOD_QUERY_COMPANY = "erp.company.query";

    /**
     * 方法名 查询部门
     */
    public static final String METHOD_QUERY_DEPT = "erp.depart.query";

    /**
     * 方法名 查询仓库
     */
    public static final String METHOD_QUERY_WAREHOUSE = "erp.warehouse.get";

    /**
     * 方法名 创建物料
     */
    public static final String METHOD_MATERIAL = "erp.goods.skuimportbatch";

    /**
     * 方法名 创建价格
     */
    public static final String METHOD_PRICE = "erp.salesgoodsskuprice.create";

    /**
     * 销售单查询 方法名
     */
    public static final String METHOD_ORDER = "oms.trade.fullinfoget";

    /**
     * 销售单查询（定制，包含淘系销售单）
     */
    public static final String METHOD_ORDER_CUSTOM = "jackyun.tradenotsensitiveinfos.list.get";

    /**
     * 分页查询退换补货单 方法名
     */
    public static final String METHOD_CHANGE = "ass-business.returnchange.fullinfoget";

    /**
     * 方法名 网店订单列表详情
     */
    public static final String METHOD_GET_INTERNET_SHOP_ORDER = "omsapi-business.order.get";

}
