
package com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder;

import jakarta.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.pttl.tlmall.lib.wsclient.client.tw.ordersync package.
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.pttl.tlmall.lib.wsclient.client.tw.ordersync
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link AddOutboundOrderResponse }
     * 
     */
    public AddOutboundOrderResponse createAddOutboundOrderResponse() {
        return new AddOutboundOrderResponse();
    }

    /**
     * Create an instance of {@link ResultInfo }
     * 
     */
    public ResultInfo createResultInfo() {
        return new ResultInfo();
    }

    /**
     * Create an instance of {@link AddOutboundOrder }
     * 
     */
    public AddOutboundOrder createAddOutboundOrder() {
        return new AddOutboundOrder();
    }

    /**
     * Create an instance of {@link PtOutboundOrderInfo }
     * 
     */
    public PtOutboundOrderInfo createPtOutboundOrderInfo() {
        return new PtOutboundOrderInfo();
    }

    /**
     * Create an instance of {@link ArrayOfPtOutboundOrderDetailInfo }
     * 
     */
    public ArrayOfPtOutboundOrderDetailInfo createArrayOfPtOutboundOrderDetailInfo() {
        return new ArrayOfPtOutboundOrderDetailInfo();
    }

    /**
     * Create an instance of {@link PtOutboundOrderDetailInfo }
     * 
     */
    public PtOutboundOrderDetailInfo createPtOutboundOrderDetailInfo() {
        return new PtOutboundOrderDetailInfo();
    }

    /**
     * Create an instance of {@link PotevioObj }
     * 
     */
    public PotevioObj createPotevioObj() {
        return new PotevioObj();
    }

}
