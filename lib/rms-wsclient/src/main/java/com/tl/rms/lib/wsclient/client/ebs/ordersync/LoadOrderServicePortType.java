
package com.tl.rms.lib.wsclient.client.ebs.ordersync;

import jakarta.jws.WebMethod;
import jakarta.jws.WebParam;
import jakarta.jws.WebResult;
import jakarta.jws.WebService;
import jakarta.jws.soap.SOAPBinding;
import jakarta.xml.bind.annotation.XmlSeeAlso;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "LoadOrderServicePortType", targetNamespace = "http://www.putiantaili.com/soa/esb/")
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
@XmlSeeAlso({
    ObjectFactory.class
})
public interface LoadOrderServicePortType {


    /**
     * 
     * @param parameters
     * @return
     *     returns com.pttl.tlmall.lib.wsclient.client.ebs.ordersync.MessageResponseType
     */
    @WebMethod
    @WebResult(name = "MessageResponse", targetNamespace = "http://www.putiantaili.com/soa/esb/", partName = "parameters")
    public MessageResponseType load(
        @WebParam(name = "MessageRequest", targetNamespace = "http://www.putiantaili.com/soa/esb/", partName = "parameters")
        MessageRequestType parameters);

}
