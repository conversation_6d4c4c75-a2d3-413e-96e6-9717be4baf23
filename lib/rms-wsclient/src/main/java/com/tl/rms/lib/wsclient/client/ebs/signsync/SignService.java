
package com.tl.rms.lib.wsclient.client.ebs.signsync;

import jakarta.xml.ws.*;

import javax.xml.namespace.QName;
import java.net.MalformedURLException;
import java.net.URL;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebServiceClient(name = "SignService", targetNamespace = "http://www.putiantaili.com/soa/esb/", wsdlLocation = "http://192.168.220.90:7001/TLSI_0005/processes/SignService?wsdl")
public class SignService
    extends Service
{

    private final static URL SIGNSERVICE_WSDL_LOCATION;
    private final static WebServiceException SIGNSERVICE_EXCEPTION;
    private final static QName SIGNSERVICE_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "SignService");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://192.168.220.90:7001/TLSI_0005/processes/SignService?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        SIGNSERVICE_WSDL_LOCATION = url;
        SIGNSERVICE_EXCEPTION = e;
    }

    public SignService() {
        super(__getWsdlLocation(), SIGNSERVICE_QNAME);
    }

    public SignService(WebServiceFeature... features) {
        super(__getWsdlLocation(), SIGNSERVICE_QNAME, features);
    }

    public SignService(URL wsdlLocation) {
        super(wsdlLocation, SIGNSERVICE_QNAME);
    }

    public SignService(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, SIGNSERVICE_QNAME, features);
    }

    public SignService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public SignService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return
     *     returns SignServicePortType
     */
    @WebEndpoint(name = "SignServiceSOAP11port_http")
    public SignServicePortType getSignServiceSOAP11PortHttp() {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "SignServiceSOAP11port_http"), SignServicePortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns SignServicePortType
     */
    @WebEndpoint(name = "SignServiceSOAP11port_http")
    public SignServicePortType getSignServiceSOAP11PortHttp(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "SignServiceSOAP11port_http"), SignServicePortType.class, features);
    }

    /**
     *
     * @return
     *     returns SignServicePortType
     */
    @WebEndpoint(name = "SignServiceSOAP12port_http")
    public SignServicePortType getSignServiceSOAP12PortHttp() {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "SignServiceSOAP12port_http"), SignServicePortType.class);
    }

    /**
     *
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns SignServicePortType
     */
    @WebEndpoint(name = "SignServiceSOAP12port_http")
    public SignServicePortType getSignServiceSOAP12PortHttp(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "SignServiceSOAP12port_http"), SignServicePortType.class, features);
    }

    /**
     *
     * @return
     *     returns SignServicePortType
     */
    @WebEndpoint(name = "SignServiceHttpport")
    public SignServicePortType getSignServiceHttpport() {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "SignServiceHttpport"), SignServicePortType.class);
    }

    /**
     *
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns SignServicePortType
     */
    @WebEndpoint(name = "SignServiceHttpport")
    public SignServicePortType getSignServiceHttpport(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "SignServiceHttpport"), SignServicePortType.class, features);
    }

    private static URL __getWsdlLocation() {
        if (SIGNSERVICE_EXCEPTION!= null) {
            throw SIGNSERVICE_EXCEPTION;
        }
        return SIGNSERVICE_WSDL_LOCATION;
    }

}
