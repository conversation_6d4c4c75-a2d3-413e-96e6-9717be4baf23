package com.tl.rms.lib.wsclient.client.tw;

import jakarta.xml.ws.*;

import javax.xml.namespace.QName;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * TW入库服务
 * <AUTHOR>
 */
@WebServiceClient(name = "InboundService", targetNamespace = "InboundService", wsdlLocation = "http://192.168.220.90:7001/TLSI_0049/processes/InboundService?wsdl")
public class InboundService extends Service {

    private final static URL INBOUNDSERVICE_WSDL_LOCATION;
    private final static WebServiceException INBOUNDSERVICE_EXCEPTION;
    private final static QName INBOUNDSERVICE_QNAME = new QName("InboundService", "InboundService");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://192.168.220.90:7001/TLSI_0049/processes/InboundService?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        INBOUNDSERVICE_WSDL_LOCATION = url;
        INBOUNDSERVICE_EXCEPTION = e;
    }

    public InboundService() {
        super(__getWsdlLocation(), INBOUNDSERVICE_QNAME);
    }

    public InboundService(WebServiceFeature... features) {
        super(__getWsdlLocation(), INBOUNDSERVICE_QNAME, features);
    }

    public InboundService(URL wsdlLocation) {
        super(wsdlLocation, INBOUNDSERVICE_QNAME);
    }

    public InboundService(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, INBOUNDSERVICE_QNAME, features);
    }

    public InboundService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public InboundService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return
     *     returns InboundServicePortType
     */
    @WebEndpoint(name = "InboundServiceSOAP11port_http")
    public InboundServicePortType getInboundServiceSOAP11PortHttp() {
        return super.getPort(new QName("InboundService", "InboundServiceSOAP11port_http"), InboundServicePortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns InboundServicePortType
     */
    @WebEndpoint(name = "InboundServiceSOAP11port_http")
    public InboundServicePortType getInboundServiceSOAP11PortHttp(WebServiceFeature... features) {
        return super.getPort(new QName("InboundService", "InboundServiceSOAP11port_http"), InboundServicePortType.class, features);
    }

    private static URL __getWsdlLocation() {
        if (INBOUNDSERVICE_EXCEPTION != null) {
            throw INBOUNDSERVICE_EXCEPTION;
        }
        return INBOUNDSERVICE_WSDL_LOCATION;
    }
}
