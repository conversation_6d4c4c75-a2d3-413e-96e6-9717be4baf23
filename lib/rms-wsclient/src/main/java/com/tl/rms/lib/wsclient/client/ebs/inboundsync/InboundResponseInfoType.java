
package com.tl.rms.lib.wsclient.client.ebs.inboundsync;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>InboundResponseInfoType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="InboundResponseInfoType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ReceiptId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="OwnerId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ReceiptType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CustomerOrderId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ExternalOrderId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ReceiptDate" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="CustomerId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="VendorName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf01" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf02" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf03" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf04" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf05" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf06" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf07" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf08" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf09" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf10" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="TransactionDate" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="DetailItems">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="InboundResponseItem" type="{http://www.putiantaili.com/soa/esb/}InboundResponseItemType" maxOccurs="unbounded"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "InboundResponseInfoType", propOrder = {
    "receiptId",
    "ownerId",
    "receiptType",
    "customerOrderId",
    "externalOrderId",
    "receiptDate",
    "customerId",
    "vendorName",
    "udf01",
    "udf02",
    "udf03",
    "udf04",
    "udf05",
    "udf06",
    "udf07",
    "udf08",
    "udf09",
    "udf10",
    "transactionDate",
    "detailItems"
})
public class InboundResponseInfoType {

    @XmlElement(name = "ReceiptId", required = true)
    protected String receiptId;
    @XmlElement(name = "OwnerId", required = true)
    protected String ownerId;
    @XmlElement(name = "ReceiptType", required = true)
    protected String receiptType;
    @XmlElement(name = "CustomerOrderId", required = true)
    protected String customerOrderId;
    @XmlElement(name = "ExternalOrderId", required = true)
    protected String externalOrderId;
    @XmlElement(name = "ReceiptDate")
    protected long receiptDate;
    @XmlElement(name = "CustomerId")
    protected String customerId;
    @XmlElement(name = "VendorName")
    protected String vendorName;
    @XmlElement(name = "Udf01")
    protected String udf01;
    @XmlElement(name = "Udf02")
    protected String udf02;
    @XmlElement(name = "Udf03")
    protected String udf03;
    @XmlElement(name = "Udf04")
    protected String udf04;
    @XmlElement(name = "Udf05")
    protected String udf05;
    @XmlElement(name = "Udf06")
    protected String udf06;
    @XmlElement(name = "Udf07")
    protected String udf07;
    @XmlElement(name = "Udf08")
    protected String udf08;
    @XmlElement(name = "Udf09")
    protected String udf09;
    @XmlElement(name = "Udf10")
    protected String udf10;
    @XmlElement(name = "TransactionDate")
    protected String transactionDate;
    @XmlElement(name = "DetailItems", required = true)
    protected DetailItems detailItems;

    /**
     * 获取receiptId属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getReceiptId() {
        return receiptId;
    }

    /**
     * 设置receiptId属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setReceiptId(String value) {
        this.receiptId = value;
    }

    /**
     * 获取ownerId属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOwnerId() {
        return ownerId;
    }

    /**
     * 设置ownerId属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOwnerId(String value) {
        this.ownerId = value;
    }

    /**
     * 获取receiptType属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getReceiptType() {
        return receiptType;
    }

    /**
     * 设置receiptType属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setReceiptType(String value) {
        this.receiptType = value;
    }

    /**
     * 获取customerOrderId属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getCustomerOrderId() {
        return customerOrderId;
    }

    /**
     * 设置customerOrderId属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCustomerOrderId(String value) {
        this.customerOrderId = value;
    }

    /**
     * 获取externalOrderId属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getExternalOrderId() {
        return externalOrderId;
    }

    /**
     * 设置externalOrderId属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setExternalOrderId(String value) {
        this.externalOrderId = value;
    }

    /**
     * 获取receiptDate属性的值。
     *
     */
    public long getReceiptDate() {
        return receiptDate;
    }

    /**
     * 设置receiptDate属性的值。
     *
     */
    public void setReceiptDate(long value) {
        this.receiptDate = value;
    }

    /**
     * 获取customerId属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getCustomerId() {
        return customerId;
    }

    /**
     * 设置customerId属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCustomerId(String value) {
        this.customerId = value;
    }

    /**
     * 获取vendorName属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getVendorName() {
        return vendorName;
    }

    /**
     * 设置vendorName属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setVendorName(String value) {
        this.vendorName = value;
    }

    /**
     * 获取udf01属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf01() {
        return udf01;
    }

    /**
     * 设置udf01属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf01(String value) {
        this.udf01 = value;
    }

    /**
     * 获取udf02属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf02() {
        return udf02;
    }

    /**
     * 设置udf02属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf02(String value) {
        this.udf02 = value;
    }

    /**
     * 获取udf03属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf03() {
        return udf03;
    }

    /**
     * 设置udf03属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf03(String value) {
        this.udf03 = value;
    }

    /**
     * 获取udf04属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf04() {
        return udf04;
    }

    /**
     * 设置udf04属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf04(String value) {
        this.udf04 = value;
    }

    /**
     * 获取udf05属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf05() {
        return udf05;
    }

    /**
     * 设置udf05属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf05(String value) {
        this.udf05 = value;
    }

    /**
     * 获取udf06属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf06() {
        return udf06;
    }

    /**
     * 设置udf06属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf06(String value) {
        this.udf06 = value;
    }

    /**
     * 获取udf07属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf07() {
        return udf07;
    }

    /**
     * 设置udf07属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf07(String value) {
        this.udf07 = value;
    }

    /**
     * 获取udf08属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf08() {
        return udf08;
    }

    /**
     * 设置udf08属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf08(String value) {
        this.udf08 = value;
    }

    /**
     * 获取udf09属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf09() {
        return udf09;
    }

    /**
     * 设置udf09属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf09(String value) {
        this.udf09 = value;
    }

    /**
     * 获取udf10属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf10() {
        return udf10;
    }

    /**
     * 设置udf10属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf10(String value) {
        this.udf10 = value;
    }

    /**
     * 获取transactionDate属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getTransactionDate() {
        return transactionDate;
    }

    /**
     * 设置transactionDate属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setTransactionDate(String value) {
        this.transactionDate = value;
    }

    /**
     * 获取detailItems属性的值。
     *
     * @return
     *     possible object is
     *     {@link DetailItems }
     *
     */
    public DetailItems getDetailItems() {
        return detailItems;
    }

    /**
     * 设置detailItems属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link DetailItems }
     *
     */
    public void setDetailItems(DetailItems value) {
        this.detailItems = value;
    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="InboundResponseItem" type="{http://www.putiantaili.com/soa/esb/}InboundResponseItemType" maxOccurs="unbounded"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "inboundResponseItem"
    })
    public static class DetailItems {

        @XmlElement(name = "InboundResponseItem", required = true)
        protected List<InboundResponseItemType> inboundResponseItem;

        /**
         * Gets the value of the inboundResponseItem property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the inboundResponseItem property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getInboundResponseItem().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link InboundResponseItemType }
         * 
         * 
         */
        public List<InboundResponseItemType> getInboundResponseItem() {
            if (inboundResponseItem == null) {
                inboundResponseItem = new ArrayList<InboundResponseItemType>();
            }
            return this.inboundResponseItem;
        }

    }

}
