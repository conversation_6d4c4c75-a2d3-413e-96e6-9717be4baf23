package com.tl.rms.lib.wsclient.client.jky;

import com.tl.rms.lib.wsclient.client.jky.constant.JackyunConstant;
import com.tl.rms.lib.wsclient.client.jky.request.*;
import com.tl.rms.lib.wsclient.client.jky.response.JackyunResponse;
import com.tl.rms.lib.wsclient.client.jky.response.JkyWarehouseResponse;
import com.tl.rms.lib.wsclient.client.jky.vo.CompanyVo;
import com.tl.rms.lib.wsclient.client.jky.vo.DepartmentVo;
import com.tl.rms.lib.wsclient.client.jky.vo.JkyWarehouseVo;
import com.tl.rms.lib.wsclient.config.WebServiceClientConfig;
import com.tl.rms.util.json.JsonProcessUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 调用吉客云接口
 *
 * <AUTHOR>
 * @date 2023/5/19
 */
@Slf4j
@Component
public class InvokeJkyService {

    /**
     * 吉客云开放平台API URL
     */
    private static final String JYY_OPEN_API = "/jackyun/openapi";

    @Autowired
    private WebServiceClientConfig wsClientConfig;

    @Value("${spring.profiles.active}")
    private String active;


    /**
     * 调用吉客云开放平台API
     *
     * <AUTHOR>
     * @date 2023/5/19
     */
    private JackyunResponse invokeJkyOpenApiService(JackyunRequest request) {

        log.info("invokeJkyOpenApiService request: {}", JsonProcessUtil.beanToJson(request));
        if (request == null) {
            log.error("调用吉客云开放平台API，参数不可为空");
            return new JackyunResponse(0, "参数为空");
        }

        String url = wsClientConfig.getEsbWsAddress() + JYY_OPEN_API;

        try {
            JackyunResponse response = new RestTemplate().postForObject(url, request, JackyunResponse.class);
            log.info("invokeJkyOpenApiService response: {} {}", request.getMethod(), JsonProcessUtil.beanToJson(response));

            return response;
        } catch (Exception e) {
            log.error("调用吉客云开放平台API异常, method: {}, error: {} ", request.getMethod(), e.getMessage(), e);
            return new JackyunResponse(0, e.getMessage());
        }

    }

    /**
     * 查询销售渠道
     *
     * <AUTHOR>
     * @date 2025/4/1
     */
    public JackyunResponse listChannel(JkyChannelQueryRequest request) {

        JackyunRequest jackyunRequest = new JackyunRequest();
        jackyunRequest.setMethod(JackyunConstant.METHOD_CHANNEL_QUERY);
        jackyunRequest.setBizcontent(JsonProcessUtil.beanToJson(request));

        JackyunResponse response = invokeJkyOpenApiService(jackyunRequest);
        if (response.getCode() != 200) {
            log.info("查询销售渠道失败，msg: {}", response.getMsg());
            return new JackyunResponse();
        }

        return response;
    }

    /**
     * 新增销售渠道
     *
     * <AUTHOR>
     * @date 2025/4/1
     */
    public JackyunResponse createChannel(@RequestBody JkyChannelCreateRequest request) {

        JackyunRequest jackyunRequest = new JackyunRequest();
        jackyunRequest.setMethod(JackyunConstant.METHOD_CHANNEL_CREATE);
        jackyunRequest.setBizcontent(JsonProcessUtil.beanToJson(request));

        return invokeJkyOpenApiService(jackyunRequest);
    }

    /**
     * 更新销售渠道
     *
     * <AUTHOR>
     * @date 2025/4/1
     */
    public JackyunResponse udpateChannel(@RequestBody JkyChannelUpdateRequest request) {

        JackyunRequest jackyunRequest = new JackyunRequest();
        jackyunRequest.setMethod(JackyunConstant.METHOD_CHANNEL_UPDATE);
        jackyunRequest.setBizcontent(JsonProcessUtil.beanToJson(request));

        return invokeJkyOpenApiService(jackyunRequest);
    }

    /**
     * 查询公司
     *
     * <AUTHOR>
     * @date 2025/5/6
     */
    public List<CompanyVo> listCompany(JkyCompanyQueryRequest request) {

        JackyunRequest jackyunRequest = new JackyunRequest();
        jackyunRequest.setMethod(JackyunConstant.METHOD_QUERY_COMPANY);
        jackyunRequest.setBizcontent(JsonProcessUtil.beanToJson(request));

        JackyunResponse response = invokeJkyOpenApiService(jackyunRequest);
        if (response.getCode() != 200) {
            log.info("查询公司失败，msg: {}", response.getMsg());
            return new ArrayList<>();
        }

        if (response.getResult().getData() == null) {
            log.info("查询公司结果为空");
            return new ArrayList<>();
        }

        return JsonProcessUtil.jsonToList(JsonProcessUtil.beanToJson(response.getResult().getData()), CompanyVo.class);
    }

    /**
     * 查询部门
     *
     * <AUTHOR>
     * @date 2025/5/6
     */
    public List<DepartmentVo> listDepartment(JkyDepartmentQueryRequest request) {

        JackyunRequest jackyunRequest = new JackyunRequest();
        jackyunRequest.setMethod(JackyunConstant.METHOD_QUERY_DEPT);
        jackyunRequest.setBizcontent(JsonProcessUtil.beanToJson(request));

        JackyunResponse response = invokeJkyOpenApiService(jackyunRequest);
        if (response.getCode() != 200) {
            log.info("查询部门失败，msg: {}", response.getMsg());
            return new ArrayList<>();
        }

        if (response.getResult().getData() == null) {
            log.info("查询部门结果为空");
            return new ArrayList<>();
        }

        return JsonProcessUtil.jsonToList(JsonProcessUtil.beanToJson(response.getResult().getData()), DepartmentVo.class);
    }

    /**
     * 查询仓库
     *
     * <AUTHOR>
     * @date 2025/5/6
     */
    public List<JkyWarehouseVo> listWarehouse(JkyWarehouseQueryRequest request) {

        JackyunRequest jackyunRequest = new JackyunRequest();
        jackyunRequest.setMethod(JackyunConstant.METHOD_QUERY_WAREHOUSE);
        jackyunRequest.setBizcontent(JsonProcessUtil.beanToJson(request));

        JackyunResponse response = invokeJkyOpenApiService(jackyunRequest);
        if (response.getCode() != 200) {
            log.info("查询仓库失败，msg: {}", response.getMsg());
            return new ArrayList<>();
        }

        if (response.getResult().getData() == null) {
            log.info("查询仓库结果为空");
            return new ArrayList<>();
        }

        JkyWarehouseResponse warehouseResponse = JsonProcessUtil.jsonToBean(JsonProcessUtil.beanToJson(response.getResult().getData()), JkyWarehouseResponse.class);

        if (warehouseResponse == null) {
            log.info("查询仓库结果为空");
            return new ArrayList<>();
        }

        return warehouseResponse.getWarehouseInfo();
    }

    /**
     * 新建物料
     * 批量创建货品
     *
     * <AUTHOR>
     * @date 2025/4/11
     */
    public JackyunResponse createMaterial(@RequestBody List<JkyMaterialCreateRequest> request) {

        JackyunRequest jackyunRequest = new JackyunRequest();
        jackyunRequest.setMethod(JackyunConstant.METHOD_MATERIAL);
        jackyunRequest.setBizcontent(JsonProcessUtil.beanToJson(request));

        return invokeJkyOpenApiService(jackyunRequest);
    }

    /**
     * 新建价格
     * 创建货品价目（已经存在则修改，根据规格、单位、币种定位）
     *
     * <AUTHOR>
     * @date 2025/4/11
     */
    public JackyunResponse createPrice(@RequestBody JkyPriceCreateRequest request) {

        JackyunRequest jackyunRequest = new JackyunRequest();
        jackyunRequest.setMethod(JackyunConstant.METHOD_PRICE);
        jackyunRequest.setBizcontent(JsonProcessUtil.beanToJson(request));

        return invokeJkyOpenApiService(jackyunRequest);
    }

    /**
     * 同步销售单
     *
     * @param orderRequest orderRequest
     * @return JackyunResponse
     * <AUTHOR>
     * @date 2025/9/2
     */
    public JackyunResponse syncOrder(JkyOrderRequest orderRequest) {

        JackyunRequest request = new JackyunRequest();
        request.setMethod(isProdEnv() ? JackyunConstant.METHOD_ORDER_CUSTOM : JackyunConstant.METHOD_ORDER);
        request.setBizcontent(JsonProcessUtil.beanToJson(orderRequest));

        return invokeJkyOpenApiService(request);
    }

    /**
     * 同步吉客云退换补货单
     *
     * @param orderRequest orderRequest
     * @return JackyunResponse
     * <AUTHOR>
     * @date 2025/9/3
     */
    public JackyunResponse syncReturn(JkyOrderRequest orderRequest) {

        JackyunRequest request = new JackyunRequest();
        request.setMethod(JackyunConstant.METHOD_CHANGE);
        request.setBizcontent(JsonProcessUtil.beanToJson(orderRequest));

        return invokeJkyOpenApiService(request);
    }

    /**
     * 是否生产环境
     *
     * <AUTHOR>
     * @date 2023/11/17 15:40
     */
    public boolean isProdEnv() {
        return "prodk8s".equals(active);
    }

    /**
     * 同步吉客云网店订单
     *
     * <AUTHOR>
     * @date 2025/8/14
     */
    public JackyunResponse syncJkyOnlineOrder(@RequestBody JkyOnlineOrderRequest request) {
        JackyunRequest jackyunRequest = new JackyunRequest();
        jackyunRequest.setMethod(JackyunConstant.METHOD_GET_INTERNET_SHOP_ORDER);
        jackyunRequest.setBizcontent(JsonProcessUtil.beanToJson(request));
        return invokeJkyOpenApiService(jackyunRequest);
    }

    public static void main(String[] args) {

        log.info("test begin");

        Map<String, Object> params = new HashMap<>();
        params.put("hasTotal", "1");
        params.put("pageSize", 50);
        params.put("pageIndex", 0);
        params.put("startAuditTime", "2023-05-11 00:00:00");
        params.put("endAuditTime", "2023-05-16 23:59:59");
        params.put("fields", "tradeNo,checkTotal,otherFee,chargeCurrency,accountName,payType,payNo,sellerMemo,buyerMemo,appendMemo,tradeFrom,register,seller,auditor,reviewer,estimateWeight,packageWeight,tradeCount,goodsTypeCount,freezeReason,abnormalDescription,onlineTradeNo,gmtCreate,gmtModified,stockoutNo,confirmTime,departName,lastShipTime,payStatus,chargeCurrencyCode,chargeExchangeRate,tradeStatus,grossProfit,estimateVolume,customerTypeName,customerGradeName,customerTags,customerCode,customerDiscount,specialReminding,blackList,tradeTime,country,state,city,district,town,zip,payTime,countryCode,cityCode,invoiceType,payerName,payerRegno,payerBankAccount,payerPhone,auditTime,payerAddress,invoiceNo,invoiceCode,invoiceStatus,payerBankName,preTypedetail,firstPayment,finalPayment,firstPaytime,finalPaytime,reviewTime,activationTime,customerTotalFee,customerDiscountFee,notifyPickTime,consignTime,orderNo,customerPostFee,shopId,customerPayment,companyName,isBillCheck,warehouseCode,warehouseName,logisticName,logisticType,mainPostid,tradeType,totalFee,taxFee,receivedPostFee,discountFee,payment,couponFee,receivedTotal,postFee,isTableSwitch,completeTime,shopCode,signingTime,settleAuditTime,localPayment,localExchangeRate,customerAccount,localCurrencyCode,platCompleteTime,flagIds,flagNames,sysFlagIds,sourceAfterNo,chargeAccount,chargeType,goodsSerial.skuId,goodsSerial.subTradeId,goodsSerial.serialNo,expense.expenseFee,expense.expenseItemName,goodsDetail.goodsNo,goodsDetail.goodsName,goodsDetail.specName,goodsDetail.barcode,goodsDetail.sellCount,goodsDetail.unit,goodsDetail.sellPrice,goodsDetail.cost,goodsDetail.discountTotal,goodsDetail.discountPoint,goodsDetail.shareFavourableFee,goodsDetail.estimateWeight,goodsDetail.goodsMemo,goodsDetail.cateName,goodsDetail.brandName,goodsDetail.goodsTags,goodsDetail.isGift,goodsDetail.discountFee,goodsDetail.taxRate,goodsDetail.estimateGoodsVolume,goodsDetail.isPresell,goodsDetail.customerPrice,goodsDetail.customerTotal,goodsDetail.tradeGoodsNo,goodsDetail.tradeGoodsName,goodsDetail.tradeGoodsSpec,goodsDetail.tradeGoodsUnit,goodsDetail.sourceSubtradeNo,goodsDetail.platCode,goodsDetail.platGoodsId,goodsDetail.subTradeId,goodsDetail.platAuthorId,goodsDetail.platAuthorName,goodsDetail.isPlatGift,goodsDetail.shareFavourableAfterFee,goodsDetail.divideSellTotal,goodsDetail.shareOrderDiscountFee,goodsDetail.sourceTradeNo,actualSendCount,goodsDetail.specId,goodsDelivery.batchNo");

        JackyunRequest request = new JackyunRequest();
        request.setMethod("oms.trade.fullinfoget");
        request.setBizcontent(JsonProcessUtil.beanToJson(params));

        String url = "http://apim-sit.pttl.com/gateway/jackyun/openapi";

        JackyunResponse response = new RestTemplate().postForObject(url, request, JackyunResponse.class);
        log.info("response: {}", JsonProcessUtil.beanToJson(response));

    }

}
