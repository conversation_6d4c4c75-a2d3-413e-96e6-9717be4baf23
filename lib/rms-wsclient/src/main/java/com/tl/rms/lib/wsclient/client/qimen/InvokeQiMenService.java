package com.tl.rms.lib.wsclient.client.qimen;

import com.qimen.api.DefaultQimenClient;
import com.qimen.api.QimenClient;
import com.qimen.api.QimenRequest;
import com.qimen.api.QimenResponse;
import com.qimen.api.request.DeliveryorderConfirmRequest;
import com.qimen.api.request.ReturnorderConfirmRequest;
import com.qimen.api.request.StockchangeReportRequest;
import com.qimen.api.response.DeliveryorderConfirmResponse;
import com.qimen.api.response.ReturnorderConfirmResponse;
import com.qimen.api.response.StockchangeReportResponse;
import com.tl.rms.common.exception.CommonException;
import com.tl.rms.lib.wsclient.config.QiMenConfig;
import com.tl.rms.lib.wsclient.config.WebServiceClientConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 调用TW系统的接口
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InvokeQiMenService {

    private final WebServiceClientConfig wsClientConfig;
    private final QiMenConfig qiMenConfig;

    private static final String SERVICE_NAME = "RMS_QIMEN";
    private static final String SERVICE_PATH = "/router/qimen/service";

    /**
     * 发货单确认接口
     */
    public DeliveryorderConfirmResponse confirmDeliveryOrder(DeliveryorderConfirmRequest request) {
        return executeQiMenRequest("confirmDeliveryOrder", request);
    }

    /**
     * 退货入库单确认接口
     */
    public ReturnorderConfirmResponse confirmReturnOrder(ReturnorderConfirmRequest request) {
        return executeQiMenRequest("confirmReturnOrder", request);
    }

    /**
     * 库存异动通知接口
     */
    public StockchangeReportResponse reportStockChange(StockchangeReportRequest request) {
        return executeQiMenRequest("reportStockChange", request);
    }

    /**
     * 通用执行方法 - 处理所有奇门接口调用
     */
    private <T extends QimenRequest<R>, R extends QimenResponse> R executeQiMenRequest(
            String methodName, T request) {
        long startTime = System.currentTimeMillis();
        String traceId = String.valueOf(System.currentTimeMillis());

        log.info("[{}][{}] traceId={} start", SERVICE_NAME, methodName, traceId);

        request.setCustomerId(qiMenConfig.getCustomerId());
        QimenClient client = new DefaultQimenClient(
                wsClientConfig.getEsbWsAddress() + SERVICE_PATH,
                qiMenConfig.getAppKey(), qiMenConfig.getAppSecret());

        try {
            R response = client.execute(request);
            long costTime = System.currentTimeMillis() - startTime;

            if (response == null) {
                log.error("[{}][{}] traceId={} error, cost: {} ms, response is null",
                        SERVICE_NAME, methodName, traceId, costTime);
                throw new CommonException("调用奇门接口失败: 响应为空");
            }

            // 检查响应是否成功
            if (response.isSuccess()) {
                log.info("[{}][{}] traceId={} success, cost: {} ms, flag: {}, code: {}, message: {}",
                        SERVICE_NAME, methodName, traceId, costTime,
                        response.getFlag(), response.getCode(), response.getMessage());
            } else {
                log.warn("[{}][{}] traceId={} failure, cost: {} ms, flag: {}, code: {}, message: {}",
                        SERVICE_NAME, methodName, traceId, costTime,
                        response.getFlag(), response.getCode(), response.getMessage());
                throw new CommonException("调用奇门接口失败: " + response.getMessage());
            }
            return response;
        } catch (CommonException e) {
            throw e;
        } catch (Exception e) {
            long costTime = System.currentTimeMillis() - startTime;
            log.error("[{}][{}] traceId={} exception, cost: {} ms, error: {}",
                    SERVICE_NAME, methodName, traceId, costTime, e.getMessage(), e);
            throw new RuntimeException("调用奇门接口发生系统异常", e);
        }
    }
}