
package com.tl.rms.lib.wsclient.client.ebs.signsync;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.annotation.XmlElementDecl;
import jakarta.xml.bind.annotation.XmlRegistry;

import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.pttl.tlmall.lib.wsclient.client.ebs.signsync package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _ResultInfo_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "ResultInfo");
    private final static QName _Sign_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "Sign");
    private final static QName _SignInfo_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "SignInfo");
    private final static QName _SignResponse_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "SignResponse");
    private final static QName _SignInfoItem_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "SignInfoItem");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.pttl.tlmall.lib.wsclient.client.ebs.signsync
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link SignInfo }
     * 
     */
    public SignInfo createSignInfo() {
        return new SignInfo();
    }

    /**
     * Create an instance of {@link SignInfoItem }
     * 
     */
    public SignInfoItem createSignInfoItem() {
        return new SignInfoItem();
    }

    /**
     * Create an instance of {@link SignResponse }
     * 
     */
    public SignResponse createSignResponse() {
        return new SignResponse();
    }

    /**
     * Create an instance of {@link Sign }
     * 
     */
    public Sign createSign() {
        return new Sign();
    }

    /**
     * Create an instance of {@link ResultInfo }
     * 
     */
    public ResultInfo createResultInfo() {
        return new ResultInfo();
    }

    /**
     * Create an instance of {@link SignInfo.DetailItems }
     * 
     */
    public SignInfo.DetailItems createSignInfoDetailItems() {
        return new SignInfo.DetailItems();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ResultInfo }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.putiantaili.com/soa/esb/", name = "ResultInfo")
    public JAXBElement<ResultInfo> createResultInfo(ResultInfo value) {
        return new JAXBElement<ResultInfo>(_ResultInfo_QNAME, ResultInfo.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Sign }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.putiantaili.com/soa/esb/", name = "Sign")
    public JAXBElement<Sign> createSign(Sign value) {
        return new JAXBElement<Sign>(_Sign_QNAME, Sign.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SignInfo }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.putiantaili.com/soa/esb/", name = "SignInfo")
    public JAXBElement<SignInfo> createSignInfo(SignInfo value) {
        return new JAXBElement<SignInfo>(_SignInfo_QNAME, SignInfo.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SignResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.putiantaili.com/soa/esb/", name = "SignResponse")
    public JAXBElement<SignResponse> createSignResponse(SignResponse value) {
        return new JAXBElement<SignResponse>(_SignResponse_QNAME, SignResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SignInfoItem }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.putiantaili.com/soa/esb/", name = "SignInfoItem")
    public JAXBElement<SignInfoItem> createSignInfoItem(SignInfoItem value) {
        return new JAXBElement<SignInfoItem>(_SignInfoItem_QNAME, SignInfoItem.class, null, value);
    }

}
