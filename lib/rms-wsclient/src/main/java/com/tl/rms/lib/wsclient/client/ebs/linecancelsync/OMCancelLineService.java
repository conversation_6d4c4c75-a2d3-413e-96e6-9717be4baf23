
package com.tl.rms.lib.wsclient.client.ebs.linecancelsync;

import jakarta.xml.ws.*;

import javax.xml.namespace.QName;
import java.net.MalformedURLException;
import java.net.URL;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebServiceClient(name = "OMCancelLineService", targetNamespace = "http://www.putiantaili.com/soa/esb/", wsdlLocation = "http://192.168.220.90:7001/TLSI_0026/processes/OMCancelLineService?wsdl")
public class OMCancelLineService
    extends Service
{

    private final static URL OMCANCELLINESERVICE_WSDL_LOCATION;
    private final static WebServiceException OMCANCELLINESERVICE_EXCEPTION;
    private final static QName OMCANCELLINESERVICE_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "OMCancelLineService");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://192.168.220.90:7001/TLSI_0026/processes/OMCancelLineService?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        OMCANCELLINESERVICE_WSDL_LOCATION = url;
        OMCANCELLINESERVICE_EXCEPTION = e;
    }

    public OMCancelLineService() {
        super(__getWsdlLocation(), OMCANCELLINESERVICE_QNAME);
    }

    public OMCancelLineService(WebServiceFeature... features) {
        super(__getWsdlLocation(), OMCANCELLINESERVICE_QNAME, features);
    }

    public OMCancelLineService(URL wsdlLocation) {
        super(wsdlLocation, OMCANCELLINESERVICE_QNAME);
    }

    public OMCancelLineService(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, OMCANCELLINESERVICE_QNAME, features);
    }

    public OMCancelLineService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public OMCancelLineService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return
     *     returns OMCancelLineServicePortType
     */
    @WebEndpoint(name = "OMCancelLineServiceSOAP11port_http")
    public OMCancelLineServicePortType getOMCancelLineServiceSOAP11PortHttp() {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "OMCancelLineServiceSOAP11port_http"), OMCancelLineServicePortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns OMCancelLineServicePortType
     */
    @WebEndpoint(name = "OMCancelLineServiceSOAP11port_http")
    public OMCancelLineServicePortType getOMCancelLineServiceSOAP11PortHttp(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "OMCancelLineServiceSOAP11port_http"), OMCancelLineServicePortType.class, features);
    }

    /**
     *
     * @return
     *     returns OMCancelLineServicePortType
     */
    @WebEndpoint(name = "OMCancelLineServiceSOAP12port_http")
    public OMCancelLineServicePortType getOMCancelLineServiceSOAP12PortHttp() {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "OMCancelLineServiceSOAP12port_http"), OMCancelLineServicePortType.class);
    }

    /**
     *
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns OMCancelLineServicePortType
     */
    @WebEndpoint(name = "OMCancelLineServiceSOAP12port_http")
    public OMCancelLineServicePortType getOMCancelLineServiceSOAP12PortHttp(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "OMCancelLineServiceSOAP12port_http"), OMCancelLineServicePortType.class, features);
    }

    /**
     *
     * @return
     *     returns OMCancelLineServicePortType
     */
    @WebEndpoint(name = "OMCancelLineServiceHttpport")
    public OMCancelLineServicePortType getOMCancelLineServiceHttpport() {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "OMCancelLineServiceHttpport"), OMCancelLineServicePortType.class);
    }

    /**
     *
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns OMCancelLineServicePortType
     */
    @WebEndpoint(name = "OMCancelLineServiceHttpport")
    public OMCancelLineServicePortType getOMCancelLineServiceHttpport(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "OMCancelLineServiceHttpport"), OMCancelLineServicePortType.class, features);
    }

    private static URL __getWsdlLocation() {
        if (OMCANCELLINESERVICE_EXCEPTION!= null) {
            throw OMCANCELLINESERVICE_EXCEPTION;
        }
        return OMCANCELLINESERVICE_WSDL_LOCATION;
    }

}
