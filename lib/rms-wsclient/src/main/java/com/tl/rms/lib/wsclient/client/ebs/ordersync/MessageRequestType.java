
package com.tl.rms.lib.wsclient.client.ebs.ordersync;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>MessageRequestType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="MessageRequestType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="SourceHeaderId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SourceCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OrgName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CustomerNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CustPoNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="InvoiceToAddress" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OrderType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OrderedDate" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="TermName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ShipFrom" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ShippingMethod" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CashAmount" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CreditType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CreditAmount" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PaymentDays" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SalesContract" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="DeliveryNum" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ReturnNum" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ProjectCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SamsungLevel" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SamsungPoNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="GomeBrand" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SuningCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="WeekBatch" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="BorrowNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Comments" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ReturnOrderNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Attribute1" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Attribute2" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OrderLines" type="{http://www.putiantaili.com/soa/esb/}OrderLinesType"/>
 *         &lt;element name="DiscountLines" type="{http://www.putiantaili.com/soa/esb/}DiscountLinesType"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MessageRequestType", propOrder = {
    "sourceHeaderId",
    "sourceCode",
    "orgName",
    "customerNumber",
    "custPoNumber",
    "invoiceToAddress",
    "orderType",
    "orderedDate",
    "termName",
    "shipFrom",
    "shippingMethod",
    "cashAmount",
    "creditType",
    "creditAmount",
    "paymentDays",
    "salesContract",
    "deliveryNum",
    "returnNum",
    "projectCode",
    "samsungLevel",
    "samsungPoNumber",
    "gomeBrand",
    "suningCode",
    "weekBatch",
    "borrowNumber",
    "comments",
    "returnOrderNumber",
    "attribute1",
    "attribute2",
    "creditExpense",
    "isFreeExpense",
    "gsAmount",
    "gsType",
    "orderLines",
    "discountLines"
})
public class MessageRequestType {

    @XmlElement(name = "SourceHeaderId")
    protected String sourceHeaderId;
    @XmlElement(name = "SourceCode")
    protected String sourceCode;
    @XmlElement(name = "OrgName")
    protected String orgName;
    @XmlElement(name = "CustomerNumber")
    protected String customerNumber;
    @XmlElement(name = "CustPoNumber")
    protected String custPoNumber;
    @XmlElement(name = "InvoiceToAddress")
    protected String invoiceToAddress;
    @XmlElement(name = "OrderType")
    protected String orderType;
    @XmlElement(name = "OrderedDate")
    protected String orderedDate;
    @XmlElement(name = "TermName")
    protected String termName;
    @XmlElement(name = "ShipFrom")
    protected String shipFrom;
    @XmlElement(name = "ShippingMethod")
    protected String shippingMethod;
    @XmlElement(name = "CashAmount")
    protected String cashAmount;
    @XmlElement(name = "CreditType")
    protected String creditType;
    @XmlElement(name = "CreditAmount")
    protected String creditAmount;
    @XmlElement(name = "PaymentDays")
    protected String paymentDays;
    @XmlElement(name = "SalesContract")
    protected String salesContract;
    @XmlElement(name = "DeliveryNum")
    protected String deliveryNum;
    @XmlElement(name = "ReturnNum")
    protected String returnNum;
    @XmlElement(name = "ProjectCode")
    protected String projectCode;
    @XmlElement(name = "SamsungLevel")
    protected String samsungLevel;
    @XmlElement(name = "SamsungPoNumber")
    protected String samsungPoNumber;
    @XmlElement(name = "GomeBrand")
    protected String gomeBrand;
    @XmlElement(name = "SuningCode")
    protected String suningCode;
    @XmlElement(name = "WeekBatch")
    protected String weekBatch;
    @XmlElement(name = "BorrowNumber")
    protected String borrowNumber;
    @XmlElement(name = "Comments")
    protected String comments;
    @XmlElement(name = "ReturnOrderNumber")
    protected String returnOrderNumber;
    @XmlElement(name = "Attribute1")
    protected String attribute1;
    @XmlElement(name = "Attribute2")
    protected String attribute2;
    @XmlElement(name = "CreditExpense")
    protected String creditExpense;
    @XmlElement(name = "IsFreeExpense")
    protected String isFreeExpense;
    @XmlElement(name = "GsAmount")
    protected String gsAmount;
    @XmlElement(name = "GsType")
    protected String gsType;
    @XmlElement(name = "OrderLines", required = true)
    protected OrderLinesType orderLines;
    @XmlElement(name = "DiscountLines", required = true)
    protected DiscountLinesType discountLines;

    /**
     * 获取sourceHeaderId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSourceHeaderId() {
        return sourceHeaderId;
    }

    /**
     * 设置sourceHeaderId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSourceHeaderId(String value) {
        this.sourceHeaderId = value;
    }

    /**
     * 获取sourceCode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSourceCode() {
        return sourceCode;
    }

    /**
     * 设置sourceCode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSourceCode(String value) {
        this.sourceCode = value;
    }

    /**
     * 获取orgName属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     * 设置orgName属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrgName(String value) {
        this.orgName = value;
    }

    /**
     * 获取customerNumber属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustomerNumber() {
        return customerNumber;
    }

    /**
     * 设置customerNumber属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustomerNumber(String value) {
        this.customerNumber = value;
    }

    /**
     * 获取custPoNumber属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustPoNumber() {
        return custPoNumber;
    }

    /**
     * 设置custPoNumber属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustPoNumber(String value) {
        this.custPoNumber = value;
    }

    /**
     * 获取invoiceToAddress属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInvoiceToAddress() {
        return invoiceToAddress;
    }

    /**
     * 设置invoiceToAddress属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInvoiceToAddress(String value) {
        this.invoiceToAddress = value;
    }

    /**
     * 获取orderType属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrderType() {
        return orderType;
    }

    /**
     * 设置orderType属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrderType(String value) {
        this.orderType = value;
    }

    /**
     * 获取orderedDate属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrderedDate() {
        return orderedDate;
    }

    /**
     * 设置orderedDate属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrderedDate(String value) {
        this.orderedDate = value;
    }

    /**
     * 获取termName属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTermName() {
        return termName;
    }

    /**
     * 设置termName属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTermName(String value) {
        this.termName = value;
    }

    /**
     * 获取shipFrom属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShipFrom() {
        return shipFrom;
    }

    /**
     * 设置shipFrom属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShipFrom(String value) {
        this.shipFrom = value;
    }

    /**
     * 获取shippingMethod属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShippingMethod() {
        return shippingMethod;
    }

    /**
     * 设置shippingMethod属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShippingMethod(String value) {
        this.shippingMethod = value;
    }

    /**
     * 获取cashAmount属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCashAmount() {
        return cashAmount;
    }

    /**
     * 设置cashAmount属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCashAmount(String value) {
        this.cashAmount = value;
    }

    /**
     * 获取creditType属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCreditType() {
        return creditType;
    }

    /**
     * 设置creditType属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCreditType(String value) {
        this.creditType = value;
    }

    /**
     * 获取creditAmount属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCreditAmount() {
        return creditAmount;
    }

    /**
     * 设置creditAmount属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCreditAmount(String value) {
        this.creditAmount = value;
    }

    /**
     * 获取paymentDays属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPaymentDays() {
        return paymentDays;
    }

    /**
     * 设置paymentDays属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPaymentDays(String value) {
        this.paymentDays = value;
    }

    /**
     * 获取salesContract属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSalesContract() {
        return salesContract;
    }

    /**
     * 设置salesContract属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSalesContract(String value) {
        this.salesContract = value;
    }

    /**
     * 获取deliveryNum属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDeliveryNum() {
        return deliveryNum;
    }

    /**
     * 设置deliveryNum属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeliveryNum(String value) {
        this.deliveryNum = value;
    }

    /**
     * 获取returnNum属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReturnNum() {
        return returnNum;
    }

    /**
     * 设置returnNum属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReturnNum(String value) {
        this.returnNum = value;
    }

    /**
     * 获取projectCode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProjectCode() {
        return projectCode;
    }

    /**
     * 设置projectCode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProjectCode(String value) {
        this.projectCode = value;
    }

    /**
     * 获取samsungLevel属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSamsungLevel() {
        return samsungLevel;
    }

    /**
     * 设置samsungLevel属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSamsungLevel(String value) {
        this.samsungLevel = value;
    }

    /**
     * 获取samsungPoNumber属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSamsungPoNumber() {
        return samsungPoNumber;
    }

    /**
     * 设置samsungPoNumber属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSamsungPoNumber(String value) {
        this.samsungPoNumber = value;
    }

    /**
     * 获取gomeBrand属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGomeBrand() {
        return gomeBrand;
    }

    /**
     * 设置gomeBrand属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGomeBrand(String value) {
        this.gomeBrand = value;
    }

    /**
     * 获取suningCode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSuningCode() {
        return suningCode;
    }

    /**
     * 设置suningCode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSuningCode(String value) {
        this.suningCode = value;
    }

    /**
     * 获取weekBatch属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWeekBatch() {
        return weekBatch;
    }

    /**
     * 设置weekBatch属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWeekBatch(String value) {
        this.weekBatch = value;
    }

    /**
     * 获取borrowNumber属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBorrowNumber() {
        return borrowNumber;
    }

    /**
     * 设置borrowNumber属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBorrowNumber(String value) {
        this.borrowNumber = value;
    }

    /**
     * 获取comments属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getComments() {
        return comments;
    }

    /**
     * 设置comments属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setComments(String value) {
        this.comments = value;
    }

    /**
     * 获取returnOrderNumber属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReturnOrderNumber() {
        return returnOrderNumber;
    }

    /**
     * 设置returnOrderNumber属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReturnOrderNumber(String value) {
        this.returnOrderNumber = value;
    }

    /**
     * 获取attribute1属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttribute1() {
        return attribute1;
    }

    /**
     * 设置attribute1属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttribute1(String value) {
        this.attribute1 = value;
    }

    /**
     * 获取attribute2属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttribute2() {
        return attribute2;
    }

    /**
     * 设置attribute2属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttribute2(String value) {
        this.attribute2 = value;
    }

    /**
     * 获取orderLines属性的值。
     * 
     * @return
     *     possible object is
     *     {@link OrderLinesType }
     *     
     */
    public OrderLinesType getOrderLines() {
        return orderLines;
    }

    /**
     * 设置orderLines属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link OrderLinesType }
     *     
     */
    public void setOrderLines(OrderLinesType value) {
        this.orderLines = value;
    }

    /**
     * 获取discountLines属性的值。
     * 
     * @return
     *     possible object is
     *     {@link DiscountLinesType }
     *     
     */
    public DiscountLinesType getDiscountLines() {
        return discountLines;
    }

    /**
     * 设置discountLines属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link DiscountLinesType }
     *     
     */
    public void setDiscountLines(DiscountLinesType value) {
        this.discountLines = value;
    }

    public String getCreditExpense() {
        return creditExpense;
    }

    public void setCreditExpense(String creditExpense) {
        this.creditExpense = creditExpense;
    }

    public String getIsFreeExpense() {
        return isFreeExpense;
    }

    public void setIsFreeExpense(String isFreeExpense) {
        this.isFreeExpense = isFreeExpense;
    }

    public String getGsAmount() {
        return gsAmount;
    }

    public void setGsAmount(String gsAmount) {
        this.gsAmount = gsAmount;
    }

    public String getGsType() {
        return gsType;
    }

    public void setGsType(String gsType) {
        this.gsType = gsType;
    }

}
