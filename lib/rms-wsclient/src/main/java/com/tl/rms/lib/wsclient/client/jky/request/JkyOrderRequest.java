package com.tl.rms.lib.wsclient.client.jky.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 吉客云 销售单接口 业务请求参数
 *
 * <AUTHOR>
 * @date 2023/5/22
 */
@Data
public class JkyOrderRequest implements Serializable {

    /**
     * 每页记录数，默认50，最大200
     */
    private Integer pageSize;

    /**
     * 页码，0为第1页
     */
    private Integer pageIndex;

    /**
     * 默认返回，首次调用时可以传1获取总记录数
     */
    private String hasTotal;

    /**
     * 最后修改时间（起始）
     */
    private String startModified;

    /**
     * 最后修改时间（截止）
     */
    private String endModified;

    /**
     * 审核时间（起始）
     */
    private String startAuditTime;

    /**
     * 审核时间（截止）
     */
    private String endAuditTime;

    /**
     * 销售单号，多个用半角逗号分隔
     */
    private String tradeNo;

    /**
     * 审核时间（起始）退换补货单
     */
    private String startAuditDate;

    /**
     * 审核时间（截止）退换补货单
     */
    private String endAuditDate;

    /**
     * 订单状态
     */
    private Integer tradeStatus;

    /**
     * 订单类型
     */
    private Integer tradeType;

    /**
     * 网店订单号
     */
    private String sourceTradeNos;

    /**
     * 需要返回的字段列表，多个字段用半角逗号分隔，可选值为返回示例中能看到的所有字段。tradeNo:订单编号,goodsDetail.outerId:商品编码,pickUpCode:取货码,expense.expenseFee打包费,expense.expenseItemName费用名称,billDate:对账时间（得传对账时间起止),goodsPlatDiscountFee:货品平台优惠
     */
    private String fields;

}