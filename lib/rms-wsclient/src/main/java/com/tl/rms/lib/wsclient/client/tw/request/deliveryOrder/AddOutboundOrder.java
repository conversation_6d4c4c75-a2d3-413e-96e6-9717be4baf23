
package com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ptOutboundOrderInfo" type="{OutboundService}PtOutboundOrderInfo" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "ptOutboundOrderInfo"
})
@XmlRootElement(name = "AddOutboundOrder")
public class AddOutboundOrder {

    protected PtOutboundOrderInfo ptOutboundOrderInfo;

    /**
     * 获取ptOutboundOrderInfo属性的值。
     * 
     * @return
     *     possible object is
     *     {@link PtOutboundOrderInfo }
     *     
     */
    public PtOutboundOrderInfo getPtOutboundOrderInfo() {
        return ptOutboundOrderInfo;
    }

    /**
     * 设置ptOutboundOrderInfo属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link PtOutboundOrderInfo }
     *     
     */
    public void setPtOutboundOrderInfo(PtOutboundOrderInfo value) {
        this.ptOutboundOrderInfo = value;
    }

}
