
package com.tl.rms.lib.wsclient.client.ebs.inboundsync;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>MessageRequestType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="MessageRequestType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="InboundResponseInfo" type="{http://www.putiantaili.com/soa/esb/}InboundResponseInfoType" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MessageRequestType", propOrder = {
    "inboundResponseInfo"
})
public class MessageRequestType {

    @XmlElement(name = "InboundResponseInfo")
    protected InboundResponseInfoType inboundResponseInfo;

    /**
     * 获取inboundResponseInfo属性的值。
     * 
     * @return
     *     possible object is
     *     {@link InboundResponseInfoType }
     *     
     */
    public InboundResponseInfoType getInboundResponseInfo() {
        return inboundResponseInfo;
    }

    /**
     * 设置inboundResponseInfo属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link InboundResponseInfoType }
     *     
     */
    public void setInboundResponseInfo(InboundResponseInfoType value) {
        this.inboundResponseInfo = value;
    }

}
