
package com.tl.rms.lib.wsclient.client.ebs.outboundsync;

import jakarta.xml.ws.*;

import javax.xml.namespace.QName;
import java.net.MalformedURLException;
import java.net.URL;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebServiceClient(name = "OutboundResponseService", targetNamespace = "http://www.putiantaili.com/soa/esb/", wsdlLocation = "http://192.168.220.90:7001/TLSI_0004/processes/OutboundResponseService?wsdl")
public class OutboundResponseService
    extends Service
{

    private final static URL OUTBOUNDRESPONSESERVICE_WSDL_LOCATION;
    private final static WebServiceException OUTBOUNDRESPONSESERVICE_EXCEPTION;
    private final static QName OUTBOUNDRESPONSESERVICE_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "OutboundResponseService");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://192.168.220.90:7001/TLSI_0004/processes/OutboundResponseService?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        OUTBOUNDRESPONSESERVICE_WSDL_LOCATION = url;
        OUTBOUNDRESPONSESERVICE_EXCEPTION = e;
    }

    public OutboundResponseService() {
        super(__getWsdlLocation(), OUTBOUNDRESPONSESERVICE_QNAME);
    }

    public OutboundResponseService(WebServiceFeature... features) {
        super(__getWsdlLocation(), OUTBOUNDRESPONSESERVICE_QNAME, features);
    }

    public OutboundResponseService(URL wsdlLocation) {
        super(wsdlLocation, OUTBOUNDRESPONSESERVICE_QNAME);
    }

    public OutboundResponseService(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, OUTBOUNDRESPONSESERVICE_QNAME, features);
    }

    public OutboundResponseService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public OutboundResponseService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return
     *     returns OutboundResponseServicePortType
     */
    @WebEndpoint(name = "OutboundResponseServiceSOAP11port_http")
    public OutboundResponseServicePortType getOutboundResponseServiceSOAP11PortHttp() {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "OutboundResponseServiceSOAP11port_http"), OutboundResponseServicePortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns OutboundResponseServicePortType
     */
    @WebEndpoint(name = "OutboundResponseServiceSOAP11port_http")
    public OutboundResponseServicePortType getOutboundResponseServiceSOAP11PortHttp(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "OutboundResponseServiceSOAP11port_http"), OutboundResponseServicePortType.class, features);
    }

    /**
     *
     * @return
     *     returns OutboundResponseServicePortType
     */
    @WebEndpoint(name = "OutboundResponseServiceSOAP12port_http")
    public OutboundResponseServicePortType getOutboundResponseServiceSOAP12PortHttp() {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "OutboundResponseServiceSOAP12port_http"), OutboundResponseServicePortType.class);
    }

    /**
     *
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns OutboundResponseServicePortType
     */
    @WebEndpoint(name = "OutboundResponseServiceSOAP12port_http")
    public OutboundResponseServicePortType getOutboundResponseServiceSOAP12PortHttp(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "OutboundResponseServiceSOAP12port_http"), OutboundResponseServicePortType.class, features);
    }

    /**
     *
     * @return
     *     returns OutboundResponseServicePortType
     */
    @WebEndpoint(name = "OutboundResponseServiceHttpport")
    public OutboundResponseServicePortType getOutboundResponseServiceHttpport() {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "OutboundResponseServiceHttpport"), OutboundResponseServicePortType.class);
    }

    /**
     *
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns OutboundResponseServicePortType
     */
    @WebEndpoint(name = "OutboundResponseServiceHttpport")
    public OutboundResponseServicePortType getOutboundResponseServiceHttpport(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "OutboundResponseServiceHttpport"), OutboundResponseServicePortType.class, features);
    }

    private static URL __getWsdlLocation() {
        if (OUTBOUNDRESPONSESERVICE_EXCEPTION!= null) {
            throw OUTBOUNDRESPONSESERVICE_EXCEPTION;
        }
        return OUTBOUNDRESPONSESERVICE_WSDL_LOCATION;
    }

}
