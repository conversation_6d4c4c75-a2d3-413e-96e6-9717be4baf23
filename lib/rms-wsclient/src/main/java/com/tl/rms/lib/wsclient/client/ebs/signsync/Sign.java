
package com.tl.rms.lib.wsclient.client.ebs.signsync;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Sign complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="Sign">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="SignInfo" type="{http://www.putiantaili.com/soa/esb/}SignInfo" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Sign", propOrder = {
    "signInfo"
})
public class Sign {

    @XmlElement(name = "SignInfo")
    protected SignInfo signInfo;

    /**
     * 获取signInfo属性的值。
     * 
     * @return
     *     possible object is
     *     {@link SignInfo }
     *     
     */
    public SignInfo getSignInfo() {
        return signInfo;
    }

    /**
     * 设置signInfo属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link SignInfo }
     *     
     */
    public void setSignInfo(SignInfo value) {
        this.signInfo = value;
    }

}
