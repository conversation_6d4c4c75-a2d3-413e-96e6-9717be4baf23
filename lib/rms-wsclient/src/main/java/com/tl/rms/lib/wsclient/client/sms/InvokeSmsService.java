package com.tl.rms.lib.wsclient.client.sms;

import com.tl.rms.lib.wsclient.config.WebServiceClientConfig;
import com.tl.rms.lib.wsclient.client.tw.response.Response;
import com.tl.rms.util.json.JsonProcessUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * 调用短信接口
 *
 * <AUTHOR>
 * @date 2025/3/26
 */
@Slf4j
@Component
public class InvokeSmsService {

    /**
     * 发送短信URL
     */
    private static final String SMS_API = "/sms/send";

    @Autowired
    private WebServiceClientConfig wsClientConfig;


    /**
     * 发送短信
     *
     * <AUTHOR>
     * @date 2025/3/26
     */
    public Response sendSms(SmsSendRequest request) {

        log.info("invokeSendSmsService request: {}", JsonProcessUtil.beanToJson(request));
        if (request == null) {
            log.error("调用发送短信API，参数不可为空");
            return new Response(1, "参数为空");
        }

        // 调用第三方短信服务接口
        try {
            Map result = new RestTemplate().postForObject(wsClientConfig.getEsbWsAddress() + SMS_API, request, Map.class);

            if (result == null) {
                return new Response(0, "发送失败");
            }
            // 处理成功响应
            Integer code = (Integer) result.get("Code");
            if (code > 0) {
                return new Response(0, "发送成功");
            } else {
                return new Response(1, (String) result.get("Message"));
            }
        } catch (HttpServerErrorException.BadGateway e) {
            // 处理网关异常（502错误）
            log.warn("短信服务发生异常，发送失败！\n request: {}", JsonProcessUtil.beanToJson(request), e);
            return new Response(1, "短信服务发生异常，发送失败");
        } catch (Exception e) {
            log.error("短信服务发生异常，发送失败！\n request: {}", JsonProcessUtil.beanToJson(request), e);
            return new Response(1, "短信服务发生异常，发送失败");
        }

    }

}
