package com.tl.rms.lib.wsclient.client.tw.request;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class TwCommonCallbackRequest {

    public static final String SUCCESS_CODE="200";

    private String bizType;
    private String code;
    private String msg;
    private String msgId;
    private String orgKey;
    private String sources;



    public static enum BizType{
        WMS_DELIVERY_ORDER_CONFIRM_ASYNC
    }
}
