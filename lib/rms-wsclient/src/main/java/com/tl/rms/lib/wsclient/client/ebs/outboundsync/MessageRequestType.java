
package com.tl.rms.lib.wsclient.client.ebs.outboundsync;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>MessageRequestType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="MessageRequestType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="OutboundResponseInfo" type="{http://www.putiantaili.com/soa/esb/}OutboundResponseInfoType" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MessageRequestType", propOrder = {
    "outboundResponseInfo"
})
public class MessageRequestType {

    @XmlElement(name = "OutboundResponseInfo")
    protected OutboundResponseInfoType outboundResponseInfo;

    /**
     * 获取outboundResponseInfo属性的值。
     * 
     * @return
     *     possible object is
     *     {@link OutboundResponseInfoType }
     *     
     */
    public OutboundResponseInfoType getOutboundResponseInfo() {
        return outboundResponseInfo;
    }

    /**
     * 设置outboundResponseInfo属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link OutboundResponseInfoType }
     *     
     */
    public void setOutboundResponseInfo(OutboundResponseInfoType value) {
        this.outboundResponseInfo = value;
    }

}
