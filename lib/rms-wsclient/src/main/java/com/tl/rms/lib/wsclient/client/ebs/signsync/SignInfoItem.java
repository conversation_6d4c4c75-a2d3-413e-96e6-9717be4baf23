
package com.tl.rms.lib.wsclient.client.ebs.signsync;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>SignInfoItem complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="SignInfoItem">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ItemGid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="TransPackageCount" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="ErpDocLineId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="LotAttr01" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf7" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SignInfoItem", propOrder = {
    "itemGid",
    "transPackageCount",
    "erpDocLineId",
    "lotAttr01",
    "udf7"
})
public class SignInfoItem {

    @XmlElement(name = "ItemGid", required = true)
    protected String itemGid;
    @XmlElement(name = "TransPackageCount")
    protected long transPackageCount;
    @XmlElement(name = "ErpDocLineId", required = true)
    protected String erpDocLineId;
    @XmlElement(name = "LotAttr01", required = true)
    protected String lotAttr01;
    @XmlElement(name = "Udf7", required = true)
    protected String udf7;

    /**
     * 获取itemGid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getItemGid() {
        return itemGid;
    }

    /**
     * 设置itemGid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setItemGid(String value) {
        this.itemGid = value;
    }

    /**
     * 获取transPackageCount属性的值。
     * 
     */
    public long getTransPackageCount() {
        return transPackageCount;
    }

    /**
     * 设置transPackageCount属性的值。
     * 
     */
    public void setTransPackageCount(long value) {
        this.transPackageCount = value;
    }

    /**
     * 获取erpDocLineId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getErpDocLineId() {
        return erpDocLineId;
    }

    /**
     * 设置erpDocLineId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setErpDocLineId(String value) {
        this.erpDocLineId = value;
    }

    /**
     * 获取lotAttr01属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLotAttr01() {
        return lotAttr01;
    }

    /**
     * 设置lotAttr01属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLotAttr01(String value) {
        this.lotAttr01 = value;
    }

    /**
     * 获取udf7属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf7() {
        return udf7;
    }

    /**
     * 设置udf7属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf7(String value) {
        this.udf7 = value;
    }

}
