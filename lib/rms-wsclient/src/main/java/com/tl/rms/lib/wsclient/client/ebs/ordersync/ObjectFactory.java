
package com.tl.rms.lib.wsclient.client.ebs.ordersync;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.annotation.XmlElementDecl;
import jakarta.xml.bind.annotation.XmlRegistry;

import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.pttl.tlmall.lib.wsclient.client.ebs.ordersync package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _MessageResponse_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "MessageResponse");
    private final static QName _MessageRequest_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "MessageRequest");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.pttl.tlmall.lib.wsclient.client.ebs.ordersync
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link MessageRequestType }
     * 
     */
    public MessageRequestType createMessageRequestType() {
        return new MessageRequestType();
    }

    /**
     * Create an instance of {@link MessageResponseType }
     * 
     */
    public MessageResponseType createMessageResponseType() {
        return new MessageResponseType();
    }

    /**
     * Create an instance of {@link DiscountLineType }
     * 
     */
    public DiscountLineType createDiscountLineType() {
        return new DiscountLineType();
    }

    /**
     * Create an instance of {@link OrderLinesType }
     * 
     */
    public OrderLinesType createOrderLinesType() {
        return new OrderLinesType();
    }

    /**
     * Create an instance of {@link OrderLineType }
     * 
     */
    public OrderLineType createOrderLineType() {
        return new OrderLineType();
    }

    /**
     * Create an instance of {@link DiscountLinesType }
     * 
     */
    public DiscountLinesType createDiscountLinesType() {
        return new DiscountLinesType();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MessageResponseType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.putiantaili.com/soa/esb/", name = "MessageResponse")
    public JAXBElement<MessageResponseType> createMessageResponse(MessageResponseType value) {
        return new JAXBElement<MessageResponseType>(_MessageResponse_QNAME, MessageResponseType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MessageRequestType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.putiantaili.com/soa/esb/", name = "MessageRequest")
    public JAXBElement<MessageRequestType> createMessageRequest(MessageRequestType value) {
        return new JAXBElement<MessageRequestType>(_MessageRequest_QNAME, MessageRequestType.class, null, value);
    }

}
