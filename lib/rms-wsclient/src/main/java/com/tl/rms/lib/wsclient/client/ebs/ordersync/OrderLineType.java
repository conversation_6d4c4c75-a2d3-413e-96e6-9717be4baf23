
package com.tl.rms.lib.wsclient.client.ebs.ordersync;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>OrderLineType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="OrderLineType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="SourceHeaderId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SourceLineId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SourceCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OrderedItem" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OrderedQuantity" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OrderQuantityUom" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="UnitSellingPrice" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="TaxCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SpecialOfferNum" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="UnitListPrice" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="IsBuyout" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="IsDirectDelivery" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ShipFromSubinventory" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ShipFromLocator" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CustPoNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Remark" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ShipToAddress" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ShipToAddrDetail" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ContactPerson" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ContactPhone" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Province" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="City" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OrderLineType", propOrder = {
    "sourceHeaderId",
    "sourceLineId",
    "sourceCode",
    "orderedItem",
    "orderedQuantity",
    "orderQuantityUom",
    "unitSellingPrice",
    "taxCode",
    "specialOfferNum",
    "unitListPrice",
    "isBuyout",
    "isDirectDelivery",
    "shipFromSubinventory",
    "shipFromLocator",
    "custPoNumber",
    "remark",
    "shipToAddress",
    "shipToAddrDetail",
    "sourceOrderId",
    "contactPerson",
    "contactPhone",
    "province",
    "city"
})
public class OrderLineType {

    @XmlElement(name = "SourceHeaderId")
    protected String sourceHeaderId;
    @XmlElement(name = "SourceLineId")
    protected String sourceLineId;
    @XmlElement(name = "SourceCode")
    protected String sourceCode;
    @XmlElement(name = "OrderedItem")
    protected String orderedItem;
    @XmlElement(name = "OrderedQuantity")
    protected String orderedQuantity;
    @XmlElement(name = "OrderQuantityUom")
    protected String orderQuantityUom;
    @XmlElement(name = "UnitSellingPrice")
    protected String unitSellingPrice;
    @XmlElement(name = "TaxCode")
    protected String taxCode;
    @XmlElement(name = "SpecialOfferNum")
    protected String specialOfferNum;
    @XmlElement(name = "UnitListPrice")
    protected String unitListPrice;
    @XmlElement(name = "IsBuyout")
    protected String isBuyout;
    @XmlElement(name = "IsDirectDelivery")
    protected String isDirectDelivery;
    @XmlElement(name = "ShipFromSubinventory")
    protected String shipFromSubinventory;
    @XmlElement(name = "ShipFromLocator")
    protected String shipFromLocator;
    @XmlElement(name = "CustPoNumber")
    protected String custPoNumber;
    @XmlElement(name = "Remark")
    protected String remark;
    @XmlElement(name = "ShipToAddress")
    protected String shipToAddress;
    @XmlElement(name = "ShipToAddrDetail")
    protected String shipToAddrDetail;
    @XmlElement(name = "ContactPerson")
    protected String contactPerson;
    @XmlElement(name = "ContactPhone")
    protected String contactPhone;
    @XmlElement(name = "Province")
    protected String province;
    @XmlElement(name = "City")
    protected String city;

    /**
     * 关联原单单号
     */
    @XmlElement(name = "SourceOrderId")
    protected String sourceOrderId;

    /**
     * 获取sourceHeaderId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSourceHeaderId() {
        return sourceHeaderId;
    }

    /**
     * 设置sourceHeaderId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSourceHeaderId(String value) {
        this.sourceHeaderId = value;
    }

    /**
     * 获取sourceLineId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSourceLineId() {
        return sourceLineId;
    }

    /**
     * 设置sourceLineId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSourceLineId(String value) {
        this.sourceLineId = value;
    }

    /**
     * 获取sourceCode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSourceCode() {
        return sourceCode;
    }

    /**
     * 设置sourceCode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSourceCode(String value) {
        this.sourceCode = value;
    }

    /**
     * 获取orderedItem属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrderedItem() {
        return orderedItem;
    }

    /**
     * 设置orderedItem属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrderedItem(String value) {
        this.orderedItem = value;
    }

    /**
     * 获取orderedQuantity属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrderedQuantity() {
        return orderedQuantity;
    }

    /**
     * 设置orderedQuantity属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrderedQuantity(String value) {
        this.orderedQuantity = value;
    }

    /**
     * 获取orderQuantityUom属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrderQuantityUom() {
        return orderQuantityUom;
    }

    /**
     * 设置orderQuantityUom属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrderQuantityUom(String value) {
        this.orderQuantityUom = value;
    }

    /**
     * 获取unitSellingPrice属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUnitSellingPrice() {
        return unitSellingPrice;
    }

    /**
     * 设置unitSellingPrice属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUnitSellingPrice(String value) {
        this.unitSellingPrice = value;
    }

    /**
     * 获取taxCode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaxCode() {
        return taxCode;
    }

    /**
     * 设置taxCode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaxCode(String value) {
        this.taxCode = value;
    }

    /**
     * 获取specialOfferNum属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSpecialOfferNum() {
        return specialOfferNum;
    }

    /**
     * 设置specialOfferNum属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSpecialOfferNum(String value) {
        this.specialOfferNum = value;
    }

    /**
     * 获取unitListPrice属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUnitListPrice() {
        return unitListPrice;
    }

    /**
     * 设置unitListPrice属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUnitListPrice(String value) {
        this.unitListPrice = value;
    }

    /**
     * 获取isBuyout属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIsBuyout() {
        return isBuyout;
    }

    /**
     * 设置isBuyout属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIsBuyout(String value) {
        this.isBuyout = value;
    }

    /**
     * 获取isDirectDelivery属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIsDirectDelivery() {
        return isDirectDelivery;
    }

    /**
     * 设置isDirectDelivery属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIsDirectDelivery(String value) {
        this.isDirectDelivery = value;
    }

    /**
     * 获取shipFromSubinventory属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShipFromSubinventory() {
        return shipFromSubinventory;
    }

    /**
     * 设置shipFromSubinventory属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShipFromSubinventory(String value) {
        this.shipFromSubinventory = value;
    }

    /**
     * 获取shipFromLocator属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShipFromLocator() {
        return shipFromLocator;
    }

    /**
     * 设置shipFromLocator属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShipFromLocator(String value) {
        this.shipFromLocator = value;
    }

    /**
     * 获取custPoNumber属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustPoNumber() {
        return custPoNumber;
    }

    /**
     * 设置custPoNumber属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustPoNumber(String value) {
        this.custPoNumber = value;
    }

    /**
     * 获取remark属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置remark属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRemark(String value) {
        this.remark = value;
    }

    /**
     * 获取shipToAddress属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShipToAddress() {
        return shipToAddress;
    }

    /**
     * 设置shipToAddress属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShipToAddress(String value) {
        this.shipToAddress = value;
    }

    /**
     * 获取shipToAddrDetail属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShipToAddrDetail() {
        return shipToAddrDetail;
    }

    /**
     * 设置shipToAddrDetail属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShipToAddrDetail(String value) {
        this.shipToAddrDetail = value;
    }

    /**
     * 获取sourceOrderId属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getSourceOrderId() {
        return sourceOrderId;
    }

    /**
     * 设置sourceOrderId属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setSourceOrderId(String value) {
        this.sourceOrderId = value;
    }

    /**
     * 获取contactPerson属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContactPerson() {
        return contactPerson;
    }

    /**
     * 设置contactPerson属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContactPerson(String value) {
        this.contactPerson = value;
    }

    /**
     * 获取contactPhone属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getContactPhone() {
        return contactPhone;
    }

    /**
     * 设置contactPhone属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setContactPhone(String value) {
        this.contactPhone = value;
    }

    /**
     * 获取province属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProvince() {
        return province;
    }

    /**
     * 设置province属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProvince(String value) {
        this.province = value;
    }

    /**
     * 获取city属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCity() {
        return city;
    }

    /**
     * 设置city属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCity(String value) {
        this.city = value;
    }

}
