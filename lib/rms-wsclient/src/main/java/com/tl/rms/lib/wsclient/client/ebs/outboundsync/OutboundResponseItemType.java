
package com.tl.rms.lib.wsclient.client.ebs.outboundsync;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>OutboundResponseItemType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="OutboundResponseItemType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="SkuId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="OrigianlQuantity" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="ShippedQuantity" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="ExternalLineId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="LotAttr01" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="LotAttr09" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf01" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf02" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf03" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf04" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf05" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf06" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf07" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf08" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf09" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf10" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Attribute2" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Attribute3" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Attribute4" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Attribute5" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OutboundResponseItemType", propOrder = {
    "skuId",
    "origianlQuantity",
    "shippedQuantity",
    "externalLineId",
    "lotAttr01",
    "lotAttr09",
    "returnAmount",
    "udf01",
    "udf02",
    "udf03",
    "udf04",
    "udf05",
    "udf06",
    "udf07",
    "udf08",
    "udf09",
    "udf10",
    "attribute2",
    "attribute3",
    "attribute4",
    "attribute5"
})
public class OutboundResponseItemType {

    @XmlElement(name = "SkuId", required = true)
    protected String skuId;
    @XmlElement(name = "OrigianlQuantity")
    protected long origianlQuantity;
    @XmlElement(name = "ShippedQuantity")
    protected long shippedQuantity;
    @XmlElement(name = "ExternalLineId", required = true)
    protected String externalLineId;
    @XmlElement(name = "LotAttr01", required = true)
    protected String lotAttr01;
    @XmlElement(name = "LotAttr09", required = true)
    protected String lotAttr09;
    @XmlElement(name = "ReturnAmount")
    protected String returnAmount;
    @XmlElement(name = "Udf01", required = true)
    protected String udf01;
    @XmlElement(name = "Udf02", required = true)
    protected String udf02;
    @XmlElement(name = "Udf03", required = true)
    protected String udf03;
    @XmlElement(name = "Udf04", required = true)
    protected String udf04;
    @XmlElement(name = "Udf05", required = true)
    protected String udf05;
    @XmlElement(name = "Udf06", required = true)
    protected String udf06;
    @XmlElement(name = "Udf07", required = true)
    protected String udf07;
    @XmlElement(name = "Udf08", required = true)
    protected String udf08;
    @XmlElement(name = "Udf09", required = true)
    protected String udf09;
    @XmlElement(name = "Udf10", required = true)
    protected String udf10;
    @XmlElement(name = "Attribute2", required = true)
    protected String attribute2;
    @XmlElement(name = "Attribute3", required = true)
    protected String attribute3;
    @XmlElement(name = "Attribute4", required = true)
    protected String attribute4;
    @XmlElement(name = "Attribute5", required = true)
    protected String attribute5;

    /**
     * 获取skuId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkuId() {
        return skuId;
    }

    /**
     * 设置skuId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkuId(String value) {
        this.skuId = value;
    }

    /**
     * 获取origianlQuantity属性的值。
     * 
     */
    public long getOrigianlQuantity() {
        return origianlQuantity;
    }

    /**
     * 设置origianlQuantity属性的值。
     * 
     */
    public void setOrigianlQuantity(long value) {
        this.origianlQuantity = value;
    }

    /**
     * 获取shippedQuantity属性的值。
     * 
     */
    public long getShippedQuantity() {
        return shippedQuantity;
    }

    /**
     * 设置shippedQuantity属性的值。
     * 
     */
    public void setShippedQuantity(long value) {
        this.shippedQuantity = value;
    }

    /**
     * 获取externalLineId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getExternalLineId() {
        return externalLineId;
    }

    /**
     * 设置externalLineId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setExternalLineId(String value) {
        this.externalLineId = value;
    }

    /**
     * 获取lotAttr01属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLotAttr01() {
        return lotAttr01;
    }

    /**
     * 设置lotAttr01属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLotAttr01(String value) {
        this.lotAttr01 = value;
    }

    /**
     * 获取lotAttr09属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLotAttr09() {
        return lotAttr09;
    }

    /**
     * 设置lotAttr09属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLotAttr09(String value) {
        this.lotAttr09 = value;
    }

    public String getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(String returnAmount) {
        this.returnAmount = returnAmount;
    }

    /**
     * 获取udf01属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf01() {
        return udf01;
    }

    /**
     * 设置udf01属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf01(String value) {
        this.udf01 = value;
    }

    /**
     * 获取udf02属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf02() {
        return udf02;
    }

    /**
     * 设置udf02属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf02(String value) {
        this.udf02 = value;
    }

    /**
     * 获取udf03属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf03() {
        return udf03;
    }

    /**
     * 设置udf03属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf03(String value) {
        this.udf03 = value;
    }

    /**
     * 获取udf04属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf04() {
        return udf04;
    }

    /**
     * 设置udf04属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf04(String value) {
        this.udf04 = value;
    }

    /**
     * 获取udf05属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf05() {
        return udf05;
    }

    /**
     * 设置udf05属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf05(String value) {
        this.udf05 = value;
    }

    /**
     * 获取udf06属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf06() {
        return udf06;
    }

    /**
     * 设置udf06属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf06(String value) {
        this.udf06 = value;
    }

    /**
     * 获取udf07属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf07() {
        return udf07;
    }

    /**
     * 设置udf07属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf07(String value) {
        this.udf07 = value;
    }

    /**
     * 获取udf08属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf08() {
        return udf08;
    }

    /**
     * 设置udf08属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf08(String value) {
        this.udf08 = value;
    }

    /**
     * 获取udf09属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf09() {
        return udf09;
    }

    /**
     * 设置udf09属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf09(String value) {
        this.udf09 = value;
    }

    /**
     * 获取udf10属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf10() {
        return udf10;
    }

    /**
     * 设置udf10属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf10(String value) {
        this.udf10 = value;
    }

    /**
     * 获取attribute2属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttribute2() {
        return attribute2;
    }

    /**
     * 设置attribute2属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttribute2(String value) {
        this.attribute2 = value;
    }

    /**
     * 获取attribute3属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttribute3() {
        return attribute3;
    }

    /**
     * 设置attribute3属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttribute3(String value) {
        this.attribute3 = value;
    }

    /**
     * 获取attribute4属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttribute4() {
        return attribute4;
    }

    /**
     * 设置attribute4属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttribute4(String value) {
        this.attribute4 = value;
    }

    /**
     * 获取attribute5属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttribute5() {
        return attribute5;
    }

    /**
     * 设置attribute5属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttribute5(String value) {
        this.attribute5 = value;
    }

}
