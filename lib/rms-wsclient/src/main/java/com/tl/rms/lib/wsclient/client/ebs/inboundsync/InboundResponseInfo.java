package com.tl.rms.lib.wsclient.client.ebs.inboundsync;

import lombok.Data;
import lombok.ToString;

/**
 * JKY退货入库请求ebs报文实体
 *
 * <AUTHOR>
 * @date 2023/10/12
 */
@Data
@ToString
public class InboundResponseInfo {

    /**
     * 分公司名称
     */
    private String branchName;

    /**
     * 库存组织名称
     */
    private String ownerId;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 销售订单行号 2
     */
    private Integer lineId;

    /**
     * 物料编码
     */
    private String matCode;

    /**
     * 退货数量
     */
    private Integer inQty;

    /**
     * 接收入库子库
     */
    private String warehouseId;

    /**
     * 目的字库货位
     */
    private String inLocator;

    /**
     * 入库时间(事物处理时间)
     */
    private String inTime;

    /**
     * TW退货入库编号
     */
    private String jkyToNumber;

}
