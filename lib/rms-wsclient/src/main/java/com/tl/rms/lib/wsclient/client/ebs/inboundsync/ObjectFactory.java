
package com.tl.rms.lib.wsclient.client.ebs.inboundsync;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.annotation.XmlElementDecl;
import jakarta.xml.bind.annotation.XmlRegistry;

import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.pttl.tlmall.lib.wsclient.client.ebs.inboundsync package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _MessageResponse_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "MessageResponse");
    private final static QName _InboundResponseInfo_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "InboundResponseInfo");
    private final static QName _MessageRequest_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "MessageRequest");
    private final static QName _InboundResponseItem_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "InboundResponseItem");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.pttl.tlmall.lib.wsclient.client.ebs.inboundsync
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link InboundResponseInfoType }
     * 
     */
    public InboundResponseInfoType createInboundResponseInfoType() {
        return new InboundResponseInfoType();
    }

    /**
     * Create an instance of {@link InboundResponseItemType }
     * 
     */
    public InboundResponseItemType createInboundResponseItemType() {
        return new InboundResponseItemType();
    }

    /**
     * Create an instance of {@link MessageRequestType }
     * 
     */
    public MessageRequestType createMessageRequestType() {
        return new MessageRequestType();
    }

    /**
     * Create an instance of {@link MessageResponseType }
     * 
     */
    public MessageResponseType createMessageResponseType() {
        return new MessageResponseType();
    }

    /**
     * Create an instance of {@link InboundResponseInfoType.DetailItems }
     * 
     */
    public InboundResponseInfoType.DetailItems createInboundResponseInfoTypeDetailItems() {
        return new InboundResponseInfoType.DetailItems();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MessageResponseType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.putiantaili.com/soa/esb/", name = "MessageResponse")
    public JAXBElement<MessageResponseType> createMessageResponse(MessageResponseType value) {
        return new JAXBElement<MessageResponseType>(_MessageResponse_QNAME, MessageResponseType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link InboundResponseInfoType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.putiantaili.com/soa/esb/", name = "InboundResponseInfo")
    public JAXBElement<InboundResponseInfoType> createInboundResponseInfo(InboundResponseInfoType value) {
        return new JAXBElement<InboundResponseInfoType>(_InboundResponseInfo_QNAME, InboundResponseInfoType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MessageRequestType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.putiantaili.com/soa/esb/", name = "MessageRequest")
    public JAXBElement<MessageRequestType> createMessageRequest(MessageRequestType value) {
        return new JAXBElement<MessageRequestType>(_MessageRequest_QNAME, MessageRequestType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link InboundResponseItemType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.putiantaili.com/soa/esb/", name = "InboundResponseItem")
    public JAXBElement<InboundResponseItemType> createInboundResponseItem(InboundResponseItemType value) {
        return new JAXBElement<InboundResponseItemType>(_InboundResponseItem_QNAME, InboundResponseItemType.class, null, value);
    }

}
