
package com.tl.rms.lib.wsclient.client.ebs.outboundsync;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.annotation.XmlElementDecl;
import jakarta.xml.bind.annotation.XmlRegistry;

import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.pttl.tlmall.lib.wsclient.client.ebs.outboundsync package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _OutboundResponseInfo_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "OutboundResponseInfo");
    private final static QName _OutboundResponseItem_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "OutboundResponseItem");
    private final static QName _MessageResponse_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "MessageResponse");
    private final static QName _MessageRequest_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "MessageRequest");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.pttl.tlmall.lib.wsclient.client.ebs.outboundsync
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link OutboundResponseInfoType }
     * 
     */
    public OutboundResponseInfoType createOutboundResponseInfoType() {
        return new OutboundResponseInfoType();
    }

    /**
     * Create an instance of {@link MessageRequestType }
     * 
     */
    public MessageRequestType createMessageRequestType() {
        return new MessageRequestType();
    }

    /**
     * Create an instance of {@link OutboundResponseItemType }
     * 
     */
    public OutboundResponseItemType createOutboundResponseItemType() {
        return new OutboundResponseItemType();
    }

    /**
     * Create an instance of {@link MessageResponseType }
     * 
     */
    public MessageResponseType createMessageResponseType() {
        return new MessageResponseType();
    }

    /**
     * Create an instance of {@link OutboundResponseInfoType.DetailItems }
     * 
     */
    public OutboundResponseInfoType.DetailItems createOutboundResponseInfoTypeDetailItems() {
        return new OutboundResponseInfoType.DetailItems();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link OutboundResponseInfoType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.putiantaili.com/soa/esb/", name = "OutboundResponseInfo")
    public JAXBElement<OutboundResponseInfoType> createOutboundResponseInfo(OutboundResponseInfoType value) {
        return new JAXBElement<OutboundResponseInfoType>(_OutboundResponseInfo_QNAME, OutboundResponseInfoType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link OutboundResponseItemType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.putiantaili.com/soa/esb/", name = "OutboundResponseItem")
    public JAXBElement<OutboundResponseItemType> createOutboundResponseItem(OutboundResponseItemType value) {
        return new JAXBElement<OutboundResponseItemType>(_OutboundResponseItem_QNAME, OutboundResponseItemType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MessageResponseType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.putiantaili.com/soa/esb/", name = "MessageResponse")
    public JAXBElement<MessageResponseType> createMessageResponse(MessageResponseType value) {
        return new JAXBElement<MessageResponseType>(_MessageResponse_QNAME, MessageResponseType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link MessageRequestType }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://www.putiantaili.com/soa/esb/", name = "MessageRequest")
    public JAXBElement<MessageRequestType> createMessageRequest(MessageRequestType value) {
        return new JAXBElement<MessageRequestType>(_MessageRequest_QNAME, MessageRequestType.class, null, value);
    }

}
