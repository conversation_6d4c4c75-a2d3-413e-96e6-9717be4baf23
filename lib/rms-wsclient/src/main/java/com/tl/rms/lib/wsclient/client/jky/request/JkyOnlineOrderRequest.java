package com.tl.rms.lib.wsclient.client.jky.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 吉客云 网店订单接口 业务请求参数
 *
 * <AUTHOR>
 * @date 2025/8/13
 */
@Data
public class JkyOnlineOrderRequest implements Serializable {

    /**
     * 必填，每页记录数，默认50，最大200
     */
    private Integer pageSize;

    /**
     * 必填，页码，0为第1页
     */
    private Integer pageIndex;

    /**
     * 最后修改时间（起始）
     */
    private String startModified;

    /**
     * 最后修改时间（截止）
     */
    private String endModified;

    /**
     * 需要返回的字段列表，多个字段用半角逗号分隔，可选值为返回示例中能看到的所有字段。tradeNo:订单编号,goodsDetail.outerId:商品编码,pickUpCode:取货码,expense.expenseFee打包费,expense.expenseItemName费用名称,billDate:对账时间（得传对账时间起止),goodsPlatDiscountFee:货品平台优惠
     */
    private String fields;

    /**
     * 店铺列表:287782131244,12356578900
     */
    private List<String> shopIds;

    /**
     * 是否查询归档数据0: 否 1: 是
     */
    private Integer hasQueryHistory;

    /**
     * 订单创建时间（开始）2023-01-01
     */
    private String createTimeBegin;

    /**
     * 订单创建时间（截止）2023-02-01
     */
    private String createTimeEnd;

}