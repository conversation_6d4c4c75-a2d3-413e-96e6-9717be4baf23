
package com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder;

import jakarta.xml.bind.annotation.*;


/**
 * <p>PtOutboundOrderInfo complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="PtOutboundOrderInfo">
 *   &lt;complexContent>
 *     &lt;extension base="{OutboundService}PotevioObj">
 *       &lt;sequence>
 *         &lt;element name="WhID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CustomerOrderID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ExternalOrderID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OrderType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OrderDate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RequireDeliveryDate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RequirementsArrivalDate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="TmsTask" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OwnerID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CustomerID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CustomerName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ReceiptOfName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ReceiptOfContact" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ReceiptOfPhone" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ReceiptOfProvince" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ReceiptOfCity" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ReceiptOfCounty" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ReceiptOfAddress" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf01" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf02" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf03" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf04" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf05" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf06" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf07" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf08" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf09" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf10" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf18" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf19" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf20" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf27" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf28" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf29" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf30" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Remark" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="EdiSyncFlag" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PickOfContact" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PickOfPhone" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PickOfIdNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PickOfAuthLetter" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Priority" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PtOutboundOrderDetailInfos" type="{OutboundService}ArrayOfPtOutboundOrderDetailInfo" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PtOutboundOrderInfo", propOrder = {
    "whID",
    "customerOrderID",
    "externalOrderID",
    "orderType",
    "orderDate",
    "requireDeliveryDate",
    "requirementsArrivalDate",
    "tmsTask",
    "ownerID",
    "customerID",
    "customerName",
    "receiptOfName",
    "receiptOfContact",
    "receiptOfPhone",
    "receiptOfProvince",
    "receiptOfCity",
    "receiptOfCounty",
    "receiptOfAddress",
    "udf01",
    "udf02",
    "udf03",
    "udf04",
    "udf05",
    "udf06",
    "udf07",
    "udf08",
    "udf09",
    "udf10",
    "udf11",
    "udf15",
    "udf17",
    "udf18",
    "udf19",
    "udf20",
    "udf27",
    "udf28",
    "udf29",
    "udf30",
    "udf34",
    "udf44",
    "remark",
    "ediSyncFlag",
    "pickOfContact",
    "pickOfPhone",
    "pickOfIdNumber",
    "pickOfAuthLetter",
    "priority",
    "ptOutboundOrderDetailInfos"
})
@XmlRootElement(name = "PtOutboundOrderInfo")
public class PtOutboundOrderInfo
    extends PotevioObj
{

    @XmlElement(name = "WhID")
    protected String whID;
    @XmlElement(name = "CustomerOrderID")
    protected String customerOrderID;
    @XmlElement(name = "ExternalOrderID")
    protected String externalOrderID;
    @XmlElement(name = "OrderType")
    protected String orderType;
    @XmlElement(name = "OrderDate", required = true)
    protected String orderDate;
    @XmlElement(name = "RequireDeliveryDate", required = true)
    protected String requireDeliveryDate;
    @XmlElement(name = "RequirementsArrivalDate", required = true)
    protected String requirementsArrivalDate;
    @XmlElement(name = "TmsTask")
    protected String tmsTask;
    @XmlElement(name = "OwnerID")
    protected String ownerID;
    @XmlElement(name = "CustomerID")
    protected String customerID;
    @XmlElement(name = "CustomerName")
    protected String customerName;
    @XmlElement(name = "ReceiptOfName")
    protected String receiptOfName;
    @XmlElement(name = "ReceiptOfContact")
    protected String receiptOfContact;
    @XmlElement(name = "ReceiptOfPhone")
    protected String receiptOfPhone;
    @XmlElement(name = "ReceiptOfProvince")
    protected String receiptOfProvince;
    @XmlElement(name = "ReceiptOfCity")
    protected String receiptOfCity;
    @XmlElement(name = "ReceiptOfCounty")
    protected String receiptOfCounty;
    @XmlElement(name = "ReceiptOfAddress")
    protected String receiptOfAddress;
    @XmlElement(name = "Udf01")
    protected String udf01;
    @XmlElement(name = "Udf02")
    protected String udf02;
    @XmlElement(name = "Udf03")
    protected String udf03;
    @XmlElement(name = "Udf04")
    protected String udf04;
    @XmlElement(name = "Udf05")
    protected String udf05;
    @XmlElement(name = "Udf06")
    protected String udf06;
    @XmlElement(name = "Udf07")
    protected String udf07;
    @XmlElement(name = "Udf08")
    protected String udf08;
    @XmlElement(name = "Udf09")
    protected String udf09;
    @XmlElement(name = "Udf10")
    protected String udf10;
    @XmlElement(name = "Udf11")
    protected String udf11;
    @XmlElement(name = "Udf17")
    protected String udf17;
    @XmlElement(name = "Udf15")
    protected String udf15;
    @XmlElement(name = "Udf18")
    protected String udf18;
    @XmlElement(name = "Udf19")
    protected String udf19;
    @XmlElement(name = "Udf20")
    protected String udf20;
    @XmlElement(name = "Udf27")
    protected String udf27;
    @XmlElement(name = "Udf28")
    protected String udf28;
    @XmlElement(name = "Udf29")
    protected String udf29;
    @XmlElement(name = "Udf30")
    protected String udf30;
    @XmlElement(name = "Udf34")
    protected String udf34;
    @XmlElement(name = "Udf44")
    protected String udf44;
    @XmlElement(name = "Remark")
    protected String remark;
    @XmlElement(name = "EdiSyncFlag")
    protected String ediSyncFlag;
    @XmlElement(name = "PickOfContact")
    protected String pickOfContact;
    @XmlElement(name = "PickOfPhone")
    protected String pickOfPhone;
    @XmlElement(name = "PickOfIdNumber")
    protected String pickOfIdNumber;
    @XmlElement(name = "PickOfAuthLetter")
    protected String pickOfAuthLetter;
    @XmlElement(name = "Priority")
    protected String priority;
    @XmlElement(name = "PtOutboundOrderDetailInfos")
    protected ArrayOfPtOutboundOrderDetailInfo ptOutboundOrderDetailInfos;

    /**
     * 获取whID属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWhID() {
        return whID;
    }

    /**
     * 设置whID属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWhID(String value) {
        this.whID = value;
    }

    /**
     * 获取customerOrderID属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustomerOrderID() {
        return customerOrderID;
    }

    /**
     * 设置customerOrderID属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustomerOrderID(String value) {
        this.customerOrderID = value;
    }

    /**
     * 获取externalOrderID属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getExternalOrderID() {
        return externalOrderID;
    }

    /**
     * 设置externalOrderID属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setExternalOrderID(String value) {
        this.externalOrderID = value;
    }

    /**
     * 获取orderType属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrderType() {
        return orderType;
    }

    /**
     * 设置orderType属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrderType(String value) {
        this.orderType = value;
    }

    /**
     * 获取orderDate属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrderDate() {
        return orderDate;
    }

    /**
     * 设置orderDate属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrderDate(String value) {
        this.orderDate = value;
    }

    /**
     * 获取requireDeliveryDate属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRequireDeliveryDate() {
        return requireDeliveryDate;
    }

    /**
     * 设置requireDeliveryDate属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRequireDeliveryDate(String value) {
        this.requireDeliveryDate = value;
    }

    /**
     * 获取requirementsArrivalDate属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRequirementsArrivalDate() {
        return requirementsArrivalDate;
    }

    /**
     * 设置requirementsArrivalDate属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRequirementsArrivalDate(String value) {
        this.requirementsArrivalDate = value;
    }

    /**
     * 获取tmsTask属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTmsTask() {
        return tmsTask;
    }

    /**
     * 设置tmsTask属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTmsTask(String value) {
        this.tmsTask = value;
    }

    /**
     * 获取ownerID属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOwnerID() {
        return ownerID;
    }

    /**
     * 设置ownerID属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOwnerID(String value) {
        this.ownerID = value;
    }

    /**
     * 获取customerID属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustomerID() {
        return customerID;
    }

    /**
     * 设置customerID属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustomerID(String value) {
        this.customerID = value;
    }

    /**
     * 获取customerName属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustomerName() {
        return customerName;
    }

    /**
     * 设置customerName属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustomerName(String value) {
        this.customerName = value;
    }

    /**
     * 获取receiptOfName属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReceiptOfName() {
        return receiptOfName;
    }

    /**
     * 设置receiptOfName属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReceiptOfName(String value) {
        this.receiptOfName = value;
    }

    /**
     * 获取receiptOfContact属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReceiptOfContact() {
        return receiptOfContact;
    }

    /**
     * 设置receiptOfContact属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReceiptOfContact(String value) {
        this.receiptOfContact = value;
    }

    /**
     * 获取receiptOfPhone属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReceiptOfPhone() {
        return receiptOfPhone;
    }

    /**
     * 设置receiptOfPhone属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReceiptOfPhone(String value) {
        this.receiptOfPhone = value;
    }

    /**
     * 获取receiptOfProvince属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReceiptOfProvince() {
        return receiptOfProvince;
    }

    /**
     * 设置receiptOfProvince属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReceiptOfProvince(String value) {
        this.receiptOfProvince = value;
    }

    /**
     * 获取receiptOfCity属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReceiptOfCity() {
        return receiptOfCity;
    }

    /**
     * 设置receiptOfCity属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReceiptOfCity(String value) {
        this.receiptOfCity = value;
    }

    /**
     * 获取receiptOfCounty属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReceiptOfCounty() {
        return receiptOfCounty;
    }

    /**
     * 设置receiptOfCounty属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReceiptOfCounty(String value) {
        this.receiptOfCounty = value;
    }

    /**
     * 获取receiptOfAddress属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReceiptOfAddress() {
        return receiptOfAddress;
    }

    /**
     * 设置receiptOfAddress属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReceiptOfAddress(String value) {
        this.receiptOfAddress = value;
    }

    /**
     * 获取udf01属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf01() {
        return udf01;
    }

    /**
     * 设置udf01属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf01(String value) {
        this.udf01 = value;
    }

    /**
     * 获取udf02属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf02() {
        return udf02;
    }

    /**
     * 设置udf02属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf02(String value) {
        this.udf02 = value;
    }

    /**
     * 获取udf03属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf03() {
        return udf03;
    }

    /**
     * 设置udf03属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf03(String value) {
        this.udf03 = value;
    }

    /**
     * 获取udf04属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf04() {
        return udf04;
    }

    /**
     * 设置udf04属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf04(String value) {
        this.udf04 = value;
    }

    /**
     * 获取udf05属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf05() {
        return udf05;
    }

    /**
     * 设置udf05属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf05(String value) {
        this.udf05 = value;
    }

    /**
     * 获取udf06属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf06() {
        return udf06;
    }

    /**
     * 设置udf06属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf06(String value) {
        this.udf06 = value;
    }

    /**
     * 获取udf07属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf07() {
        return udf07;
    }

    /**
     * 设置udf07属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf07(String value) {
        this.udf07 = value;
    }

    /**
     * 获取udf08属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf08() {
        return udf08;
    }

    /**
     * 设置udf08属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf08(String value) {
        this.udf08 = value;
    }

    /**
     * 获取udf09属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf09() {
        return udf09;
    }

    /**
     * 设置udf09属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf09(String value) {
        this.udf09 = value;
    }

    /**
     * 获取udf10属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf10() {
        return udf10;
    }

    /**
     * 设置udf10属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf10(String value) {
        this.udf10 = value;
    }

    /**
     * 获取udf18属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf18() {
        return udf18;
    }

    /**
     * 设置udf18属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf18(String value) {
        this.udf18 = value;
    }

    /**
     * 获取udf19属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf19() {
        return udf19;
    }

    /**
     * 设置udf19属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf19(String value) {
        this.udf19 = value;
    }

    /**
     * 获取udf20属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf20() {
        return udf20;
    }

    /**
     * 设置udf20属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf20(String value) {
        this.udf20 = value;
    }

    /**
     * 获取udf27属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf27() {
        return udf27;
    }

    /**
     * 设置udf27属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf27(String value) {
        this.udf27 = value;
    }

    /**
     * 获取udf28属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf28() {
        return udf28;
    }

    /**
     * 设置udf28属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf28(String value) {
        this.udf28 = value;
    }

    /**
     * 获取udf29属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf29() {
        return udf29;
    }

    /**
     * 设置udf29属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf29(String value) {
        this.udf29 = value;
    }

    /**
     * 获取udf30属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf30() {
        return udf30;
    }

    /**
     * 设置udf30属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf30(String value) {
        this.udf30 = value;
    }

    /**
     * 获取udf34属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf34() {
        return udf34;
    }

    /**
     * 设置udf34属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf34(String value) {
        this.udf34 = value;
    }

    public String getUdf44() {
        return udf44;
    }

    public void setUdf44(String value) {
        this.udf44 = value;
    }

    /**
     * 获取remark属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置remark属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRemark(String value) {
        this.remark = value;
    }

    /**
     * 获取ediSyncFlag属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEdiSyncFlag() {
        return ediSyncFlag;
    }

    /**
     * 设置ediSyncFlag属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEdiSyncFlag(String value) {
        this.ediSyncFlag = value;
    }

    /**
     * 获取pickOfContact属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPickOfContact() {
        return pickOfContact;
    }

    /**
     * 设置pickOfContact属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPickOfContact(String value) {
        this.pickOfContact = value;
    }

    /**
     * 获取pickOfPhone属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPickOfPhone() {
        return pickOfPhone;
    }

    /**
     * 设置pickOfPhone属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPickOfPhone(String value) {
        this.pickOfPhone = value;
    }

    /**
     * 获取pickOfIdNumber属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPickOfIdNumber() {
        return pickOfIdNumber;
    }

    /**
     * 设置pickOfIdNumber属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPickOfIdNumber(String value) {
        this.pickOfIdNumber = value;
    }

    /**
     * 获取pickOfAuthLetter属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPickOfAuthLetter() {
        return pickOfAuthLetter;
    }

    /**
     * 设置pickOfAuthLetter属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPickOfAuthLetter(String value) {
        this.pickOfAuthLetter = value;
    }

    /**
     * 获取priority属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPriority() {
        return priority;
    }

    /**
     * 设置priority属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPriority(String value) {
        this.priority = value;
    }

    public String getUdf11() {
        return udf11;
    }

    public void setUdf11(String udf11) {
        this.udf11 = udf11;
    }

    public String getUdf17() {
        return udf17;
    }

    public void setUdf17(String udf17) {
        this.udf17 = udf17;
    }

    public String getUdf15() {
        return udf15;
    }

    public void setUdf15(String udf15) {
        this.udf15 = udf15;
    }

    /**
     * 获取ptOutboundOrderDetailInfos属性的值。
     * 
     * @return
     *     possible object is
     *     {@link ArrayOfPtOutboundOrderDetailInfo }
     *     
     */
    public ArrayOfPtOutboundOrderDetailInfo getPtOutboundOrderDetailInfos() {
        return ptOutboundOrderDetailInfos;
    }

    /**
     * 设置ptOutboundOrderDetailInfos属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link ArrayOfPtOutboundOrderDetailInfo }
     *     
     */
    public void setPtOutboundOrderDetailInfos(ArrayOfPtOutboundOrderDetailInfo value) {
        this.ptOutboundOrderDetailInfos = value;
    }

}
