
package com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder;

import java.math.BigDecimal;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>PtOutboundOrderDetailInfo complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="PtOutboundOrderDetailInfo">
 *   &lt;complexContent>
 *     &lt;extension base="{OutboundService}PotevioObj">
 *       &lt;sequence>
 *         &lt;element name="SkuID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SkuDescr" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Qty" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="ExternalLineID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LotAttr01" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LotAttr02" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LotAttr03" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LotAttr04" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LotAttr05" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LotAttr06" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LotAttr07" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LotAttr08" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LotAttr09" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf01" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf02" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf03" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf04" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf05" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf06" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf07" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf08" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf09" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Udf10" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Remark" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CustPoNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PtOutboundOrderDetailInfo", propOrder = {
    "skuID",
    "skuDescr",
    "qty",
    "externalLineID",
    "lotAttr01",
    "lotAttr02",
    "lotAttr03",
    "lotAttr04",
    "lotAttr05",
    "lotAttr06",
    "lotAttr07",
    "lotAttr08",
    "lotAttr09",
    "udf01",
    "udf02",
    "udf03",
    "udf04",
    "udf05",
    "udf06",
    "udf07",
    "udf08",
    "udf09",
    "udf10",
    "remark",
    "custPoNumber"
})
public class PtOutboundOrderDetailInfo
    extends PotevioObj
{

    @XmlElement(name = "SkuID")
    protected String skuID;
    @XmlElement(name = "SkuDescr")
    protected String skuDescr;
    @XmlElement(name = "Qty", required = true)
    protected BigDecimal qty;
    @XmlElement(name = "ExternalLineID")
    protected String externalLineID;
    @XmlElement(name = "LotAttr01")
    protected String lotAttr01;
    @XmlElement(name = "LotAttr02")
    protected String lotAttr02;
    @XmlElement(name = "LotAttr03")
    protected String lotAttr03;
    @XmlElement(name = "LotAttr04")
    protected String lotAttr04;
    @XmlElement(name = "LotAttr05")
    protected String lotAttr05;
    @XmlElement(name = "LotAttr06")
    protected String lotAttr06;
    @XmlElement(name = "LotAttr07")
    protected String lotAttr07;
    @XmlElement(name = "LotAttr08")
    protected String lotAttr08;
    @XmlElement(name = "LotAttr09")
    protected String lotAttr09;
    @XmlElement(name = "Udf01")
    protected String udf01;
    @XmlElement(name = "Udf02")
    protected String udf02;
    @XmlElement(name = "Udf03")
    protected String udf03;
    @XmlElement(name = "Udf04")
    protected String udf04;
    @XmlElement(name = "Udf05")
    protected String udf05;
    @XmlElement(name = "Udf06")
    protected String udf06;
    @XmlElement(name = "Udf07")
    protected String udf07;
    @XmlElement(name = "Udf08")
    protected String udf08;
    @XmlElement(name = "Udf09")
    protected String udf09;
    @XmlElement(name = "Udf10")
    protected String udf10;
    @XmlElement(name = "Remark")
    protected String remark;
    @XmlElement(name = "CustPoNumber")
    protected String custPoNumber;

    /**
     * 获取skuID属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkuID() {
        return skuID;
    }

    /**
     * 设置skuID属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkuID(String value) {
        this.skuID = value;
    }

    /**
     * 获取skuDescr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkuDescr() {
        return skuDescr;
    }

    /**
     * 设置skuDescr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkuDescr(String value) {
        this.skuDescr = value;
    }

    /**
     * 获取qty属性的值。
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getQty() {
        return qty;
    }

    /**
     * 设置qty属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setQty(BigDecimal value) {
        this.qty = value;
    }

    /**
     * 获取externalLineID属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getExternalLineID() {
        return externalLineID;
    }

    /**
     * 设置externalLineID属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setExternalLineID(String value) {
        this.externalLineID = value;
    }

    /**
     * 获取lotAttr01属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLotAttr01() {
        return lotAttr01;
    }

    /**
     * 设置lotAttr01属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLotAttr01(String value) {
        this.lotAttr01 = value;
    }

    /**
     * 获取lotAttr02属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLotAttr02() {
        return lotAttr02;
    }

    /**
     * 设置lotAttr02属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLotAttr02(String value) {
        this.lotAttr02 = value;
    }

    /**
     * 获取lotAttr03属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLotAttr03() {
        return lotAttr03;
    }

    /**
     * 设置lotAttr03属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLotAttr03(String value) {
        this.lotAttr03 = value;
    }

    /**
     * 获取lotAttr04属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLotAttr04() {
        return lotAttr04;
    }

    /**
     * 设置lotAttr04属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLotAttr04(String value) {
        this.lotAttr04 = value;
    }

    /**
     * 获取lotAttr05属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLotAttr05() {
        return lotAttr05;
    }

    /**
     * 设置lotAttr05属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLotAttr05(String value) {
        this.lotAttr05 = value;
    }

    /**
     * 获取lotAttr06属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLotAttr06() {
        return lotAttr06;
    }

    /**
     * 设置lotAttr06属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLotAttr06(String value) {
        this.lotAttr06 = value;
    }

    /**
     * 获取lotAttr07属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLotAttr07() {
        return lotAttr07;
    }

    /**
     * 设置lotAttr07属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLotAttr07(String value) {
        this.lotAttr07 = value;
    }

    /**
     * 获取lotAttr08属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLotAttr08() {
        return lotAttr08;
    }

    /**
     * 设置lotAttr08属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLotAttr08(String value) {
        this.lotAttr08 = value;
    }

    /**
     * 获取lotAttr09属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLotAttr09() {
        return lotAttr09;
    }

    /**
     * 设置lotAttr09属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLotAttr09(String value) {
        this.lotAttr09 = value;
    }

    /**
     * 获取udf01属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf01() {
        return udf01;
    }

    /**
     * 设置udf01属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf01(String value) {
        this.udf01 = value;
    }

    /**
     * 获取udf02属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf02() {
        return udf02;
    }

    /**
     * 设置udf02属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf02(String value) {
        this.udf02 = value;
    }

    /**
     * 获取udf03属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf03() {
        return udf03;
    }

    /**
     * 设置udf03属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf03(String value) {
        this.udf03 = value;
    }

    /**
     * 获取udf04属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf04() {
        return udf04;
    }

    /**
     * 设置udf04属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf04(String value) {
        this.udf04 = value;
    }

    /**
     * 获取udf05属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf05() {
        return udf05;
    }

    /**
     * 设置udf05属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf05(String value) {
        this.udf05 = value;
    }

    /**
     * 获取udf06属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf06() {
        return udf06;
    }

    /**
     * 设置udf06属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf06(String value) {
        this.udf06 = value;
    }

    /**
     * 获取udf07属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf07() {
        return udf07;
    }

    /**
     * 设置udf07属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf07(String value) {
        this.udf07 = value;
    }

    /**
     * 获取udf08属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf08() {
        return udf08;
    }

    /**
     * 设置udf08属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf08(String value) {
        this.udf08 = value;
    }

    /**
     * 获取udf09属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf09() {
        return udf09;
    }

    /**
     * 设置udf09属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf09(String value) {
        this.udf09 = value;
    }

    /**
     * 获取udf10属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf10() {
        return udf10;
    }

    /**
     * 设置udf10属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf10(String value) {
        this.udf10 = value;
    }

    /**
     * 获取remark属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置remark属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRemark(String value) {
        this.remark = value;
    }

    /**
     * 获取custPoNumber属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustPoNumber() {
        return custPoNumber;
    }

    /**
     * 设置custPoNumber属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustPoNumber(String value) {
        this.custPoNumber = value;
    }

}
