package com.tl.rms.lib.wsclient.client.tw.request.inbound;

import com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder.PotevioObj;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * TW入库订单明细信息
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PtInboundOrderDetailInfo", propOrder = {
    "skuID",
    "skuDescr",
    "qty",
    "externalLineID",
    "lotAttr01",
    "udf01"
})
public class PtInboundOrderDetailInfo extends PotevioObj {

    @XmlElement(name = "SkuID")
    protected String skuID;

    @XmlElement(name = "SkuDescr")
    protected String skuDescr;

    @XmlElement(name = "Qty", required = true)
    protected BigDecimal qty;

    @XmlElement(name = "ExternalLineID")
    protected String externalLineID;

    @XmlElement(name = "LotAttr01")
    protected String lotAttr01;

    @XmlElement(name = "Udf01")
    protected String udf01;
}
