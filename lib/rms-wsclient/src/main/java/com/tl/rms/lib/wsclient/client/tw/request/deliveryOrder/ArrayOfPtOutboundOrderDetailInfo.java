
package com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder;

import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>ArrayOfPtOutboundOrderDetailInfo complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="ArrayOfPtOutboundOrderDetailInfo">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="PtOutboundOrderDetailInfo" type="{OutboundService}PtOutboundOrderDetailInfo" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ArrayOfPtOutboundOrderDetailInfo", propOrder = {
    "ptOutboundOrderDetailInfo"
})
public class ArrayOfPtOutboundOrderDetailInfo {

    @XmlElement(name = "PtOutboundOrderDetailInfo", nillable = true)
    protected List<PtOutboundOrderDetailInfo> ptOutboundOrderDetailInfo;

    /**
     * Gets the value of the ptOutboundOrderDetailInfo property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the ptOutboundOrderDetailInfo property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPtOutboundOrderDetailInfo().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PtOutboundOrderDetailInfo }
     * 
     * 
     */
    public List<PtOutboundOrderDetailInfo> getPtOutboundOrderDetailInfo() {
        if (ptOutboundOrderDetailInfo == null) {
            ptOutboundOrderDetailInfo = new ArrayList<PtOutboundOrderDetailInfo>();
        }
        return this.ptOutboundOrderDetailInfo;
    }

}
