
package com.tl.rms.lib.wsclient.client.ebs.ordersync;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>DiscountLineType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="DiscountLineType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="SourceHeaderId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SourceLineId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SourceCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OmLineNum" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="UsedAmount" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DiscountLineType", propOrder = {
    "sourceHeaderId",
    "sourceLineId",
    "sourceCode",
    "omLineNum",
    "usedAmount"
})
public class DiscountLineType {

    @XmlElement(name = "SourceHeaderId")
    protected String sourceHeaderId;
    @XmlElement(name = "SourceLineId")
    protected String sourceLineId;
    @XmlElement(name = "SourceCode")
    protected String sourceCode;
    @XmlElement(name = "OmLineNum")
    protected String omLineNum;
    @XmlElement(name = "UsedAmount")
    protected String usedAmount;

    /**
     * 获取sourceHeaderId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSourceHeaderId() {
        return sourceHeaderId;
    }

    /**
     * 设置sourceHeaderId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSourceHeaderId(String value) {
        this.sourceHeaderId = value;
    }

    /**
     * 获取sourceLineId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSourceLineId() {
        return sourceLineId;
    }

    /**
     * 设置sourceLineId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSourceLineId(String value) {
        this.sourceLineId = value;
    }

    /**
     * 获取sourceCode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSourceCode() {
        return sourceCode;
    }

    /**
     * 设置sourceCode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSourceCode(String value) {
        this.sourceCode = value;
    }

    /**
     * 获取omLineNum属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOmLineNum() {
        return omLineNum;
    }

    /**
     * 设置omLineNum属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOmLineNum(String value) {
        this.omLineNum = value;
    }

    /**
     * 获取usedAmount属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUsedAmount() {
        return usedAmount;
    }

    /**
     * 设置usedAmount属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUsedAmount(String value) {
        this.usedAmount = value;
    }

}
