
package com.tl.rms.lib.wsclient.client.ebs.signsync;

import jakarta.jws.WebMethod;
import jakarta.jws.WebParam;
import jakarta.jws.WebResult;
import jakarta.jws.WebService;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.ws.RequestWrapper;
import jakarta.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "SignServicePortType", targetNamespace = "http://www.putiantaili.com/soa/esb/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface SignServicePortType {


    /**
     * 
     * @param signInfo
     * @return
     *     returns com.pttl.tlmall.lib.wsclient.client.ebs.signsync.ResultInfo
     */
    @WebMethod(operationName = "Sign")
    @WebResult(name = "SignResponse", targetNamespace = "")
    @RequestWrapper(localName = "Sign", targetNamespace = "http://www.putiantaili.com/soa/esb/", className = "com.pttl.tlmall.lib.wsclient.client.ebs.signsync.Sign")
    @ResponseWrapper(localName = "SignResponse", targetNamespace = "http://putiantaili.soa.founder.com", className = "com.pttl.tlmall.lib.wsclient.client.ebs.signsync.SignResponse")
    public ResultInfo sign(
        @WebParam(name = "SignInfo", targetNamespace = "")
        SignInfo signInfo);

}
