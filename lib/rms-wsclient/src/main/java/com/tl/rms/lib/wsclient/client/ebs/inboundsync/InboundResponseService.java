
package com.tl.rms.lib.wsclient.client.ebs.inboundsync;

import jakarta.xml.ws.*;

import javax.xml.namespace.QName;
import java.net.MalformedURLException;
import java.net.URL;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebServiceClient(name = "InboundResponseService", targetNamespace = "http://www.putiantaili.com/soa/esb/", wsdlLocation = "http://192.168.220.90:7001/TLSI_0003/processes/InboundResponseService?wsdl")
public class InboundResponseService
    extends Service
{

    private final static URL INBOUNDRESPONSESERVICE_WSDL_LOCATION;
    private final static WebServiceException INBOUNDRESPONSESERVICE_EXCEPTION;
    private final static QName INBOUNDRESPONSESERVICE_QNAME = new QName("http://www.putiantaili.com/soa/esb/", "InboundResponseService");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://192.168.220.90:7001/TLSI_0003/processes/InboundResponseService?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        INBOUNDRESPONSESERVICE_WSDL_LOCATION = url;
        INBOUNDRESPONSESERVICE_EXCEPTION = e;
    }

    public InboundResponseService() {
        super(__getWsdlLocation(), INBOUNDRESPONSESERVICE_QNAME);
    }

    public InboundResponseService(WebServiceFeature... features) {
        super(__getWsdlLocation(), INBOUNDRESPONSESERVICE_QNAME, features);
    }

    public InboundResponseService(URL wsdlLocation) {
        super(wsdlLocation, INBOUNDRESPONSESERVICE_QNAME);
    }

    public InboundResponseService(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, INBOUNDRESPONSESERVICE_QNAME, features);
    }

    public InboundResponseService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public InboundResponseService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return
     *     returns InboundResponseServicePortType
     */
    @WebEndpoint(name = "InboundResponseServiceSOAP11port_http")
    public InboundResponseServicePortType getInboundResponseServiceSOAP11PortHttp() {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "InboundResponseServiceSOAP11port_http"), InboundResponseServicePortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns InboundResponseServicePortType
     */
    @WebEndpoint(name = "InboundResponseServiceSOAP11port_http")
    public InboundResponseServicePortType getInboundResponseServiceSOAP11PortHttp(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "InboundResponseServiceSOAP11port_http"), InboundResponseServicePortType.class, features);
    }

    /**
     *
     * @return
     *     returns InboundResponseServicePortType
     */
    @WebEndpoint(name = "InboundResponseServiceSOAP12port_http")
    public InboundResponseServicePortType getInboundResponseServiceSOAP12PortHttp() {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "InboundResponseServiceSOAP12port_http"), InboundResponseServicePortType.class);
    }

    /**
     *
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns InboundResponseServicePortType
     */
    @WebEndpoint(name = "InboundResponseServiceSOAP12port_http")
    public InboundResponseServicePortType getInboundResponseServiceSOAP12PortHttp(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "InboundResponseServiceSOAP12port_http"), InboundResponseServicePortType.class, features);
    }

    /**
     *
     * @return
     *     returns InboundResponseServicePortType
     */
    @WebEndpoint(name = "InboundResponseServiceHttpport")
    public InboundResponseServicePortType getInboundResponseServiceHttpport() {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "InboundResponseServiceHttpport"), InboundResponseServicePortType.class);
    }

    /**
     *
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns InboundResponseServicePortType
     */
    @WebEndpoint(name = "InboundResponseServiceHttpport")
    public InboundResponseServicePortType getInboundResponseServiceHttpport(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.putiantaili.com/soa/esb/", "InboundResponseServiceHttpport"), InboundResponseServicePortType.class, features);
    }

    private static URL __getWsdlLocation() {
        if (INBOUNDRESPONSESERVICE_EXCEPTION!= null) {
            throw INBOUNDRESPONSESERVICE_EXCEPTION;
        }
        return INBOUNDRESPONSESERVICE_WSDL_LOCATION;
    }

}
