
package com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder;

import jakarta.xml.bind.annotation.*;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="AddOutboundOrderResult" type="{OutboundService}ResultInfo" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "addOutboundOrderResult"
})
@XmlRootElement(name = "AddOutboundOrderResponse")
public class AddOutboundOrderResponse {

    @XmlElement(name = "AddOutboundOrderResult")
    protected ResultInfo addOutboundOrderResult;

    /**
     * 获取addOutboundOrderResult属性的值。
     * 
     * @return
     *     possible object is
     *     {@link ResultInfo }
     *     
     */
    public ResultInfo getAddOutboundOrderResult() {
        return addOutboundOrderResult;
    }

    /**
     * 设置addOutboundOrderResult属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link ResultInfo }
     *     
     */
    public void setAddOutboundOrderResult(ResultInfo value) {
        this.addOutboundOrderResult = value;
    }

}
