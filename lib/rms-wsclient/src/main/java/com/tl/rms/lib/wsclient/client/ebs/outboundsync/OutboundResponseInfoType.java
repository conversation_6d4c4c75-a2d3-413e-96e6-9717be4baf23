
package com.tl.rms.lib.wsclient.client.ebs.outboundsync;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>OutboundResponseInfoType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="OutboundResponseInfoType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="OrderId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="OwnerId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Type" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CustomerOrderId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ExternalOrderId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CustomerId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ReceiptName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf01" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf02" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf03" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf04" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf05" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf07" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf08" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="LotAttr01" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="TransactionDate" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="DetailItems">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="OutboundResponseItem" type="{http://www.putiantaili.com/soa/esb/}OutboundResponseItemType" maxOccurs="unbounded"/>
 *                 &lt;/sequence>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OutboundResponseInfoType", propOrder = {
    "orderId",
    "ownerId",
    "type",
    "customerOrderId",
    "externalOrderId",
    "customerId",
    "receiptName",
    "udf01",
    "udf02",
    "udf03",
    "udf04",
    "udf05",
    "udf07",
    "udf08",
    "lotAttr01",
    "transactionDate",
    "detailItems"
})
public class OutboundResponseInfoType {

    @XmlElement(name = "OrderId", required = true)
    protected String orderId;
    @XmlElement(name = "OwnerId", required = true)
    protected String ownerId;
    @XmlElement(name = "Type", required = true)
    protected String type;
    @XmlElement(name = "CustomerOrderId", required = true)
    protected String customerOrderId;
    @XmlElement(name = "ExternalOrderId", required = true)
    protected String externalOrderId;
    @XmlElement(name = "CustomerId", required = true)
    protected String customerId;
    @XmlElement(name = "ReceiptName", required = true)
    protected String receiptName;
    @XmlElement(name = "Udf01", required = true)
    protected String udf01;
    @XmlElement(name = "Udf02", required = true)
    protected String udf02;
    @XmlElement(name = "Udf03", required = true)
    protected String udf03;
    @XmlElement(name = "Udf04", required = true)
    protected String udf04;
    @XmlElement(name = "Udf05", required = true)
    protected String udf05;
    @XmlElement(name = "Udf07", required = true)
    protected String udf07;
    @XmlElement(name = "Udf08", required = true)
    protected String udf08;
    @XmlElement(name = "LotAttr01", required = true)
    protected String lotAttr01;
    @XmlElement(name = "TransactionDate")
    protected String transactionDate;
    @XmlElement(name = "DetailItems", required = true)
    protected DetailItems detailItems;

    /**
     * 获取orderId属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * 设置orderId属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOrderId(String value) {
        this.orderId = value;
    }

    /**
     * 获取ownerId属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOwnerId() {
        return ownerId;
    }

    /**
     * 设置ownerId属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOwnerId(String value) {
        this.ownerId = value;
    }

    /**
     * 获取type属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getType() {
        return type;
    }

    /**
     * 设置type属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * 获取customerOrderId属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getCustomerOrderId() {
        return customerOrderId;
    }

    /**
     * 设置customerOrderId属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCustomerOrderId(String value) {
        this.customerOrderId = value;
    }

    /**
     * 获取externalOrderId属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getExternalOrderId() {
        return externalOrderId;
    }

    /**
     * 设置externalOrderId属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setExternalOrderId(String value) {
        this.externalOrderId = value;
    }

    /**
     * 获取customerId属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getCustomerId() {
        return customerId;
    }

    /**
     * 设置customerId属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCustomerId(String value) {
        this.customerId = value;
    }

    /**
     * 获取receiptName属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getReceiptName() {
        return receiptName;
    }

    /**
     * 设置receiptName属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setReceiptName(String value) {
        this.receiptName = value;
    }

    /**
     * 获取udf01属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf01() {
        return udf01;
    }

    /**
     * 设置udf01属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf01(String value) {
        this.udf01 = value;
    }

    /**
     * 获取udf02属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf02() {
        return udf02;
    }

    /**
     * 设置udf02属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf02(String value) {
        this.udf02 = value;
    }

    /**
     * 获取udf03属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf03() {
        return udf03;
    }

    /**
     * 设置udf03属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf03(String value) {
        this.udf03 = value;
    }

    /**
     * 获取udf04属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf04() {
        return udf04;
    }

    /**
     * 设置udf04属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf04(String value) {
        this.udf04 = value;
    }

    /**
     * 获取udf05属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf05() {
        return udf05;
    }

    /**
     * 设置udf05属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf05(String value) {
        this.udf05 = value;
    }

    /**
     * 获取udf07属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf07() {
        return udf07;
    }

    /**
     * 设置udf07属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf07(String value) {
        this.udf07 = value;
    }

    /**
     * 获取udf08属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUdf08() {
        return udf08;
    }

    /**
     * 设置udf08属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUdf08(String value) {
        this.udf08 = value;
    }

    /**
     * 获取lotAttr01属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getLotAttr01() {
        return lotAttr01;
    }

    /**
     * 设置lotAttr01属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setLotAttr01(String value) {
        this.lotAttr01 = value;
    }

    /**
     * 获取transactionDate属性的值。
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getTransactionDate() {
        return transactionDate;
    }

    /**
     * 设置transactionDate属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setTransactionDate(String value) {
        this.transactionDate = value;
    }

    /**
     * 获取detailItems属性的值。
     *
     * @return
     *     possible object is
     *     {@link DetailItems }
     *
     */
    public DetailItems getDetailItems() {
        return detailItems;
    }

    /**
     * 设置detailItems属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link DetailItems }
     *
     */
    public void setDetailItems(DetailItems value) {
        this.detailItems = value;
    }


    /**
     * <p>anonymous complex type的 Java 类。
     * 
     * <p>以下模式片段指定包含在此类中的预期内容。
     * 
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="OutboundResponseItem" type="{http://www.putiantaili.com/soa/esb/}OutboundResponseItemType" maxOccurs="unbounded"/>
     *       &lt;/sequence>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "outboundResponseItem"
    })
    public static class DetailItems {

        @XmlElement(name = "OutboundResponseItem", required = true)
        protected List<OutboundResponseItemType> outboundResponseItem;

        /**
         * Gets the value of the outboundResponseItem property.
         * 
         * <p>
         * This accessor method returns a reference to the live list,
         * not a snapshot. Therefore any modification you make to the
         * returned list will be present inside the JAXB object.
         * This is why there is not a <CODE>set</CODE> method for the outboundResponseItem property.
         * 
         * <p>
         * For example, to add a new item, do as follows:
         * <pre>
         *    getOutboundResponseItem().add(newItem);
         * </pre>
         * 
         * 
         * <p>
         * Objects of the following type(s) are allowed in the list
         * {@link OutboundResponseItemType }
         * 
         * 
         */
        public List<OutboundResponseItemType> getOutboundResponseItem() {
            if (outboundResponseItem == null) {
                outboundResponseItem = new ArrayList<OutboundResponseItemType>();
            }
            return this.outboundResponseItem;
        }

    }

}
