
package com.tl.rms.lib.wsclient.client.ebs.inboundsync;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>InboundResponseItemType complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="InboundResponseItemType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="SkuId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ExpectedQuantity" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="ReceivedQuantity" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="ExternLineId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="LotAttr01" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="LotAttr09" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Udf10" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Attribute2" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Attribute3" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Attribute4" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Attribute5" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "InboundResponseItemType", propOrder = {
    "skuId",
    "expectedQuantity",
    "receivedQuantity",
    "externLineId",
    "lotAttr01",
    "lotAttr09",
    "udf10",
    "attribute2",
    "attribute3",
    "attribute4",
    "attribute5",
    "returnAmount"
})
public class InboundResponseItemType {

    @XmlElement(name = "SkuId", required = true)
    protected String skuId;
    @XmlElement(name = "ExpectedQuantity")
    protected long expectedQuantity;
    @XmlElement(name = "ReceivedQuantity")
    protected long receivedQuantity;
    @XmlElement(name = "ExternLineId", required = true)
    protected String externLineId;
    @XmlElement(name = "LotAttr01", required = true)
    protected String lotAttr01;
    @XmlElement(name = "LotAttr09", required = true)
    protected String lotAttr09;
    @XmlElement(name = "Udf10", required = true)
    protected String udf10;
    @XmlElement(name = "Attribute2", required = true)
    protected String attribute2;
    @XmlElement(name = "Attribute3", required = true)
    protected String attribute3;
    @XmlElement(name = "Attribute4", required = true)
    protected String attribute4;
    @XmlElement(name = "Attribute5", required = true)
    protected String attribute5;
    @XmlElement(name = "ReturnAmount")
    protected String returnAmount;

    /**
     * 获取skuId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkuId() {
        return skuId;
    }

    /**
     * 设置skuId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkuId(String value) {
        this.skuId = value;
    }

    /**
     * 获取expectedQuantity属性的值。
     * 
     */
    public long getExpectedQuantity() {
        return expectedQuantity;
    }

    /**
     * 设置expectedQuantity属性的值。
     * 
     */
    public void setExpectedQuantity(long value) {
        this.expectedQuantity = value;
    }

    /**
     * 获取receivedQuantity属性的值。
     * 
     */
    public long getReceivedQuantity() {
        return receivedQuantity;
    }

    /**
     * 设置receivedQuantity属性的值。
     * 
     */
    public void setReceivedQuantity(long value) {
        this.receivedQuantity = value;
    }

    /**
     * 获取externLineId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getExternLineId() {
        return externLineId;
    }

    /**
     * 设置externLineId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setExternLineId(String value) {
        this.externLineId = value;
    }

    /**
     * 获取lotAttr01属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLotAttr01() {
        return lotAttr01;
    }

    /**
     * 设置lotAttr01属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLotAttr01(String value) {
        this.lotAttr01 = value;
    }

    /**
     * 获取lotAttr09属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLotAttr09() {
        return lotAttr09;
    }

    /**
     * 设置lotAttr09属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLotAttr09(String value) {
        this.lotAttr09 = value;
    }

    /**
     * 获取udf10属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUdf10() {
        return udf10;
    }

    /**
     * 设置udf10属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUdf10(String value) {
        this.udf10 = value;
    }

    /**
     * 获取attribute2属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttribute2() {
        return attribute2;
    }

    /**
     * 设置attribute2属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttribute2(String value) {
        this.attribute2 = value;
    }

    /**
     * 获取attribute3属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttribute3() {
        return attribute3;
    }

    /**
     * 设置attribute3属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttribute3(String value) {
        this.attribute3 = value;
    }

    /**
     * 获取attribute4属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttribute4() {
        return attribute4;
    }

    /**
     * 设置attribute4属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttribute4(String value) {
        this.attribute4 = value;
    }

    /**
     * 获取attribute5属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttribute5() {
        return attribute5;
    }

    /**
     * 设置attribute5属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttribute5(String value) {
        this.attribute5 = value;
    }

    public String getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(String returnAmount) {
        this.returnAmount = returnAmount;
    }
}
