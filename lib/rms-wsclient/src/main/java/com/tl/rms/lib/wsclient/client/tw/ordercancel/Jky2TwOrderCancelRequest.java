package com.tl.rms.lib.wsclient.client.tw.ordercancel;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Data;

/**
 * 取消订单请求实体类
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "request")
public class Jky2TwOrderCancelRequest {

    /**
     * 仓库编码, string (50)，必填 ，统仓统配等无需 ERP 指定仓储编码的情况填 OTHER
     */
    @XmlElement(name = "warehouseCode", required = true)
    private String warehouseCode;

    /**
     * 货主编码, string (50)
     */
    @XmlElement(name = "ownerCode")
    private String ownerCode;

    /**
     * 单据编码, string (50) ，必填
     */
    @XmlElement(name = "orderCode", required = true)
    private String orderCode;

    /**
     * 仓储系统单据编码, string (50) ，条件必填
     */
    @XmlElement(name = "orderId")
    private String orderId;

    /**
     * 单据类型, JYCK= 一般交易出库单，HHCK= 换货出库 ，BFCK= 补发 出库
     * PTCK=普通出库单，DBCK=调拨出库 ，B2BRK=B2B 入库，B2BCK=B2B 出库，
     * QTCK=其他出库， SCRK=生产入库，LYRK=领用入库，CCRK=残次品入库，CGRK=采 购入库 ，
     * DBRK= 调拨入库 ，QTRK= 其他入库 ，XTRK= 销退入库，THRK=退货入库， HHRK= 换货入库 ，
     * CNJG= 仓内加工单 ，CGTH=采购退货出库单
     */
    @XmlElement(name = "orderType")
    private String orderType;

    /**
     * 取消原因, string (500)
     */
    @XmlElement(name = "cancelReason")
    private String cancelReason;
}
