package com.tl.rms.lib.wsclient.client.tw;

import com.qimen.api.QimenResponse;
import com.tl.rms.common.exception.CommonException;
import com.tl.rms.lib.wsclient.client.tw.ordercancel.Jky2TwOrderCancelRequest;
import com.tl.rms.lib.wsclient.client.tw.request.TwCommonCallbackRequest;
import com.tl.rms.lib.wsclient.common.LoggingHandler;
import com.tl.rms.lib.wsclient.config.WebServiceClientConfig;
import com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder.PtOutboundOrderInfo;
import com.tl.rms.lib.wsclient.vo.QiMenResponse;
import com.tl.rms.lib.wsclient.client.tw.response.Response;
import com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder.ResultInfo;
import com.tl.rms.util.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.net.URL;
import java.util.Collections;

/**
 * 调用TW系统的接口
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InvokeTwService {

    private final WebServiceClientConfig wsClientConfig;

    // 单据取消 FIXME 待确认接口地址
    private static final String JKY_2_TW_ORDER_CANCEL = "/xxx/xxx/orderCancel";
    private static final String ORDER_SYNC_WSDL = "/TLSI_0049/processes/OutboundService?wsdl";
    private static final String QIMEN_CALLBACK_URL = "";

    // 服务标识 - 用于日志追踪
    private static final String SERVICE_NAME = "RMS_TW";

    /**
     * 调用TW系统订单取消接口
     *
     * @param request 订单取消请求参数
     * @return TW系统响应结果
     */
    public QiMenResponse rms2TwOrderCancel(Jky2TwOrderCancelRequest request) {
        String methodName = "rms2TwOrderCancel";
        long startTime = System.currentTimeMillis();
        String traceId = String.valueOf(System.currentTimeMillis());

        log.info("[{}][{}] traceId={} start", SERVICE_NAME, methodName, traceId);

        // 请求参数校验
        QiMenResponse validateResponse = validateJky2TwOrderCancel(request);
        if (validateResponse != null) {
            log.warn("[{}][{}] traceId={} validation failed: {}", SERVICE_NAME, methodName, traceId, validateResponse.getMessage());
            return validateResponse;
        }

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_XML);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_XML));
            headers.add("biztype", "WMS_ORDER_CANCEL_SYNC");

            HttpEntity<Jky2TwOrderCancelRequest> httpEntity = new HttpEntity<>(request, headers);
            String url = wsClientConfig.getEsbWsAddress() + JKY_2_TW_ORDER_CANCEL;
            log.debug("[{}][{}] traceId={} sending request to url: {}", SERVICE_NAME, methodName, traceId, url);

            ResponseEntity<QiMenResponse> responseEntity =
                    new RestTemplate().postForEntity(url, httpEntity, QiMenResponse.class);
            QiMenResponse response = responseEntity.getBody();

            long endTime = System.currentTimeMillis();
            long costTime = endTime - startTime;

            if (response != null) {
                log.info("[{}][{}] traceId={} end, cost: {} ms, responseFlag: {}, responseCode: {}, responseMessage: {}",
                        SERVICE_NAME, methodName, traceId, costTime, response.getFlag(), response.getCode(), response.getMessage());
            } else {
                log.warn("[{}][{}] traceId={} end, cost: {} ms, response is null", SERVICE_NAME, methodName, traceId, costTime);
            }

            return response;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long costTime = endTime - startTime;

            log.error("[{}][{}] traceId={} error, cost: {} ms", SERVICE_NAME, methodName, traceId, costTime, e);
            return QiMenResponse.failure("单据取消异常:" + e.getMessage());
        }
    }

    /**
     * 校验订单取消请求参数
     *
     * @param request 请求参数
     * @return 校验失败返回错误响应，校验通过返回null
     */
    private QiMenResponse validateJky2TwOrderCancel(Jky2TwOrderCancelRequest request) {

        if (request == null) {
            return QiMenResponse.failure("请求参数不能为空");
        }

        if (!StringUtils.hasText(request.getWarehouseCode())) {
            return QiMenResponse.failure("仓库编码(warehouseCode)不能为空");
        }

        if (!StringUtils.hasText(request.getOrderCode())) {
            return QiMenResponse.failure("单据编码(orderCode)不能为空");
        }

        return null;
    }

    /**
     * 调用TW订单同步接口
     */
    @SneakyThrows
    public Response invokeTwOrderSyncService(PtOutboundOrderInfo request) {
        log.info("invokeTWOrderSyncService request: {}", request == null ? null : JsonUtil.toJson(request));
        if (request == null) {
            return Response.badRequest();
        }

        URL wsdl = new URL(wsClientConfig.getEsbWsAddress() + ORDER_SYNC_WSDL);
        OutboundService ss = new OutboundService(wsdl);
        OutboundServicePortType port = ss.getOutboundServiceSOAP11PortHttp();
        // 日志
        LoggingHandler.handleMessage(port);
        ResultInfo response = port.addOutboundOrder(request);
        log.info("invokeTWOrderSyncService response: {}", response == null ? null : JsonUtil.toJson(response));
        if (response != null) {
            return new Response(Integer.valueOf(response.getReturnCode()), response.getErrorMsg(),
                    response.getDateTime());
        } else {
            throw new CommonException(Response.noResponse().getErrorMsg());
        }
    }

    public <T extends QimenResponse> void callback(String requestBody, T t, TwCommonCallbackRequest.BizType bizType) {
        if (t == null) {
            throw new CommonException("奇门响应信息为空");
        }

        TwCommonCallbackRequest request = TwCommonCallbackRequest.builder().sources(requestBody)
            .code(TwCommonCallbackRequest.SUCCESS_CODE).bizType(bizType.name()).msg(convertRespObjToOrigin(t))
            // suixin todo 待确认
            // .msgId()
            // .orgKey()
            .build();

        // 调用TW
        Response response = new RestTemplate().postForObject(QIMEN_CALLBACK_URL, request, Response.class);
        // suixin todo 返回值如何处理
    }

    private static <T extends QimenResponse> String convertRespObjToOrigin(T t) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("<response>");
        if (t.getFlag() != null) {
            stringBuilder.append("<flag>");
            stringBuilder.append(t.getFlag());
            stringBuilder.append("</flag>");
        }
        if (t.getCode() != null) {
            stringBuilder.append("<code>");
            stringBuilder.append(t.getCode());
            stringBuilder.append("</code>");
        }
        stringBuilder.append("</response>");
        return stringBuilder.toString();
    }
}
