package com.tl.rms.lib.wsclient.client.qimen.stockchange;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElementWrapper;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Data;

import java.util.List;

/**
 * 库存异动请求实体类
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "request")
public class Tw2JkyStockChangeRequest {

    /**
     * 商品列表
     */
    @XmlElementWrapper(name = "items")
    @XmlElement(name = "item")
    private List<StockItem> items;

    /**
     * 单个商品库存信息
     */
    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class StockItem {

        /**
         * 货主编码, string (50) , 必填
         */
        @XmlElement(name = "ownerCode", required = true)
        private String ownerCode;

        /**
         * 仓库编码, string (50)，必填
         */
        @XmlElement(name = "warehouseCode", required = true)
        private String warehouseCode;

        /**
         * 引起异动的单据编码，string（50），必填
         */
        @XmlElement(name = "orderCode", required = true)
        private String orderCode;

        /**
         * 单据类型 ，string（50），JYCK= 一般交易出库单，HHCK= 换货出库 ，BFCK= 补发出库
         * PTCK=普通出库单，DBCK=调拨出库 ，QTCK=其他出库，SCRK=生产入库，LYRK=领用入库，
         * CCRK=残次品入库，CGRK=采购入库 ，DBRK= 调拨入库 ，QTRK= 其他入库 ，XTRK= 销退入库
         * HHRK= 换货入库 CNJG= 仓内加工单 ZTTZ=状态调整单
         */
        @XmlElement(name = "orderType")
        private String orderType;

        /**
         * 外部业务编码, 消息 ID, 用于去重，用来保证因为网络等原因导致重复传输，请求不会被重复处理，必填
         */
        @XmlElement(name = "outBizCode", required = true)
        private String outBizCode;

        /**
         * 商品编码, string (50) , 必填
         */
        @XmlElement(name = "itemCode", required = true)
        private String itemCode;

        /**
         * 仓储系统商品 ID, string (50)，条件必填
         */
        @XmlElement(name = "itemId")
        private String itemId;

        /**
         * 库存类型，string (50) , ZP=正品, CC=残次,JS=机损, XS= 箱损, ZT=在途库存，DJ=冻结
         */
        @XmlElement(name = "inventoryType")
        private String inventoryType;

        /**
         * 库存是否冻结, Y|N
         */
        @XmlElement(name = "isLocked")
        private String isLocked;

        /**
         * 商品变化量，int，必填，可为正为负
         */
        @XmlElement(name = "quantity", required = true)
        private Integer quantity;

        /**
         * 批次编码, string (50)
         */
        @XmlElement(name = "batchCode")
        private String batchCode;

        /**
         * 商品生产日期 YYYY-MM-DD
         */
        @XmlElement(name = "productDate")
        private String productDate;

        /**
         * 商品过期日期 YYYY-MM-DD
         */
        @XmlElement(name = "expireDate")
        private String expireDate;

        /**
         * 生产批号, string (50)
         */
        @XmlElement(name = "produceCode")
        private String produceCode;

        /**
         * 异动时间, string (19) , YYYY-MM-DD HH:MM:SS
         */
        @XmlElement(name = "changeTime")
        private String changeTime;

        /**
         * 同一商品下多批次支持
         */
        @XmlElementWrapper(name = "batchs")
        @XmlElement(name = "batch")
        private List<BatchInfo> batchs;

        /**
         * 备注, string (500)
         */
        @XmlElement(name = "remark")
        private String remark;

        /**
         * 批次信息
         */
        @Data
        @XmlAccessorType(XmlAccessType.FIELD)
        public static class BatchInfo {

            /**
             * 批次编号，string(50)
             */
            @XmlElement(name = "batchCode")
            private String batchCode;

            /**
             * 生产日期，string(10)，YYYY-MM-DD
             */
            @XmlElement(name = "productDate")
            private String productDate;

            /**
             * 过期日期，string(10)，YYYY-MM-DD
             */
            @XmlElement(name = "expireDate")
            private String expireDate;

            /**
             * 生产批号，string(50)
             */
            @XmlElement(name = "produceCode")
            private String produceCode;

            /**
             * 库存类型，string (50) , ZP=正品, CC=残次,JS=机损, XS= 箱损, ZT=在途库存，DJ=冻结
             */
            @XmlElement(name = "inventoryType")
            private String inventoryType;

            /**
             * 异动数量, int，要求 batchs 节点下所有的异动数量之和等于 orderline 中的异动数量
             */
            @XmlElement(name = "quantity")
            private Integer quantity;
        }
    }
}
