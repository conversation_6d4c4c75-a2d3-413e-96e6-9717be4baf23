<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>rms-lib</artifactId>
		<groupId>com.tl.rms.lib</groupId>
		<version>1.0</version>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<artifactId>rms-wsclient</artifactId>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>com.tl.rms.lib</groupId>
			<artifactId>rms-logging</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>
		<!--<dependency>
			<groupId>com.tl.tlmall.lib</groupId>
			<artifactId>tl-log</artifactId>
		</dependency>-->

		<dependency>
			<groupId>com.sun.xml.ws</groupId>
			<artifactId>jaxws-rt</artifactId>
			<version>4.0.2</version>
		</dependency>

		<!--<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
		</dependency>-->
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.xml.ws</groupId>
			<artifactId>jakarta.xml.ws-api</artifactId>
		</dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-util</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-common</artifactId>
        </dependency>

		<dependency>
			<groupId>com.taobao</groupId>
			<artifactId>taobao-sdk-java-auto</artifactId>
			<version>1594021799456-20250811</version>
		</dependency>

		<dependency>
			<groupId>com.tl.rms.lib</groupId>
			<artifactId>rms-common</artifactId>
		</dependency>
	</dependencies>
</project>
