package com.tl.rms.lib.logging;

import com.tl.rms.util.JsonUtil;
import org.slf4j.Logger;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * 日志格式化
 * <AUTHOR>
 * @date 2021/7/15
 */
public class LogFormat {

    private static final String LOG_MSG = "action:{} type:{} reason:{} original:{}";

    private LogFormat() {
    }

    /**
     * 异常日志格式化，用于日志采集
     *
     * @param action 动作：
     *               mq，传队列名，如：orderApprove
     *               webservice，传invoke的方法名，如：invokeBMCStoreMappingSyncService
     *               rest和http，传调用的方法url，如：InvokeHwService.PICK_QTY_SYNC_URL
     * @param type 类型：系统-组件
     *             @see com.tl.rms.lib.logging.LogTypeEnum
     * @param reason 异常原因：传String和Throwable类型
     *               有报错原因的传String
     *               catch到的异常应直接传Exception
     * @param original 请求来源的参数
     * <AUTHOR>
     * @date 2021/7/15
     */
    public static void error(Logger log, String action, String type, Object reason, Object original){
        String reasonStr;
        if(reason instanceof Throwable){
            reasonStr = convertMessage((Throwable)reason);
        }else{
            reasonStr = reason.toString();
        }
        String originalJson = JsonUtil.toJson(original);
        log.error(LOG_MSG, action, type, reasonStr, originalJson);
    }

    /**
     * 异常堆栈信息转字符串
     * <AUTHOR>
     * @date 2021/7/20
     */
    private static String convertMessage(Throwable e){
        if(e == null){
            return null;
        }
        StringWriter sw = new StringWriter(512);
        PrintWriter pw = new PrintWriter(sw);
        e.printStackTrace(pw);
        return sw.getBuffer().toString();
    }

    /**
     * 没有Response的错误日志
     * <AUTHOR>
     * @date 2021/7/24
     */
    public static void errorNoResponse(Logger log, Object request){
        String requestJson = JsonUtil.toJson(request);
        log.error("request:{} response:No Response", requestJson);
    }

}
