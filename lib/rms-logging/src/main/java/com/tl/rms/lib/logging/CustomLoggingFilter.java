package com.tl.rms.lib.logging;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * 需要运行在SessionFilter之后
 * see org.springframework.session.web.http.SessionRepositoryFilter#DEFAULT_ORDER
 */
@Order(Integer.MIN_VALUE + 50 + 10)
@Slf4j
public class CustomLoggingFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, <PERSON><PERSON><PERSON><PERSON><PERSON> filter<PERSON>hain) throws ServletException, IOException {
        MDC.put("ip", request.getHeader("ctx_ip"));
        MDC.put("sessionId", request.getHeader("ctx_session_id"));
        MDC.put("userId", request.getHeader("ctx_request_user_id"));
        MDC.put("clientId", request.getHeader("ctx_client_id"));
        MDC.put("isMobile", "1".equals(request.getHeader("ismobile")) ? "" : "PC");
        String requestURI = request.getHeader("ctx_request_uri");
        MDC.put("requestURI", requestURI);
        filterChain.doFilter(request, response);
    }

}
