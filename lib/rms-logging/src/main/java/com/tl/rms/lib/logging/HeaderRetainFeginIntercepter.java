package com.tl.rms.lib.logging;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.*;

@Configuration
public class HeaderRetainFeginIntercepter implements RequestInterceptor {

    private List<String> retainedHeaders = new ArrayList<>();


    @PostConstruct
    protected void init() {
        retainedHeaders.add("ctx_ip");
        retainedHeaders.add("ctx_session_id");
        retainedHeaders.add("ctx_request_uri");
        retainedHeaders.add("ctx_request_user_id");
        retainedHeaders.add("ctx_client_id");
        retainedHeaders.add("X-Real-IP");
        retainedHeaders.add("ismobile");
        retainedHeaders.add("ismac");
    }

    @Override
    public void apply(RequestTemplate requestTemplate) {
        Map<String, String> headers = getHeaders(getHttpServletRequest());
        if (headers == null || headers.isEmpty()) {
            return;
        }
        for (String headerName : headers.keySet()) {
            if (retainedHeaders != null && retainedHeaders.contains(headerName)) {
                requestTemplate.header(headerName, headers.get(headerName));
            }
        }
    }

    private HttpServletRequest getHttpServletRequest() {
        try {
            if (RequestContextHolder.getRequestAttributes() == null) {
                return null;
            }
            return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        } catch (Exception e) {
            return null;
        }
    }

    private Map<String, String> getHeaders(HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        Map<String, String> map = new LinkedHashMap<String, String>();
        Enumeration<String> enumeration = request.getHeaderNames();
        if (enumeration == null) {
            return null;
        }
        while (enumeration.hasMoreElements()) {
            String key = enumeration.nextElement();
            String value = request.getHeader(key);
            map.put(key, value);
        }
        return map;
    }

}
