package com.tl.rms.lib.logging;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@ComponentScan("com.tl.rms.lib.logging")
public class CustomLoggingAutoConfiguration {

    @Bean
    public FilterRegistrationBean myOncePerRequestFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new CustomLoggingFilter());
        // 拦截路径
        registration.addUrlPatterns("/*");
        // 拦截器名称
        registration.setName("CustomLoggingFilter");
        // 顺序
        registration.setOrder(Integer.MIN_VALUE + 50 + 10);
        return registration;
    }

}
