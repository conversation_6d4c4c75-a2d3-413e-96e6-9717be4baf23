package com.tl.rms.lib.fulfillment.message;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: zengpeng
 * : TW行取消反馈
 * @date 9/5/2019 4:21 PM
 * <p>
 * 接收TW行取消反馈:
 * 行取消发送的时候为按照shippingGroup分组为一条消息发送
 * 但接收的反馈消息为shipItem行为一条反馈消息.
 * {
 * "CustomerOrderID": "orderId",
 * "ExternalOrderID": "shippingGroupId",
 * "CommandSource": "",
 * "CancelDate": "yyyy/MM/dd HH:mm:ss",
 * "Success": "",
 * "LineInfos": [{ // 只有一行
 * "ExternalLineID": "shipItemRelId",
 * "Success": "ok表示成功其他表示失败",
 * "Msg": ""
 * }]
 * }
 **/
@Data
public class CancelLineResponse extends AbsMessage {

    private String shippingGroupId;

    /**
     * 取消时间 yyyy/MM/dd HH:mm:ss
     */
    private String cancelDate;

    private List<Line> lines;

    public void addLine(String id, String success, String message) {

        if (lines == null) {
            lines = new ArrayList<>();
        }
        Line line = new Line();
        line.setId(id);
        line.setSuccess(success);
        line.setMessage(message);
        lines.add(line);
    }

    public void addLine(String id, String success, String message, String materialCode) {

        if (lines == null) {
            lines = new ArrayList<>();
        }
        Line line = new Line();
        line.setId(id);
        line.setSuccess(success);
        line.setMessage(message);
        line.setMaterialCode(materialCode);
        lines.add(line);
    }

    @Data
    public static class Line {

        /**
         * shipItemRel id
         */
        private String id;

        /**
         * ok表示成功其他表示失败
         */
        private String success;

        private String message;

        private String materialCode;

    }

}
