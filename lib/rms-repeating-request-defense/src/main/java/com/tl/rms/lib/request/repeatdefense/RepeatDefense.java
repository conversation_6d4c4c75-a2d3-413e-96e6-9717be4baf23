package com.tl.rms.lib.request.repeatdefense;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 防重复提交，用在controller方法上
 *
 * <AUTHOR>
 * @date 9/11/2019 6:53 PM
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RepeatDefense {

    /**
     * 间隔时间(单位毫秒)
     */
    int interval() default 1500;

    String message() default "请不要重复操作";
}
