package com.tl.rms.lib.request.repeatdefense;

import com.tl.rms.lib.beans.web.restful.ResponseMessage;
import com.tl.rms.lib.redis.util.RedisLock;
import com.tl.rms.util.json.JsonProcessUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Optional;

/**
 * 防重复提交
 *
 * <AUTHOR>
 * @date 9/11/2019 6:55 PM
 */
@Aspect
@Component
@Slf4j
public class RepeatDefenseAspect {

    private static String PREFIX = "repeatDefense:";

    @Autowired
    private RedisTemplate redisTemplate;

    @Around("@annotation(repeatDefense)")
    public Object around(ProceedingJoinPoint pjp, RepeatDefense repeatDefense) throws Throwable {

        if (redisTemplate == null) {
            log.warn("redisTemplate is null");
            return pjp.proceed();
        }

        if (RequestContextHolder.getRequestAttributes() == null) {
            log.warn("request is null");
            return pjp.proceed();
        }

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        if (request == null || request.getSession() == null) {
            log.warn("request or session is null");
            return pjp.proceed();
        }

        String sessionId = request.getSession().getId();
        String method = pjp.getSignature().toString(); //
        //System.out.println("RepeatDefenseAspect: " + method);

        RedisLock lock = null;
        try {
            String key = PREFIX + sessionId + ":" + method;
            lock = new RedisLock(redisTemplate, key, 0, repeatDefense.interval());  // 不主动解锁，等待锁自动释放
            if (lock.tryLock()) {
                Object o = pjp.proceed();
                return o;
            } else {
                HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
                response.setContentType("application/json; charset=UTF-8");
                response.getWriter().write(
                        JsonProcessUtil.beanToJson(ResponseMessage.error(repeatDefense.message())));
                response.getWriter().close();
                return null;
            }
        } catch (Exception e) {
            throw e;
        } finally {
            Optional.ofNullable(lock).ifPresent(RedisLock::unlock);
        }
    }


}