<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.tl</groupId>
        <artifactId>rms</artifactId>
        <version>1.0</version>
    </parent>

    <name>rms-lib</name>
    <groupId>com.tl.rms.lib</groupId>
    <artifactId>rms-lib</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>rms-beans</module>
        <module>rms-common</module>
        <module>rms-discovery-consul</module>
        <module>rms-gateway-util</module>
        <module>rms-logging</module>
        <module>rms-mq-support</module>
        <module>rms-redis</module>
        <module>rms-repeating-request-defense</module>
        <module>rms-session</module>
        <module>rms-useragent</module>
        <module>rms-util</module>
        <module>rms-validate</module>
        <module>rms-wsclient</module>
        <module>rms-fulfillment-basic</module>
        <module>rms-fulfillment-client</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-beans</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-common</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-discovery-consul</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-gateway-util</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-logging</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-mq-support</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-redis</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-repeating-request-defense</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-session</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-useragent</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-util</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-validate</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-wsclient</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-fulfillment-basic</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-fulfillment-client</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.tl.rms.lib</groupId>
                <artifactId>rms-generator-id</artifactId>
                <version>1.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
    </dependencies>

</project>