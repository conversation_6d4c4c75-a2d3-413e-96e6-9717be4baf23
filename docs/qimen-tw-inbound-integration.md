# 奇门退货入库对接TW入库接口实现

## 功能概述

本功能实现了奇门退货入库接口与TW侧入库接口的对接，当收到奇门的退货入库请求时，系统会将参数转换为TW侧的入库参数并直接调用TW入库接口，不再通过消息队列处理。

## 实现架构

```
奇门退货入库请求 -> QiMenController.handleReturnOrderCreate() 
                -> QiMenToTwConverter.convertReturnOrderToTwInbound()
                -> InvokeTwService.invokeTwInboundSyncService()
                -> TW入库接口
```

## 核心组件

### 1. 奇门退货入库VO类

- **QMReturnOrderCreateVo**: 奇门退货入库单创建主请求类
- **QMReturnOrderVo**: 退货入库单主信息
- **QMReturnOrderSenderVo**: 发件人信息
- **QMReturnOrderLinesVo**: 行项目集合
- **QMReturnOrderLineVo**: 行项目详情

### 2. TW入库请求类

- **PtInboundOrderInfo**: TW入库订单信息
- **PtInboundOrderDetailInfo**: TW入库订单明细
- **ArrayOfPtInboundOrderDetailInfo**: 明细数组
- **InboundServicePortType**: 入库服务接口
- **InboundService**: 入库服务类

### 3. 参数转换服务

- **QiMenToTwConverter**: 奇门到TW参数转换服务
  - `convertReturnOrderToTwInbound()`: 核心转换方法

### 4. TW服务调用

- **InvokeTwService**: 增加了 `invokeTwInboundSyncService()` 方法

## 参数映射关系

### 奇门 -> TW 主要字段映射

| 奇门字段 | TW字段 | 说明 |
|---------|--------|------|
| returnOrderCode | CustomerOrderID | 吉客云退货入库单号 |
| returnOrderCode | ExternalOrderID | 吉客云退货入库单号 |
| warehouseCode | WhID | erp仓库名称（需转换） |
| - | OrderType | 固定值"Return" |
| ownerCode | OwnerID | 固定值"太力总部" |
| ownerCode | CustomerID | 制单ou（需转换） |
| senderInfo.name | DeliveryToContact | 退货人名称 |
| senderInfo.mobile | DeliveryToPhone | 退货联系电话 |
| senderInfo.province | DeliveryToProvince | 退货省份 |
| senderInfo.city | DeliveryToCity | 退货城市 |
| senderInfo.detailAddress | DeliveryToAddress | 退货详细地址 |
| preDeliveryOrderCode | Udf01 | 原销售订单号 |
| sourcePlatformCode | Udf05 | 项目名称（需转换） |
| - | Udf08 | 固定值"JKY" |
| - | Source1 | 固定值"QI_MEN" |
| - | Source2 | 固定值"JKY" |
| - | Source3 | 固定值"DIAN_KE_TAI_LI" |

### 明细字段映射

| 奇门字段 | TW字段 | 说明 |
|---------|--------|------|
| itemCode | SkuID | 退货物料编码 |
| itemCode | SkuDescr | 退货物料描述 |
| planQty | Qty | 退货数量 |
| orderLineNo | ExternalLineID | 退货入库单行号 |
| warehouseCode | LotAttr01 | erp仓库名称 |
| sourceOrderCode | Udf01 | 销售单网店订单号 |

## 接口调用流程

1. **接收奇门请求**: QiMenController接收POST请求
2. **签名验证**: 验证奇门请求签名
3. **XML解析**: 使用XmlToBeanUtil解析XML为Java对象
4. **参数转换**: QiMenToTwConverter转换参数格式
5. **调用TW接口**: InvokeTwService直接调用TW入库接口
6. **返回结果**: 根据TW响应返回奇门格式的响应

## 配置说明

### WSDL配置
```java
private static final String INBOUND_SYNC_WSDL = "/TLSI_0049/processes/InboundService?wsdl";
```

### 固定值配置
- 货主名称: "太力总部"
- 订单类型: "Return"
- 来源标识: "QI_MEN", "JKY", "DIAN_KE_TAI_LI"

## 错误处理

1. **参数验证**: 检查必填字段
2. **转换异常**: 捕获参数转换错误
3. **TW调用异常**: 处理TW接口调用失败
4. **响应格式**: 统一返回奇门标准响应格式

## 测试

提供了单元测试类 `QiMenToTwConverterTest` 用于验证参数转换逻辑。

## 使用示例

### 奇门请求示例
```xml
<?xml version="1.0" encoding="utf-8"?>
<request>
<returnOrder>
<returnOrderCode>TLRK202508180001</returnOrderCode>
<ownerCode>OWNER001</ownerCode>
<warehouseCode>WH001</warehouseCode>
<orderType>THRK</orderType>
<preDeliveryOrderCode>TLJY202508080195</preDeliveryOrderCode>
<senderInfo>
<name>冯士军</name>
<mobile>18423472101-2448#</mobile>
<province>黑龙江省</province>
<city>哈尔滨市</city>
<detailAddress>黑龙江省 哈尔滨市 宾县 宾州镇 大千路147号鸿福世纪城2期</detailAddress>
</senderInfo>
</returnOrder>
<orderLines>
<orderLine>
<orderLineNo>1</orderLineNo>
<sourceOrderCode>************</sourceOrderCode>
<itemCode>10019205483170000</itemCode>
<planQty>1</planQty>
</orderLine>
</orderLines>
</request>
```

### TW请求示例
```xml
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ptInboundOrderInfo xmlns="InboundService">
    <WhID>总部深圳联运通仓</WhID>
    <CustomerOrderID>TLRK202508180001</CustomerOrderID>
    <ExternalOrderID>TLRK202508180001</ExternalOrderID>
    <OrderType>Return</OrderType>
    <OwnerID>太力总部</OwnerID>
    <CustomerID>PTTL_OU_06_广州分公司</CustomerID>
    <DeliveryToContact>冯士军</DeliveryToContact>
    <DeliveryToPhone>18423472101-2448#</DeliveryToPhone>
    <DeliveryToProvince>黑龙江省</DeliveryToProvince>
    <DeliveryToCity>哈尔滨市</DeliveryToCity>
    <DeliveryToAddress>黑龙江省 哈尔滨市 宾县 宾州镇 大千路147号鸿福世纪城2期</DeliveryToAddress>
    <Udf01>TLJY202508080195</Udf01>
    <Udf05>线上零售-京东三店</Udf05>
    <Udf08>JKY</Udf08>
    <PtInboundOrderDetailInfos>
        <PtInboundOrderDetailInfo>
            <SkuID>10019205483170000</SkuID>
            <SkuDescr>10019205483170000</SkuDescr>
            <Qty>1</Qty>
            <ExternalLineID>1</ExternalLineID>
            <LotAttr01>总部深圳联运通仓</LotAttr01>
            <Udf01>************</Udf01>
        </PtInboundOrderDetailInfo>
    </PtInboundOrderDetailInfos>
    <Source1>QI_MEN</Source1>
    <Source2>JKY</Source2>
    <Source3>DIAN_KE_TAI_LI</Source3>
</ptInboundOrderInfo>
```

## 注意事项

1. 确保TW入库服务地址配置正确
2. 根据实际业务需求调整字段映射关系
3. 完善错误处理和日志记录
4. 考虑添加重试机制和熔断保护
