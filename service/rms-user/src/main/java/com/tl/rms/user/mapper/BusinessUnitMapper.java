package com.tl.rms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tl.rms.user.domain.po.BusinessUnit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface BusinessUnitMapper extends BaseMapper<BusinessUnit> {

    @Select({
            "select bu.bu_name from t_business_unit bu join t_project p on bu.bu_id = p.bu_id ",
            " where bu.deleted = 0 and p.deleted = 0 and p.id = #{projectId}"
    })
    String getBuNameByProjectId(@Param("projectId") String projectId);

}
