package com.tl.rms.user.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tl.rms.user.domain.po.Warehouse;
import com.tl.rms.user.domain.vo.WarehouseQueryReqVo;
import com.tl.rms.user.domain.vo.WarehouseVo;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface WarehouseService extends IService<Warehouse> {

    Page<WarehouseVo> page(@RequestBody WarehouseQueryReqVo queryVo);

    Warehouse getByName(String name);

    List<WarehouseVo> queryByNames(List<String> names);

    List<WarehouseVo> queryByIds(List<String> ids);

    Boolean sync(WarehouseVo warehouseVo);
}