package com.tl.rms.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tl.rms.user.api.WarehouseClient;
import com.tl.rms.user.domain.converter.WarehouseConverter;
import com.tl.rms.user.domain.po.Warehouse;
import com.tl.rms.user.domain.vo.WarehouseQueryReqVo;
import com.tl.rms.user.domain.vo.WarehouseVo;
import com.tl.rms.user.service.WarehouseService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping
@RequiredArgsConstructor
public class WarehouseController implements WarehouseClient {

    private final WarehouseService warehouseService;

    @Override
    public Page<WarehouseVo> page(@RequestBody WarehouseQueryReqVo queryVo) {
        return warehouseService.page(queryVo);
    }

    @Override
    public List<WarehouseVo> list() {
        LambdaQueryWrapper<Warehouse> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Warehouse::getDeleted, 0);
        List<Warehouse> warehouseList = warehouseService.list(queryWrapper);
        return WarehouseConverter.MAPPER.toVoList(warehouseList);
    }

    @Override
    public WarehouseVo getWarehouseById(String id) {
        Warehouse warehouse = warehouseService.getById(id);
        return WarehouseConverter.MAPPER.toVo(warehouse);
    }

    @Override
    public Warehouse getByName(String name) {
        return warehouseService.getByName(name);
    }

    @Override
    public List<WarehouseVo> queryByNames(@RequestBody List<String> names) {
        return warehouseService.queryByNames(names);
    }

    @Override
    public List<WarehouseVo> queryByIds(@RequestBody List<String> ids) {
        return warehouseService.queryByIds(ids);
    }

    @Override
    public Boolean sync(WarehouseVo warehouseVo) {
        return warehouseService.sync(warehouseVo);
    }
}