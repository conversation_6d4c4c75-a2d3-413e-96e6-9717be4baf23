package com.tl.rms.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.user.domain.po.BusinessUnit;
import com.tl.rms.user.mapper.BusinessUnitMapper;
import com.tl.rms.user.service.BusinessUnitService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BusinessUnitServiceImpl extends ServiceImpl<BusinessUnitMapper, BusinessUnit>
        implements BusinessUnitService {

    private final BusinessUnitMapper businessUnitMapper;

    @Override
    public String getBuNameByProjectId(String projectId) {
        return businessUnitMapper.getBuNameByProjectId(projectId);
    }

}