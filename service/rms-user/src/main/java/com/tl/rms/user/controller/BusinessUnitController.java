package com.tl.rms.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tl.rms.user.api.BusinessUnitClient;
import com.tl.rms.user.domain.converter.BusinessUnitConverter;
import com.tl.rms.user.domain.po.BusinessUnit;
import com.tl.rms.user.domain.vo.BusinessUnitVo;
import com.tl.rms.user.service.BusinessUnitService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping
@RequiredArgsConstructor
public class BusinessUnitController implements BusinessUnitClient {

    private final BusinessUnitService businessUnitService;

    @Override
    public List<BusinessUnitVo> list() {
        LambdaQueryWrapper<BusinessUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinessUnit::getDeleted, 0);
        List<BusinessUnit> businessUnitList = businessUnitService.list(queryWrapper);
        return BusinessUnitConverter.MAPPER.toVoList(businessUnitList);
    }

    @Override
    public String getBuNameByProjectId(String projectId) {
        return businessUnitService.getBuNameByProjectId(projectId);
    }

}