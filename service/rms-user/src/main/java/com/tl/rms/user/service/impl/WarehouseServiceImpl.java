package com.tl.rms.user.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.user.domain.converter.WarehouseConverter;
import com.tl.rms.user.domain.po.Warehouse;
import com.tl.rms.user.domain.vo.WarehouseQueryReqVo;
import com.tl.rms.user.domain.vo.WarehouseVo;
import com.tl.rms.user.mapper.WarehouseMapper;
import com.tl.rms.user.service.WarehouseBranchService;
import com.tl.rms.user.service.WarehouseService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
public class WarehouseServiceImpl extends ServiceImpl<WarehouseMapper, Warehouse> implements WarehouseService {

    private final WarehouseBranchService warehouseBranchService;

    @Override
    public Page<WarehouseVo> page(WarehouseQueryReqVo queryVo) {
        Page<Warehouse> page = super.page(queryVo.toPage(), buildQueryWrapper(queryVo));
        return (Page<WarehouseVo>) page.convert(po -> {
            WarehouseVo warehouseVo = WarehouseConverter.MAPPER.toVo(po);
            warehouseVo.setWarehouseBranchVoList(warehouseBranchService.listByWarehouseId(po.getId()));
            return warehouseVo;
        });
    }

    @Override
    public Warehouse getByName(String name) {
        LambdaQueryWrapper<Warehouse> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Warehouse::getDeleted, 0);
        queryWrapper.eq(Warehouse::getName, name);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<WarehouseVo> queryByNames(List<String> names) {
        return queryWarehouses(names, Warehouse::getName);
    }

    @Override
    public List<WarehouseVo> queryByIds(List<String> ids) {
        return queryWarehouses(ids, Warehouse::getId);
    }

    @Override
    @Transactional
    public Boolean sync(WarehouseVo warehouseVo) {

        Warehouse existingWarehouse = this.getOne(
                new LambdaQueryWrapper<Warehouse>()
                        .eq(Warehouse::getId, warehouseVo.getId()));

        Warehouse warehouse;
        if (existingWarehouse != null) {
            warehouse = existingWarehouse;
            warehouse.setName(warehouseVo.getName());
            warehouse.setOrgCode(warehouseVo.getOrgCode());
            warehouse.setOrgName(warehouseVo.getOrgName());
            warehouse.setWhOrgName(warehouseVo.getWhOrgName());
            warehouse.setContactName(warehouseVo.getContactName());
            warehouse.setContactWay(warehouseVo.getContactWay());
            warehouse.setAddress(warehouseVo.getAddress());
            warehouse.setIsActive(warehouseVo.getIsActive());
            this.updateById(warehouse);
        } else {
            warehouse = new Warehouse();
            warehouse.setId(warehouseVo.getId());
            warehouse.setName(warehouseVo.getName());
            warehouse.setOrgCode(warehouseVo.getOrgCode());
            warehouse.setOrgName(warehouseVo.getOrgName());
            warehouse.setWhOrgName(warehouseVo.getWhOrgName());
            warehouse.setContactName(warehouseVo.getContactName());
            warehouse.setContactWay(warehouseVo.getContactWay());
            warehouse.setAddress(warehouseVo.getAddress());
            warehouse.setIsActive(warehouseVo.getIsActive());
            this.save(warehouse);
        }

        if (CollUtil.isNotEmpty(warehouseVo.getWarehouseBranchVoList())) {
            warehouseBranchService.deleteByWarehouseId(warehouse.getId());
            warehouseBranchService.createWarehouseBranch(warehouseVo.getWarehouseBranchVoList());
        }

        return Boolean.TRUE;
    }

    private LambdaQueryWrapper<Warehouse> buildQueryWrapper(WarehouseQueryReqVo queryVo) {
        return new LambdaQueryWrapper<Warehouse>()
                .like(StringUtils.isNotBlank(queryVo.getId()), Warehouse::getId, queryVo.getId())
                .like(StringUtils.isNotBlank(queryVo.getName()), Warehouse::getName, queryVo.getName())
                .eq(StringUtils.isNotBlank(queryVo.getOrgCode()), Warehouse::getOrgCode, queryVo.getOrgCode())
                .ge(queryVo.getCreateTimeStart() != null, Warehouse::getCreateTime, queryVo.getCreateTimeStart())
                .le(queryVo.getCreateTimeEnd() != null, Warehouse::getCreateTime, queryVo.getCreateTimeEnd());
    }

    private List<WarehouseVo> queryWarehouses(List<String> values, SFunction<Warehouse, ?> function) {
        if (CollectionUtils.isEmpty(values)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<Warehouse> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Warehouse::getDeleted, 0);
        queryWrapper.in(function, values);
        List<Warehouse> warehouseList = this.list(queryWrapper);
        return WarehouseConverter.MAPPER.toVoList(warehouseList);
    }
}