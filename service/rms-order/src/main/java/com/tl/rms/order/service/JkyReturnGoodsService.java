package com.tl.rms.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tl.rms.order.domain.po.JkyReturnGoods;
import com.tl.rms.order.domain.vo.JkyReturnGoodsVO;

import java.util.List;

public interface JkyReturnGoodsService extends IService<JkyReturnGoods> {

    int deleteByTradeAfterIds(List<Long> tradeAfterIds);

    int batchInsert(List<JkyReturnGoodsVO> rows);

}