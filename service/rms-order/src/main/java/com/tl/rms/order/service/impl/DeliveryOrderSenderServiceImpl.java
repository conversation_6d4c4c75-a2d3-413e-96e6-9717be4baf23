package com.tl.rms.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.order.domain.po.DeliveryOrderSender;
import com.tl.rms.order.mapper.DeliveryOrderSenderMapper;
import com.tl.rms.order.service.DeliveryOrderSenderService;
import org.springframework.stereotype.Service;

@Service
public class DeliveryOrderSenderServiceImpl extends ServiceImpl<DeliveryOrderSenderMapper, DeliveryOrderSender> implements DeliveryOrderSenderService {
}