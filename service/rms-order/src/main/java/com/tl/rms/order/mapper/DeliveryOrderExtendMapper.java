package com.tl.rms.order.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tl.rms.order.domain.vo.DeliveryOrderExportVo;
import com.tl.rms.order.domain.vo.DeliveryOrderQueryVo;
import com.tl.rms.order.domain.vo.DeliveryOrderVo;

/**
 * <AUTHOR>
 */
@Mapper
public interface DeliveryOrderExtendMapper {

    Page<DeliveryOrderVo> selectList(Page<DeliveryOrderVo> page, @Param("query") DeliveryOrderQueryVo queryVo);

    Page<DeliveryOrderExportVo> selectExportList(Page<DeliveryOrderExportVo> page, @Param("query") DeliveryOrderQueryVo queryVo);
}
