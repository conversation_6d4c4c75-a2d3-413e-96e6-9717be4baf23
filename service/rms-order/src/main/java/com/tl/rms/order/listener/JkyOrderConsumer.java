package com.tl.rms.order.listener;


import com.rabbitmq.client.Channel;
import com.tl.rms.lib.mq.config.RabbitContainerFactory;
import com.tl.rms.lib.mq.producer.MqProducer;
import com.tl.rms.order.domain.enums.JkyOrderPayTypeEnum;
import com.tl.rms.order.domain.enums.JkyOrderTradeStatusEnum;
import com.tl.rms.order.domain.enums.JkyOrderTradeTypeEnum;
import com.tl.rms.order.domain.vo.JkyOrderGoodsBigDataVO;
import com.tl.rms.order.domain.vo.JkyOrderGoodsPreVO;
import com.tl.rms.order.mapper.JkyOrderMapper;
import com.tl.rms.util.json.JsonProcessUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 吉客云线上订单
 *
 * <AUTHOR>
 * @date 2024/6/24
 */
@Slf4j
@Component
public class JkyOrderConsumer {

    @Autowired
    private JkyOrderMapper orderMapper;

    @Autowired
    private MqProducer mqProducer;

    @Value("${mq.queue.jkyOrderLine}")
    private String jkyOrderLine;

    /**
     * 线上订单对接大数据
     */
    @RabbitListener(queues = "${mq.queue.jkyOrderLinePre}", containerFactory = RabbitContainerFactory.PARALLEL_CONTAINER_FACTORY)
    public void jkyOrderLinePre(Channel channel, Message message) throws Exception {
        byte[] data = message.getBody();
        String content = new String(data);
        log.info("线上订单对接大数据: {}", content);

        try {
            JkyOrderGoodsPreVO preVO = JsonProcessUtil.jsonToBean(data, JkyOrderGoodsPreVO.class);

            if (CollectionUtils.isEmpty(preVO.getTradeNos()) && StringUtils.isEmpty(preVO.getTradeNo())) {
                log.info("线上订单对接大数据，参数不正确");
                return;
            }

            List<JkyOrderGoodsBigDataVO> bigDataVOs = orderMapper.listJkyOrder2BigData(preVO);

            if (CollectionUtils.isEmpty(bigDataVOs)) {
                log.info("线上订单对接大数据，未查询到销售单数据");
                return;
            }

            for (JkyOrderGoodsBigDataVO bigDataVO : bigDataVOs) {
                bigDataVO.setTradeStatusStr(JkyOrderTradeStatusEnum.getDesc(bigDataVO.getTradeStatus()));
                bigDataVO.setTradeTypeStr(JkyOrderTradeTypeEnum.getDesc(bigDataVO.getTradeType()));
                bigDataVO.setPayTypeStr(JkyOrderPayTypeEnum.getDesc(bigDataVO.getPayType()));
                mqProducer.sendMessage(jkyOrderLine, bigDataVO);
            }

        } catch (Exception e) {
            log.error("线上订单对接大数据，异常：{}", e.getMessage(), e);
            throw e;
        }

    }

}
