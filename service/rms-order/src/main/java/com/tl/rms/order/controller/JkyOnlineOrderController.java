package com.tl.rms.order.controller;

import com.tl.rms.order.domain.vo.JkyOnlineOrderExportVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tl.rms.order.api.JkyOnlineOrderClient;
import com.tl.rms.order.domain.vo.JkyOnlineOrderQueryVo;
import com.tl.rms.order.domain.po.JkyOnlineOrder;
import com.tl.rms.order.domain.vo.JkyOnlineOrderDetailVo;
import com.tl.rms.order.domain.vo.JkyOnlineOrderVo;
import com.tl.rms.order.service.JkyOnlineOrderService;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@RequestMapping("/jky/onlineOrder")
@RestController
public class JkyOnlineOrderController implements JkyOnlineOrderClient {

    @Resource
    private JkyOnlineOrderService jkyOnlineOrderService;

    @Override
    public Page<JkyOnlineOrderVo> page(JkyOnlineOrderQueryVo dto) {

        Page<JkyOnlineOrder> page = jkyOnlineOrderService.lambdaQuery()
            .in(CollectionUtils.isNotEmpty(dto.getShopIds()), JkyOnlineOrder::getShopId, dto.getShopIds())
            .in(CollectionUtils.isNotEmpty(dto.getTradeNos()), JkyOnlineOrder::getTradeNo, dto.getTradeNos())
            .eq(StringUtils.isNotBlank(dto.getTradeStatusExplain()), JkyOnlineOrder::getTradeStatusExplain,
                dto.getTradeStatusExplain())
            .eq(StringUtils.isNotBlank(dto.getCurStatusExplain()), JkyOnlineOrder::getCurStatusExplain,
                dto.getCurStatusExplain())
            .eq(StringUtils.isNotBlank(dto.getSynStatusExplain()), JkyOnlineOrder::getSynStatusExplain,
                dto.getSynStatusExplain())
            .eq(StringUtils.isNotBlank(dto.getLogisticName()), JkyOnlineOrder::getLogisticName, dto.getLogisticName())
            .eq(StringUtils.isNotBlank(dto.getLogisticNo()), JkyOnlineOrder::getLogisticNo, dto.getLogisticNo())
            .and(dto.getCreateTimeStart() != null && dto.getCreateTimeEnd() != null,
                s -> s.between(JkyOnlineOrder::getCreateTime, dto.getCreateTimeStart(), dto.getCreateTimeEnd()))
            .and(dto.getPayTimeStart() != null && dto.getPayTimeEnd() != null,
                s -> s.between(JkyOnlineOrder::getPayTime, dto.getPayTimeStart(), dto.getPayTimeEnd()))
            .and(CollectionUtils.isNotEmpty(dto.getSysFlagExplains()), q -> {
                for (String flag : dto.getSysFlagExplains()) {
                    q.or().likeRight(JkyOnlineOrder::getSysFlagExplain, flag);
                }
            }).last("order by gmt_modified desc").page(dto.toPage());

        if (page.getTotal() <= 0) {
            return new Page<>();
        }

        IPage<JkyOnlineOrderVo> convert = page.convert(po -> {
            JkyOnlineOrderVo vo = new JkyOnlineOrderVo();
            BeanUtils.copyProperties(po, vo);
            return vo;
        });
        return (Page<JkyOnlineOrderVo>)convert;
    }

    @Override
    public Page<JkyOnlineOrderExportVo> pageWithGoods(JkyOnlineOrderQueryVo dto) {
        return (Page<JkyOnlineOrderExportVo>)jkyOnlineOrderService.pageWithGoods(dto);
    }

    @Override
    public JkyOnlineOrderDetailVo detail(String tradeNo) {
        return jkyOnlineOrderService.detail(tradeNo);
    }
}
