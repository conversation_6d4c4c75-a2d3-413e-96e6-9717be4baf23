package com.tl.rms.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.order.domain.po.JkyOrderOtherFee;
import com.tl.rms.order.mapper.JkyOrderOtherFeeMapper;
import com.tl.rms.order.service.JkyOrderOtherFeeService;
import org.springframework.stereotype.Service;

@Service
public class JkyOrderOtherFeeServiceImpl extends ServiceImpl<JkyOrderOtherFeeMapper, JkyOrderOtherFee> implements JkyOrderOtherFeeService {

    @Override
    public int deleteByTradeId(Long tradeId) {
        LambdaQueryWrapper<JkyOrderOtherFee> lambdaQueryWrapper = Wrappers.lambdaQuery(JkyOrderOtherFee.class);
        lambdaQueryWrapper.eq(JkyOrderOtherFee::getTradeId, tradeId);
        return baseMapper.delete(lambdaQueryWrapper);
    }

    @Override
    public boolean create(JkyOrderOtherFee otherFee) {
        return super.save(otherFee);
    }

}