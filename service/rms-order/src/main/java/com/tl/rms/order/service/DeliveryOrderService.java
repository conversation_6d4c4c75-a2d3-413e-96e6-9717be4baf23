package com.tl.rms.order.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tl.rms.order.domain.po.DeliveryOrder;
import com.tl.rms.order.domain.vo.*;

public interface DeliveryOrderService extends IService<DeliveryOrder> {

    Page<DeliveryOrderVo> selectPage(DeliveryOrderQueryVo deliveryOrderQueryVo);

    Page<DeliveryOrderExportVo> selectExportPage(DeliveryOrderQueryVo deliveryOrderQueryVo);

    List<DeliveryOrderDetailVo> detail(Long id);

    void create(QMDeliveryOrderCreateVo createVo);

//    void create(XXX xx);
}