package com.tl.rms.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tl.rms.order.domain.po.JkyOrderPurchaseDiscountRate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface JkyOrderPurchaseDiscountRateMapper extends BaseMapper<JkyOrderPurchaseDiscountRate> {

    List<JkyOrderPurchaseDiscountRate> listRate();

    BigDecimal getRateByShopAndModel(@Param("shopCode") String shopCode, @Param("modelName") String modelName);

    BigDecimal getRateByShop(@Param("shopCode") String shopCode);

}