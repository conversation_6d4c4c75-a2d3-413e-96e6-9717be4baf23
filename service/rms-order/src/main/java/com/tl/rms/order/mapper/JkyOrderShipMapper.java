package com.tl.rms.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tl.rms.order.domain.po.JkyOrderShip;
import com.tl.rms.order.domain.po.extend.JkyOrderShipExtend;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface JkyOrderShipMapper extends BaseMapper<JkyOrderShip> {

    @Select("<script>" +
            "SELECT order_id, MIN(sign_time) AS sign_time FROM t_jky_order_ship WHERE order_id IN " +
            "<foreach collection='orderNoList' item='orderNo' open='(' separator=',' close=')'>#{orderNo}</foreach> " +
            "GROUP BY order_id" +
            "</script>")
    List<JkyOrderShip> listEarliestSignTimeGroupByOrderNo(List<String> orderNoList);

    int updateByPrimaryKeySelective(JkyOrderShip row);

    /**
     * 批量插入，id自增
     */
    int insertBatch(List<JkyOrderShip> list);

    List<JkyOrderShipExtend> listByOrderId(String orderId);

    JkyOrderShip selectByOrderAndMaterial(@Param("orderId") String orderId, @Param("materialCode") String materialCode);

    List<String> listOrderIdByOut2Ebs(@Param("offset") int offset, @Param("limit") int limit);

    List<String> listOrderIdBySign2Ebs(@Param("offset") int offset, @Param("limit") int limit);

    List<String> listOrderIdByIn2Ebs(@Param("offset") int offset, @Param("limit") int limit);

    List<JkyOrderShip> listByInboundReturn(@Param("offset") int offset, @Param("limit") int limit);

    int updateCancelLineDoNotSendEbs();

    List<String> listOrderIdByCancelLine2EbsEqual(@Param("offset") int offset, @Param("limit") int limit);

    List<String> listOrderIdByCancelLine2EbsLike(@Param("offset") int offset, @Param("limit") int limit);

    int updateOut2EbsStatus(List<Long> ids);

    int updateSign2EbsStatus(List<Long> ids);

    int updateIn2EbsStatus(List<Long> ids);

    int updateIn2EbsStatus4Return(List<Long> ids);

    int updateCancelLine2EbsStatus(List<Long> ids);

    /**
     * 根据订单ID获取物流行列表
     *
     * @param orderId 销售单号tradeNo
     * @return 物流行列表
     */
    List<JkyOrderShip> listByOriginalOrderId(@Param("orderId") String orderId);

}