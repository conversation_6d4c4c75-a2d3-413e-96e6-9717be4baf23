package com.tl.rms.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tl.rms.order.domain.po.JkyOrderShip;
import com.tl.rms.order.domain.po.extend.JkyOrderShipExtend;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface JkyOrderShipService extends IService<JkyOrderShip> {

    Map<String, LocalDateTime> getEarliestSignTimeMapByOrderNoList(List<String> orderNoList);

    List<JkyOrderShip> listByOriginalOrderId(String orderId);

    List<JkyOrderShipExtend> queryByOrderId(String orderId);

    void saveJkyOrderShip(List<JkyOrderShip> jkyOrderShips);

    void sendJkyOrderShip2BigData(List<JkyOrderShip> jkyOrderShips);

    void updateOut2EbsStatus(List<Long> ids);

    void updateSign2EbsStatus(List<Long> ids);

    void updateIn2EbsStatus(List<Long> ids);

    void updateCancelLine2EbsStatus(List<Long> ids);

}