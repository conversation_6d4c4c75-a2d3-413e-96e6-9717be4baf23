package com.tl.rms.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tl.rms.order.domain.po.JkyReturn;
import com.tl.rms.order.domain.vo.JkyReturnVO;

import java.util.List;

public interface JkyReturnService extends IService<JkyReturn> {

    /**
     * 同步吉客云退换补货单
     *
     * <AUTHOR>
     * @date 2023/6/1
     */
    void syncJkyReturn(List<JkyReturnVO> returnVOs, String userId);

    /**
     * 查询吉客云退换补货单最大审核时间
     *
     * <AUTHOR>
     * @date 2023/6/1
     */
    String queryJkyReturnMaxAuditTime();

}