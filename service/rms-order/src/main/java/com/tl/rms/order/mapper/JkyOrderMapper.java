package com.tl.rms.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tl.rms.order.domain.po.JkyOrder;
import com.tl.rms.order.domain.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface JkyOrderMapper extends BaseMapper<JkyOrder> {

    List<DistributionSalesStatisticsRespVo> distributionSalesStatisticsByGbCode(@Param("requestVo") DistributionSalesStatisticsGbCodeReqVo requestVo);

    List<DistributionSalesStatisticsRespVo> distributionSalesStatisticsByMaterialCode(@Param("requestVo") DistributionSalesStatisticsMaterialCodeReqVo requestVo);

    JkyOrderEbsVO queryJkyOrderEbsVOByTradeId(Long tradeId);

    String queryJkyOrderMaxAuditTime(@Param("type") String type);

    List<String> listTradeNo2Update(@Param("status") List<Integer> status);

    List<String> listTradeNoByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<Long> listJkyOrderNeedSendEbs();

    int batchUpdateStatus(@Param("list") List<JkyOrderSyncVO> list, @Param("user") String user);

    int updateByPrimaryKeySelective(JkyOrder row);

    int updateJkyOrderSendEbsTimeCustBranch(JkyOrderEbsVO order);

    String selectShopCodeByTradeNo(String tradeNo);

    int updateJkyOrderDoNotSendEbs();

    List<JkyOrder> selectJkyOrderByBillTradeNo(String billTradeNo);

    List<JkyOrderGoodsBigDataVO> listJkyOrder2BigData(JkyOrderGoodsPreVO preVO);

    /**
     * 根据售后单号查询原销售单信息
     *
     * @param tradeId 售后单
     * @return JkyOrder
     * <AUTHOR>
     * @date 2025/7/29
     */
    JkyOrder queryJkyOrderByAfterSaleTradeId(Long tradeId);

    /**
     * 根据销售单创建时间范围获取销售单列表
     *
     * @param startTime 销售单创建开始时间范围
     * @param endTime   销售单创建结束时间范围
     * @return 销售单列表
     */
    List<JkyOrder> queryOrderByCreateTime(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 根据销售单号获取销售单
     *
     * @param tradeNo 订单编号
     * @return 销售单
     */
    JkyOrder queryJkyOrderByTradeNo(@Param("tradeNo") String tradeNo);

    /**
     * 获取销售单为退货单的tradeIds
     *
     * @param tradeIds 销售单id
     * @return 销售单为退货单的tradeIds
     */
    List<Long> listTradeIdsByTradeTypeReturn(@Param("tradeIds") List<Long> tradeIds);

    /**
     * 根据售后的退换货单获取原销售单
     *
     * @param sourceAfterNo 售后来源单号
     * @return 原销售单
     */
    JkyOrder queryJkyOrderBySourceAfterNo(@Param("sourceAfterNo") String sourceAfterNo);

    /**
     * 销售单推送ebs异常信息
     *
     * @param tradeId  销售单主键
     * @param errorMsg 异常信息
     * @return {@link int}
     * <AUTHOR>
     * @date 2023/10/23
     */
    int updateJkyOrderSendEbsStatusError(@Param("tradeId") Long tradeId, @Param("errorMsg") String errorMsg);

}