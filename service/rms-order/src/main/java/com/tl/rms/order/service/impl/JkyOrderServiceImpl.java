package com.tl.rms.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.common.model.SortConditionVo;
import com.tl.rms.lib.beans.web.restful.ResponseMessage;
import com.tl.rms.lib.mq.producer.MqProducer;
import com.tl.rms.order.domain.converter.JkyOrderConverter;
import com.tl.rms.order.domain.converter.JkyOrderGoodsConverter;
import com.tl.rms.order.domain.enums.JkyOrderPayTypeEnum;
import com.tl.rms.order.domain.enums.JkyOrderTradeStatusEnum;
import com.tl.rms.order.domain.enums.JkyOrderTradeTypeEnum;
import com.tl.rms.order.domain.po.*;
import com.tl.rms.order.domain.vo.*;
import com.tl.rms.order.mapper.JkyOrderMapper;
import com.tl.rms.order.service.*;
import com.tl.rms.util.BeanUtil;
import com.tl.rms.util.DateUtil;
import com.tl.rms.util.json.JsonProcessUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class JkyOrderServiceImpl extends ServiceImpl<JkyOrderMapper, JkyOrder> implements JkyOrderService {

    private final JkyOrderGoodsService orderGoodsService;

    private final JkyOrderShipService jkyOrderShipService;

    private final JkyOrderGoodsDeliveryService orderGoodsDeliveryService;

    private final JkyOrderGoodsSerialService orderGoodsSerialService;

    private final JkyOrderOtherFeeService orderOtherFeeService;

    private final JkyOrderPayService orderPayService;

    private final JkyOrderPurchaseDiscountRateService jkyOrderPurchaseDiscountRateService;

    private final MqProducer mqProducer;

    @Value("${mq.queue.jkyOrderLine}")
    private String jkyOrderLine;

    @Value("${mq.queue.jkyOrderLinePre}")
    private String jkyOrderLinePre;

    /**
     * 吉客云销售单需要重新获取的状态
     */
    @Value("#{'${jackyun.order.updateStatus}'.split(',')}")
    private List<Integer> jkyOrderUpdateStatus;

    @Value("${spring.profiles.active}")
    private String active;

    /**
     * 吉客云国补金额 正则匹配
     */
    private static final Pattern GOV_SUBSIDY_AMOUNT = Pattern.compile("\"govSubsidyAmount\":(\\d+\\.\\d+)");


    @Override
    public Page<JkyOrderVo> page(JkyOrderQueryReqVo jkyOrderQueryReqVo) {
        Page<JkyOrder> page = super.page(jkyOrderQueryReqVo.toPage(), buildQueryWrapper(jkyOrderQueryReqVo));
        if (page == null || CollUtil.isEmpty(page.getRecords())) {
            return new Page<>();
        }

        List<String> tradeNoList = page.getRecords().stream()
                .map(JkyOrder::getTradeNo)
                .collect(Collectors.toList());
        Map<String, LocalDateTime> earliestSignTimeMap = jkyOrderShipService.getEarliestSignTimeMapByOrderNoList(tradeNoList);

        if (BooleanUtils.isTrue(jkyOrderQueryReqVo.getIsWithGoodsList())) {
            List<Long> tradeIds = page.getRecords().stream()
                    .map(JkyOrder::getTradeId)
                    .collect(Collectors.toList());

            Map<Long, List<JkyOrderGoods>> goodsMap = orderGoodsService.listByTradeIds(tradeIds)
                    .stream()
                    .collect(Collectors.groupingBy(JkyOrderGoods::getTradeId));

            return (Page<JkyOrderVo>) page.convert(po -> {
                JkyOrderVo jkyOrderVo = JkyOrderConverter.MAPPER.toVo(po);
                List<JkyOrderGoods> jkyOrderGoodsList = goodsMap.get(po.getTradeId());
                List<JkyOrderGoodsVo> jkyGoodsVoList = JkyOrderGoodsConverter.MAPPER.toVoList(jkyOrderGoodsList);
                // 国补订单
                if (isGovSubsidyOrder(jkyOrderVo, jkyGoodsVoList)) {
                    setGovSubsidyInfo(jkyOrderVo, jkyGoodsVoList);
                    setGovSubsidyGoodsInfo(jkyOrderVo, jkyGoodsVoList);
                }
                jkyOrderVo.setEarliestSignTime(earliestSignTimeMap.get(po.getTradeNo()));
                jkyOrderVo.setGoodsVoList(jkyGoodsVoList);
                return jkyOrderVo;
            });
        }

        return (Page<JkyOrderVo>) page.convert(po -> {
            JkyOrderVo jkyOrderVo = JkyOrderConverter.MAPPER.toVo(po);
            jkyOrderVo.setEarliestSignTime(earliestSignTimeMap.get(po.getTradeNo()));
            return jkyOrderVo;
        });
    }

    @Override
    public JkyOrderVo getByTradeId(Long tradeId) {
        JkyOrder jkyOrder = super.getById(tradeId);
        if (jkyOrder == null) {
            return null;
        }

        List<String> tradeNoList = CollUtil.newArrayList(jkyOrder.getTradeNo());
        Map<String, LocalDateTime> earliestSignTimeMap = jkyOrderShipService.getEarliestSignTimeMapByOrderNoList(tradeNoList);

        List<JkyOrderGoods> jkyOrderGoodsList = orderGoodsService.getByTradeId(tradeId);
        JkyOrderVo jkyOrderVo = JkyOrderConverter.MAPPER.toVo(jkyOrder);
        List<JkyOrderGoodsVo> jkyGoodsVoList = JkyOrderGoodsConverter.MAPPER.toVoList(jkyOrderGoodsList);
        // 国补订单
        if (isGovSubsidyOrder(jkyOrderVo, jkyGoodsVoList)) {
            setGovSubsidyInfo(jkyOrderVo, jkyGoodsVoList);
            setGovSubsidyGoodsInfo(jkyOrderVo, jkyGoodsVoList);
        }
        jkyOrderVo.setEarliestSignTime(earliestSignTimeMap.get(jkyOrder.getTradeNo()));
        jkyOrderVo.setGoodsVoList(jkyGoodsVoList);
        return jkyOrderVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NOT_SUPPORTED)
    public List<DistributionSalesStatisticsRespVo> distributionSalesStatisticsByGbCode(DistributionSalesStatisticsGbCodeReqVo requestVo) {
        return baseMapper.distributionSalesStatisticsByGbCode(requestVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NOT_SUPPORTED)
    public List<DistributionSalesStatisticsRespVo> distributionSalesStatisticsByMaterialCode(DistributionSalesStatisticsMaterialCodeReqVo requestVo) {
        return baseMapper.distributionSalesStatisticsByMaterialCode(requestVo);
    }

    /**
     * 构建查询条件
     *
     * @param jkyOrderQueryReqVo 查询参数
     * @return 查询条件
     */
    private LambdaQueryWrapper<JkyOrder> buildQueryWrapper(JkyOrderQueryReqVo jkyOrderQueryReqVo) {
        LambdaQueryWrapper<JkyOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollUtil.isNotEmpty(jkyOrderQueryReqVo.getTradeIds()),
                JkyOrder::getTradeId, jkyOrderQueryReqVo.getTradeIds());
        queryWrapper.eq(StringUtils.isNotBlank(jkyOrderQueryReqVo.getChannelCode()),
                JkyOrder::getShopCode, jkyOrderQueryReqVo.getChannelCode());
        queryWrapper.like(StringUtils.isNotBlank(jkyOrderQueryReqVo.getTradeNo()),
                JkyOrder::getTradeNo, jkyOrderQueryReqVo.getTradeNo());
        queryWrapper.like(StringUtils.isNotBlank(jkyOrderQueryReqVo.getOnlineTradeNo()),
                JkyOrder::getOnlineTradeNo, jkyOrderQueryReqVo.getOnlineTradeNo());
        queryWrapper.in(CollUtil.isNotEmpty(jkyOrderQueryReqVo.getTradeStatusList()),
                JkyOrder::getTradeStatus, jkyOrderQueryReqVo.getTradeStatusList());
        queryWrapper.eq(StringUtils.isNotBlank(jkyOrderQueryReqVo.getWarehouseId()),
                JkyOrder::getWarehouseCode, jkyOrderQueryReqVo.getWarehouseId());
        queryWrapper.like(StringUtils.isNotBlank(jkyOrderQueryReqVo.getMainPostId()),
                JkyOrder::getMainPostid, jkyOrderQueryReqVo.getMainPostId());
        queryWrapper.eq(jkyOrderQueryReqVo.getTradeType() != null,
                JkyOrder::getTradeType, jkyOrderQueryReqVo.getTradeType());
        queryWrapper.ge(jkyOrderQueryReqVo.getTradeTimeStart() != null,
                JkyOrder::getTradeTime, jkyOrderQueryReqVo.getTradeTimeStart());
        queryWrapper.le(jkyOrderQueryReqVo.getTradeTimeEnd() != null,
                JkyOrder::getTradeTime, jkyOrderQueryReqVo.getTradeTimeEnd());
        queryWrapper.ge(jkyOrderQueryReqVo.getConsignTimeStart() != null,
                JkyOrder::getConsignTime, jkyOrderQueryReqVo.getConsignTimeStart());
        queryWrapper.le(jkyOrderQueryReqVo.getConsignTimeEnd() != null,
                JkyOrder::getConsignTime, jkyOrderQueryReqVo.getConsignTimeEnd());
        if (CollUtil.isNotEmpty(jkyOrderQueryReqVo.getFlagNames())) {
            queryWrapper.and(wrapper -> {
                for (String flagName : jkyOrderQueryReqVo.getFlagNames()) {
                    wrapper.or().like(JkyOrder::getFlagNames, flagName);
                }
            });
        }
        queryWrapper.like(StringUtils.isNotBlank(jkyOrderQueryReqVo.getGoodsList()),
                JkyOrder::getGoodslist, jkyOrderQueryReqVo.getGoodsList());

        // 数据权限开始
        if (CollUtil.isNotEmpty(jkyOrderQueryReqVo.getDataPermissionChannelCodeList())) {
            queryWrapper.in(JkyOrder::getShopCode, jkyOrderQueryReqVo.getDataPermissionChannelCodeList());
        }
        // 数据权限结束

        if (CollUtil.isNotEmpty(jkyOrderQueryReqVo.getSortConditions())) {
            Map<String, SFunction<JkyOrder, ?>> sortFieldMap = new HashMap<>() {{
                // 承诺发货时间
                put("lastShipTime", JkyOrder::getLastShipTime);
                // 下单时间
                put("tradeTime", JkyOrder::getTradeTime);
                // 发货时间
                put("consignTime", JkyOrder::getConsignTime);
                // 付款时间
                put("payTime", JkyOrder::getPayTime);
            }};

            for (SortConditionVo sortCondition : jkyOrderQueryReqVo.getSortConditions()) {
                SFunction<JkyOrder, ?> sortFunction = sortFieldMap.get(sortCondition.getField());
                if (sortFunction != null) {
                    if (BooleanUtils.isTrue(sortCondition.getAsc())) {
                        queryWrapper.orderByAsc(sortFunction);
                    } else {
                        queryWrapper.orderByDesc(sortFunction);
                    }
                }
            }
        } else {
            queryWrapper.orderByDesc(JkyOrder::getTradeTime);
        }

        return queryWrapper;
    }

    /**
     * 是否是国补订单
     *
     * @param orderVo     订单VO对象
     * @param goodsVoList 订单商品列表
     * @return 是否是国补订单
     */
    private boolean isGovSubsidyOrder(JkyOrderVo orderVo, List<JkyOrderGoodsVo> goodsVoList) {
        return StringUtils.isNotBlank(orderVo.getFlagNames())
                && orderVo.getFlagNames().contains("国补订单")
                && orderVo.getGovSubsidyAmount() != null
                && orderVo.getPurchaseDiscountRate() != null
                && CollUtil.isNotEmpty(goodsVoList)
                && goodsVoList.size() == 1
                && goodsVoList.get(0).getShareFavourableAfterFee() != null;
    }

    /**
     * 设置订单国补信息（头表）
     *
     * @param jkyOrderVo  订单VO对象
     * @param goodsVoList 订单商品列表
     */
    private void setGovSubsidyInfo(JkyOrderVo jkyOrderVo, List<JkyOrderGoodsVo> goodsVoList) {
        // 政府承担国补金额(非DB字段) = 【国补金额】-【商家承担国补金额】
        try {
            if (StringUtils.isNotBlank(jkyOrderVo.getGovSubsidyAmount())) {
                BigDecimal govAmount = new BigDecimal(jkyOrderVo.getGovSubsidyAmount());
                BigDecimal merchantAmount = new BigDecimal("0");
                if (StringUtils.isNotBlank(jkyOrderVo.getGovSubsidyAmountMerchant())) {
                    merchantAmount = new BigDecimal(jkyOrderVo.getGovSubsidyAmountMerchant());
                }
                String govSubsidyAmountGovernment = govAmount.subtract(merchantAmount).setScale(2, RoundingMode.HALF_UP).toString();
                jkyOrderVo.setGovSubsidyAmountGovernment(govSubsidyAmountGovernment);
            }
        } catch (NumberFormatException e) {
            log.error("govSubsidyAmountGovernment 转换失败：{}", e.getMessage());
        }

        // 净国补金额(非DB字段) = 【政府承担国补金额】* (1-【采购折扣点位(%)】)
        try {
            if (StringUtils.isNotBlank(jkyOrderVo.getGovSubsidyAmountGovernment()) && jkyOrderVo.getPurchaseDiscountRate() != null) {
                BigDecimal govAmount = new BigDecimal(jkyOrderVo.getGovSubsidyAmountGovernment());
                BigDecimal purchaseDiscountRate = jkyOrderVo.getPurchaseDiscountRate().divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);
                String netGovSubsidyAmount = govAmount.multiply(BigDecimal.ONE.subtract(purchaseDiscountRate)).setScale(2, RoundingMode.HALF_UP).toString();
                jkyOrderVo.setNetGovSubsidyAmount(netGovSubsidyAmount);
            }
        } catch (NumberFormatException e) {
            log.error("netGovSubsidyAmount 转换失败：{}", e.getMessage());
        }

        // 采购折扣点位费用(非DB字段) = (【分摊后金额(行)】-【商家承担国补金额-抖音】) *【采购折扣点位(%)】
        try {
            if (null != jkyOrderVo.getPurchaseDiscountRate() && CollectionUtils.isNotEmpty(goodsVoList)) {
                if (goodsVoList.size() == 1) {
                    BigDecimal merchantAmount = new BigDecimal("0");
                    if (StringUtils.isNotBlank(jkyOrderVo.getGovSubsidyAmountMerchant())) {
                        merchantAmount = new BigDecimal(jkyOrderVo.getGovSubsidyAmountMerchant());
                    }
                    BigDecimal favourableAfterFee = goodsVoList.get(0).getShareFavourableAfterFee().abs();
                    BigDecimal purchaseDiscountRate = jkyOrderVo.getPurchaseDiscountRate().divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);
                    String purchaseDiscountFee = favourableAfterFee.subtract(merchantAmount).multiply(purchaseDiscountRate).setScale(2, RoundingMode.HALF_UP).toString();
                    jkyOrderVo.setPurchaseDiscountFee(purchaseDiscountFee);
                } else {
                    log.warn("计算采购折扣费时，订单出现多行商品，无法计算，订单号：{}", jkyOrderVo.getTradeNo());
                }
            }
        } catch (NumberFormatException e) {
            log.error("purchaseDiscountFee转换失败：{}", e.getMessage());
        }
    }

    /**
     * 设置商品行国补信息
     *
     * @param jkyOrderVo  订单VO
     * @param goodsVoList 商品行列表
     */
    private void setGovSubsidyGoodsInfo(JkyOrderVo jkyOrderVo, List<JkyOrderGoodsVo> goodsVoList) {
        if (CollectionUtils.isEmpty(goodsVoList)) {
            log.warn("商品行为空，无法设置国补信息，订单号: {}", jkyOrderVo.getTradeNo());
            return;
        }

        try {
            JkyOrderGoodsVo goodsVo = goodsVoList.get(0);

            // 验证销售数量
            if (goodsVo.getSellCount() == null || BigDecimal.ZERO.compareTo(goodsVo.getSellCount()) == 0) {
                log.warn("商品销售数量无效，订单号: {}, 数量: {}", jkyOrderVo.getTradeNo(), goodsVo.getSellCount());
                return;
            }
            BigDecimal sellCount = goodsVo.getSellCount().abs();

            BigDecimal shareAfterFee = goodsVo.getShareFavourableAfterFee().abs();
            BigDecimal merchantSubsidy = new BigDecimal("0");
            if (StringUtils.isNotBlank(jkyOrderVo.getGovSubsidyAmountMerchant())) {
                merchantSubsidy = new BigDecimal(jkyOrderVo.getGovSubsidyAmountMerchant());
            }
            BigDecimal purchaseDiscountFee = new BigDecimal(jkyOrderVo.getPurchaseDiscountFee());

            // 净单价 = (分摊后金额 - 商家国补金额 - 采购折扣费) / 数量
            BigDecimal netUnitPrice = shareAfterFee
                    .subtract(merchantSubsidy)
                    .subtract(purchaseDiscountFee)
                    .divide(sellCount, 2, RoundingMode.HALF_UP);
            goodsVo.setNetUnitPrice(netUnitPrice);

            // 订单行净应收 = 净单价 * 数量
            BigDecimal netReceivableAmount = netUnitPrice.multiply(sellCount)
                    .setScale(2, RoundingMode.HALF_UP);
            goodsVo.setNetReceivableAmount(netReceivableAmount);

            goodsVoList.set(0, goodsVo);
        } catch (Exception e) {
            log.error("设置国补商品信息异常，订单号: {}, 错误: {}", jkyOrderVo.getTradeNo(), e.getMessage(), e);
        }
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncJkyOrder(List<JkyOrderSyncVO> orderVOs, String userId) {

        long time = System.currentTimeMillis();

        List<JkyOrderPurchaseDiscountRate> rateList = jkyOrderPurchaseDiscountRateService.listRate();
        Map<String, BigDecimal> rateMap = rateList.stream().collect(Collectors.toMap(k -> k.getShopCode(), k -> k.getRate()));
        log.info("rateMap: {}", JsonProcessUtil.beanToJson(rateMap));

        for (JkyOrderSyncVO orderVO : orderVOs) {
            // gov_subsidy 字段类型是json，需将空字符串处理为null
            if ("".equals(orderVO.getGovSubsidy())) {
                orderVO.setGovSubsidy(null);
            }
            JkyOrder order = new JkyOrder();
            BeanUtil.copy(orderVO, order);

            order.setModifiedBy(userId);
            if (needSendEbs(order.getTradeStatus(), order.getTradeType())) {
                order.setSendEbsStatus(2);
            } else {
                order.setSendEbsStatus(0);
            }

            limitColumnLength(order);

            if (StringUtils.isNotEmpty(orderVO.getFlagNames()) && orderVO.getFlagNames().contains("国补订单.")) {
                // 国补金额
                setGovSubsidyAmount(orderVO, order);
                // 国补订单 采购折扣点位
                setRate(orderVO, order, rateMap);
            }

            // 先按照 tradeId 查询是否存在，因为要更新发送EBS时间，所以不能删除
            JkyOrder oldOrder = this.getById(order.getTradeId());
            // 售后单国补金额
            setGovSubsidyAmountAfterSale(order, oldOrder);
            // 净应收金额
            setEbsAmount(orderVO, order);
            if (oldOrder == null) {
                order.setCreateDate(Integer.valueOf(DateUtil.dateToStr(new Date(), "yyyyMMdd")));
                order.setCreatedBy(userId);
                save(order);
            } else {
                // 之前发送过EBS，若单号一样，无需再次发送
                if (order.getSendEbsStatus() == 2 && oldOrder.getSendEbsStatus() == 1
                        && order.getTradeNo().equals(oldOrder.getTradeNo())) {
                    order.setSendEbsStatus(1);
                }
                if (!order.getTradeNo().equals(oldOrder.getTradeNo())) {
                    sendOriginalOrder2BigData(oldOrder.getTradeNo());
                }
                order.setModifyTime(LocalDateTime.now());
                baseMapper.updateByPrimaryKeySelective(order);
            }

            // 插入前，先删除
            List<JkyOrderGoodsSyncVO> goodsDetails = orderVO.getGoodsDetail();
            if (CollectionUtils.isNotEmpty(goodsDetails)) {
                List<Long> subTradeIds = orderGoodsService.listSubTradeIdByTradeId(order.getTradeId());
                orderGoodsService.deleteByTradeId(order.getTradeId());
                orderGoodsDeliveryService.deleteByTradeId(order.getTradeId());
                for (JkyOrderGoodsSyncVO goodsVO : goodsDetails) {
                    JkyOrderGoods goods = new JkyOrderGoods();
                    BeanUtil.copy(goodsVO, goods);
                    // 接口文档中有tradeId，为避免实际数据中tradeId不存在，特此赋值
                    if (null == goods.getTradeId()) {
                        log.info("goods: {}，不存在tradeId", goods.getSubTradeId());
                        goods.setTradeId(order.getTradeId());
                    }
                    goods.setCreatedBy(userId);
                    goods.setModifiedBy(userId);
                    orderGoodsService.create(goods);

                    if (CollectionUtils.isNotEmpty(subTradeIds) && subTradeIds.contains(goods.getSubTradeId())) {
                        subTradeIds.remove(goods.getSubTradeId());
                    }

                    List<JkyOrderGoodsDelivery> goodsDeliveries = goodsVO.getGoodsDelivery();
                    if (CollectionUtils.isNotEmpty(goodsDeliveries)) {
                        for (JkyOrderGoodsDelivery goodsDelivery : goodsDeliveries) {
                            goodsDelivery.setCreatedBy(userId);
                            goodsDelivery.setModifiedBy(userId);
                            // 接口文档中有tradeId，为避免实际数据中tradeId不存在，特此赋值
                            if (null == goodsDelivery.getTradeId()) {
                                log.info("subTradeId: {}，不存在tradeId", goodsDelivery.getSubTradeId());
                                goodsDelivery.setTradeId(order.getTradeId());
                            }
                            orderGoodsDeliveryService.create(goodsDelivery);
                        }
                    }
                }

                sendDeleteLine2BigData(subTradeIds, order.getTradeNo());
            }

            List<JkyOrderPay> orderPays = orderVO.getTradeOrderPayList();
            if (CollectionUtils.isNotEmpty(orderPays)) {
                orderPayService.deleteByTradeId(order.getTradeId());
                for (JkyOrderPay orderPay : orderPays) {
                    orderPay.setCreatedBy(userId);
                    orderPay.setModifiedBy(userId);
                    // 接口文档中有tradeId，为避免实际数据中tradeId不存在，特此赋值
                    if (null == orderPay.getTradeId()) {
                        log.info("orderPay: {}，不存在tradeId", orderPay.getPayNo());
                        orderPay.setTradeId(order.getTradeId());
                    }
                    orderPayService.create(orderPay);
                }
            }

            List<JkyOrderGoodsSerial> goodsSerials = orderVO.getGoodsSerials();
            if (CollectionUtils.isNotEmpty(goodsSerials)) {
                orderGoodsSerialService.deleteByTradeId(order.getTradeId());
                for (JkyOrderGoodsSerial goodsSerial : goodsSerials) {
                    goodsSerial.setTradeId(order.getTradeId());
                    goodsSerial.setCreatedBy(userId);
                    goodsSerial.setModifiedBy(userId);
                    orderGoodsSerialService.create(goodsSerial);
                }
            }

            List<JkyOrderOtherFee> otherFees = orderVO.getOtherPaymentFees();
            if (CollectionUtils.isNotEmpty(otherFees)) {
                orderOtherFeeService.deleteByTradeId(order.getTradeId());
                for (JkyOrderOtherFee otherFee : otherFees) {
                    otherFee.setTradeId(order.getTradeId());
                    otherFee.setCreatedBy(userId);
                    otherFee.setModifiedBy(userId);
                    orderOtherFeeService.create(otherFee);
                }
            }

            // 发送大数据
//            sendJkyOrder2BigData(orderVO);
        }

        log.info("syncJkyOrder cost: {}ms", System.currentTimeMillis() - time);

    }

    /**
     * 销售单号发生变更，则把原销售单行号数据按照“已取消-驳回审核”发送给大数据
     *
     * @param tradeNo tradeNo
     * <AUTHOR>
     * @date 2024/7/15 19:12
     */
    private void sendOriginalOrder2BigData(String tradeNo) {

        JkyOrderGoodsPreVO preVO = new JkyOrderGoodsPreVO();
        preVO.setTradeNo(tradeNo);
        List<JkyOrderGoodsBigDataVO> bigDataVOs = baseMapper.listJkyOrder2BigData(preVO);
        if (CollectionUtils.isEmpty(bigDataVOs)) {
            log.info("sendOriginalOrder2BigData no data {}", tradeNo);
            return;
        }

        for (JkyOrderGoodsBigDataVO bigDataVO : bigDataVOs) {
            bigDataVO.setTradeStatus(JkyOrderTradeStatusEnum.STATUS_10000.getStatus());
            bigDataVO.setTradeStatusStr(JkyOrderTradeStatusEnum.STATUS_10000.getDesc());
            bigDataVO.setTradeTypeStr(JkyOrderTradeTypeEnum.getDesc(bigDataVO.getTradeType()));
            bigDataVO.setPayTypeStr(JkyOrderPayTypeEnum.getDesc(bigDataVO.getPayType()));
            log.info("sendOriginalOrder2BigData: {}", JsonProcessUtil.beanToJson(bigDataVO));
            mqProducer.sendMessage(jkyOrderLine, bigDataVO);
        }
    }

    /**
     * 将销售单中已删除的行明细，按照“已取消-驳回审核”发送给大数据
     *
     * @param lineIds lineIds
     * @param tradeNo tradeNo
     * <AUTHOR>
     * @date 2024/12/5
     */
    private void sendDeleteLine2BigData(List<Long> lineIds, String tradeNo) {

        if (CollectionUtils.isEmpty(lineIds)) {
            return;
        }

        for (Long lineId : lineIds) {
            JkyOrderGoodsBigDataVO bigDataVO = new JkyOrderGoodsBigDataVO();
            bigDataVO.setLineId(lineId);
            bigDataVO.setTradeNo(tradeNo);
            bigDataVO.setTradeStatus(JkyOrderTradeStatusEnum.STATUS_10000.getStatus());
            bigDataVO.setTradeStatusStr(JkyOrderTradeStatusEnum.STATUS_10000.getDesc());
            log.info("sendDeleteLine2BigData: {}", JsonProcessUtil.beanToJson(bigDataVO));
            mqProducer.sendMessage(jkyOrderLine, bigDataVO);
        }
    }

    /**
     * 限制字段长度
     *
     * @param order 吉客云销售单
     * <AUTHOR>
     * @date 2024/9/3 15:21
     */
    private void limitColumnLength(JkyOrder order) {

        if (StringUtils.isNotEmpty(order.getSellerMemo()) && order.getSellerMemo().length() > 2048) {
            order.setSellerMemo(order.getSellerMemo().substring(0, 2048));
        }

        if (StringUtils.isNotEmpty(order.getBuyerMemo()) && order.getBuyerMemo().length() > 2048) {
            order.setBuyerMemo(order.getBuyerMemo().substring(0, 2048));
        }

        if (StringUtils.isNotEmpty(order.getAppendMemo()) && order.getAppendMemo().length() > 2048) {
            order.setAppendMemo(order.getAppendMemo().substring(0, 2048));
        }

        if (StringUtils.isNotEmpty(order.getPayerName()) && order.getPayerName().length() > 200) {
            order.setPayerName(order.getPayerName().substring(0, 200));
        }

    }

    /**
     * 设置国补金额
     * 因为业务配置规则，生产环境用customizeTradeColumn5，测试环境用customizeTradeColumn1
     *
     * @param orderVO orderVO
     * @param order   order
     * <AUTHOR>
     * @date 2025/4/1
     */
    private void setGovSubsidyAmount(JkyOrderSyncVO orderVO, JkyOrder order) {

        JkyOrderExtendVO extendVO = orderVO.getTradeOrderColumnExt();
        if (extendVO == null) {
            return;
        }
        log.info("JkyOrderExtendVO: {}", JsonProcessUtil.beanToJson(extendVO));
        // 商家承担国补金额-抖音
        if (StringUtils.isNotEmpty(extendVO.getCustomizeTradeColumn14())) {
            order.setGovSubsidyAmountMerchant(extendVO.getCustomizeTradeColumn14());
        }
        // 销售公司-抖音
        if (StringUtils.isNotEmpty(extendVO.getCustomizeTradeColumn11())) {
            order.setConsignCode(extendVO.getCustomizeTradeColumn11());
        }
        // 公司主体-天猫
        if (StringUtils.isNotEmpty(extendVO.getCustomizeTradeColumn12())) {
            order.setConsignCode(extendVO.getCustomizeTradeColumn12());
        }
        // 国补金额-多多
        if (StringUtils.isNotEmpty(extendVO.getCustomizeTradeColumn7())) {
            order.setGovSubsidyAmount(extendVO.getCustomizeTradeColumn7());
            return;
        }
        // 国补金额-抖音
        if (StringUtils.isNotEmpty(extendVO.getCustomizeTradeColumn8())) {
            order.setGovSubsidyAmount(extendVO.getCustomizeTradeColumn8());
            return;
        }
        // 国补金额-天猫
        if (StringUtils.isNotEmpty(extendVO.getCustomizeTradeColumn9())) {
            order.setGovSubsidyAmount(extendVO.getCustomizeTradeColumn9());
            return;
        }
        // 国补金额-京东
        if (StringUtils.isNotEmpty(extendVO.getCustomizeTradeColumn10())) {
            order.setGovSubsidyAmount(extendVO.getCustomizeTradeColumn10());
            return;
        }

        String column5 = extendVO.getCustomizeTradeColumn5();
        if (StringUtils.isEmpty(column5)) {
            log.warn("tradeOrderColumnExt: {}", JsonProcessUtil.beanToJson(extendVO));
            return;
        }
        Matcher matcher = GOV_SUBSIDY_AMOUNT.matcher(column5);
        if (matcher.find()) {
            order.setGovSubsidyAmount(matcher.group(1));
        } else {
            log.warn("未匹配到govSubsidyAmount: {} {}", orderVO.getTradeNo(), extendVO.getCustomizeTradeColumn5());
        }

        /*if ("prodk8s".equals(active)) {
            String column5 = extendVO.getCustomizeTradeColumn5();
            if (StringUtils.isEmpty(column5)) {
                log.warn("tradeOrderColumnExt: {}", JsonProcessUtil.beanToJson(extendVO));
                return;
            }
            Matcher matcher = GOV_SUBSIDY_AMOUNT.matcher(column5);
            if (matcher.find()) {
                order.setGovSubsidyAmount(matcher.group(1));
            } else {
                log.warn("未匹配到govSubsidyAmount: {} {}", orderVO.getTradeNo(), extendVO.getCustomizeTradeColumn5());
            }
        } else {
            order.setGovSubsidyAmount(extendVO.getCustomizeTradeColumn1());
        }*/
    }

    /**
     * 设置国补金额
     * 售后单的国补金额、商家承担国补为空的话，则尝试从原销售单上获取
     *
     * @param order    销售单
     * @param oldOrder 旧销售单
     * <AUTHOR>
     * @date 2025/7/29
     */
    private void setGovSubsidyAmountAfterSale(JkyOrder order, JkyOrder oldOrder) {
        // 非国补、非售后、售后有国补金额，无需处理
        if (StringUtils.isEmpty(order.getFlagNames()) || !order.getFlagNames().contains("国补订单.")
                || order.getTradeType() != 8 || StringUtils.isNotEmpty(order.getGovSubsidyAmount())) {
            return;
        }
        // 售后单更新，旧售后单有国补金额，则直接赋值
        if (oldOrder != null && StringUtils.isNotEmpty(oldOrder.getGovSubsidyAmount())) {
            order.setGovSubsidyAmount(oldOrder.getGovSubsidyAmount());
            order.setGovSubsidyAmountMerchant(oldOrder.getGovSubsidyAmountMerchant());
            return;
        }

        JkyOrder originOrder = baseMapper.queryJkyOrderBySourceAfterNo(order.getSourceAfterNo());
        if (originOrder != null) {
            order.setPurchaseDiscountRate(originOrder.getPurchaseDiscountRate());
            order.setGovSubsidyAmount(originOrder.getGovSubsidyAmount());
            order.setGovSubsidyAmountMerchant(originOrder.getGovSubsidyAmountMerchant());
        }

    }

    /**
     * 设置国补订单的采购折扣点位信息
     *
     * @param orderVO orderVO
     * @param order   order
     * <AUTHOR>
     * @date 2025/7/18
     */
    private void setRate(JkyOrderSyncVO orderVO, JkyOrder order, Map<String, BigDecimal> rateMap) {
        // 国补订单只有1行货品明细数据，且数量是1
        if (CollectionUtils.isEmpty(orderVO.getGoodsDetail())) {
            return;
        }
        String key = null;
        for (JkyOrderGoodsSyncVO goodsVO : orderVO.getGoodsDetail()) {
            // 只处理分摊后金额不等于零的
            if (BigDecimal.ZERO.compareTo(goodsVO.getShareFavourableAfterFee()) != 0) {
                key = orderVO.getShopCode() + goodsVO.getGoodsName();
                break;
            }
        }
        BigDecimal rate = rateMap.get(key);
        if (rate == null) {
            rate = rateMap.get(orderVO.getShopCode());
        }
        if (rate == null) {
            return;
        }

        order.setPurchaseDiscountRate(rate);
    }

    /**
     * 计算订单的净应收金额
     *
     * @param orderVO orderVO
     * @param order   order
     * <AUTHOR>
     * @date 2025/7/21
     */
    private void setEbsAmount(JkyOrderSyncVO orderVO, JkyOrder order) {

        List<JkyOrderGoodsSyncVO> goodsDetails = orderVO.getGoodsDetail();
        if (CollectionUtils.isEmpty(goodsDetails)) {
            return;
        }
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (JkyOrderGoodsSyncVO goodsVO : goodsDetails) {
            BigDecimal unitPrice = goodsVO.getShareFavourableAfterFee().divide(goodsVO.getSellCount(), 2, RoundingMode.HALF_UP);
            totalAmount = totalAmount.add(goodsVO.getSellCount().multiply(unitPrice).setScale(2, RoundingMode.HALF_UP));
        }
        // 净应收金额 = (总金额 - 商家承担国补金额) * (1 - 点位)
        BigDecimal govSubsidyAmountMerchant = BigDecimal.ZERO;
        if (StringUtils.isNotEmpty(order.getGovSubsidyAmountMerchant())) {
            govSubsidyAmountMerchant = new BigDecimal(order.getGovSubsidyAmountMerchant());
        }

        BigDecimal discountFactor;
        BigDecimal purchaseDiscountRate = order.getPurchaseDiscountRate();
        if (purchaseDiscountRate == null) {
            discountFactor = BigDecimal.ONE;
        } else {
            discountFactor = BigDecimal.ONE.subtract(purchaseDiscountRate.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
        }

        // 总金额大于零
        if (BigDecimal.ZERO.compareTo(totalAmount) < 0) {
            totalAmount = totalAmount.subtract(govSubsidyAmountMerchant);
        } else if (BigDecimal.ZERO.compareTo(totalAmount) > 0) {
            totalAmount = totalAmount.add(govSubsidyAmountMerchant);
        }

        // 净应收金额
        order.setEbsAmount(totalAmount.multiply(discountFactor).setScale(2, RoundingMode.HALF_UP).toString());
    }

    /**
     * 经能效平台分析 ,未被调用的方法. 28531cd1b4d37c0a9428ca536f15b28c
     * 扫描时间: 2024-10-30 16:19:28
     */
    @Override
    public void syncJkyOrderStatus(List<JkyOrderSyncVO> orderVOs, String userId) {

        long time = System.currentTimeMillis();
        baseMapper.batchUpdateStatus(orderVOs, userId);

        // 发送大数据
        JkyOrderGoodsPreVO preVO = new JkyOrderGoodsPreVO();
        preVO.setTradeNos(orderVOs.stream().map(JkyOrderSyncVO::getTradeNo).toList());
        mqProducer.sendMessage(jkyOrderLinePre, preVO);

        log.info("syncJkyOrderStatus cost: {}ms", System.currentTimeMillis() - time);

    }

    /**
     * 是否需要发送EBS
     * 根据状态和类型判断
     *
     * <AUTHOR>
     * @date 2023/5/30
     */
    private boolean needSendEbs(Integer status, Integer type) {
        return (4112 == status || 4123 == status || 6000 == status || 9090 == status) && (1 == type || 2 == type || 7 == type || 8 == type);
    }

    /**
     * 吉客云销售单发送大数据
     *
     * <AUTHOR>
     * @date 2023/5/30
     */
    private void sendJkyOrder2BigData(JkyOrderSyncVO orderVO) {

        List<JkyOrderGoodsSyncVO> goodsDetails = orderVO.getGoodsDetail();

        if (CollectionUtils.isNotEmpty(goodsDetails)) {
            JkyOrderGoodsBigDataVO bigDataVO = new JkyOrderGoodsBigDataVO();
            BeanUtil.copy(orderVO, bigDataVO);
            bigDataVO.setTradeCount(orderVO.getTradeCount().longValue());
            bigDataVO.setTradeStatusStr(JkyOrderTradeStatusEnum.getDesc(orderVO.getTradeStatus()));
            bigDataVO.setTradeTypeStr(JkyOrderTradeTypeEnum.getDesc(orderVO.getTradeType()));
            bigDataVO.setPayTypeStr(JkyOrderPayTypeEnum.getDesc(orderVO.getPayType()));

            for (JkyOrderGoodsSyncVO goods : goodsDetails) {
                bigDataVO.setLineId(goods.getSubTradeId());
                bigDataVO.setMatCode(goods.getBarcode());
                bigDataVO.setGoodsNo(goods.getGoodsNo());
                bigDataVO.setGoodsName(goods.getGoodsName());
                bigDataVO.setSpecName(goods.getSpecName());
                bigDataVO.setSellCount(goods.getSellCount());
                bigDataVO.setSellPrice(goods.getSellPrice());
                bigDataVO.setDiscountTotal(goods.getDiscountTotal());
                bigDataVO.setIsGift(goods.getIsGift());
                bigDataVO.setShareFavourableAfterFee(goods.getShareFavourableAfterFee());
                bigDataVO.setSellTotal(goods.getSellTotal());
                bigDataVO.setPlatAuthorId(goods.getPlatAuthorId());
                bigDataVO.setPlatAuthorName(goods.getPlatAuthorName());
                mqProducer.sendMessage(jkyOrderLine, bigDataVO);
            }
        }

    }

    @Override
    public String queryJkyOrderMaxAuditTime(String type) {
        return baseMapper.queryJkyOrderMaxAuditTime(type);
    }

    @Override
    public List<String> listTradeNo2Update() {
        return baseMapper.listTradeNo2Update(jkyOrderUpdateStatus);
    }

    @Override
    public List<String> listTradeNoByDate(String startDate, String endDate) {
        return baseMapper.listTradeNoByDate(startDate, endDate);
    }

    @Override
    public List<Long> listJkyOrderNeedSendEbs() {
        List<Long> orderNeedSendEbsList = baseMapper.listJkyOrderNeedSendEbs();
        // 增加校验条件
        return checkReturnConditions(orderNeedSendEbsList);
    }

    /**
     * 售后单，对接给ebs系统前，中台这边做判断：
     * 原销售订单+原销售单的出库和签收都对接给ebs后，在将售后单对接给ebs;
     *
     * @param tradeIds 待检查销售单主键
     * @return {@link List<Long>}
     * <AUTHOR>
     * @date 2023/10/17
     */
    private List<Long> checkReturnConditions(List<Long> tradeIds) {
        if (CollectionUtils.isEmpty(tradeIds)) {
            log.info("checkReturnConditions: not jkyOrderNeedSendEbs");
            return tradeIds;
        }

        // 获取其中为退货的单号(售后单)
        List<Long> tradeTypeReturnTradeIds = baseMapper.listTradeIdsByTradeTypeReturn(tradeIds);
        // 不存在退货单则不校验
        if (CollectionUtils.isEmpty(tradeTypeReturnTradeIds)) {
            log.info("checkReturnConditions: not tradeTypeReturnTradeIds");
            return tradeIds;
        }

        // 校验各个退货单是否符合推送ebs条件
        List<Long> filterReturnJkyOrders = filterReturnJkyOrder(tradeTypeReturnTradeIds);

        // 移除不符合发送ebs的销售退货单
        if (!CollectionUtils.isEmpty(filterReturnJkyOrders)) {
            tradeIds.removeIf(filterReturnJkyOrders::contains);
        }
        return tradeIds;
    }

    /**
     * 过滤退货单
     *
     * @param tradeTypeReturnTradeIds 退货的单号(售后单)
     * @return 不符合发送ebs的订单(售后单)
     */
    private List<Long> filterReturnJkyOrder(List<Long> tradeTypeReturnTradeIds) {

        List<Long> excludeSendEbsList = new ArrayList<>();
        for (Long tradeTypeReturnTradeId : tradeTypeReturnTradeIds) {
            // 根据售后单获取原销售单
            JkyOrder jkyOrder = this.getById(tradeTypeReturnTradeId);
            JkyOrder originalJkyOrder = baseMapper.queryJkyOrderBySourceAfterNo(jkyOrder.getSourceAfterNo());
            if (Objects.isNull(originalJkyOrder)) {
                log.info("filterReturnJkyOrder: unable to obtain the original jkyOrder, returnTradeId: {}, sourceAfterNo: {}",
                        tradeTypeReturnTradeId, jkyOrder.getSourceAfterNo());
                // 查询不到退货单原单 不发送ebs
                excludeSendEbsList.add(tradeTypeReturnTradeId);
                continue;
            }

            // 检查原销售单是否已发送ebs 没有则销售退货单(本单)不发送ebs
            if (originalJkyOrder.getSendEbsStatus() != 1) {
                log.info("filterReturnJkyOrder: original jkyOrder not SendEbsStatus, originalJkyOrder: {}, returnTradeId: {}, sourceAfterNo: {}",
                        originalJkyOrder.getOrderNo(), tradeTypeReturnTradeId, jkyOrder.getSourceAfterNo());
                // 退货单原单没有发送给ebs 所以销售退货单不发送ebs
                excludeSendEbsList.add(tradeTypeReturnTradeId);
                continue;
            }

            // 根据原销售单获取原销售单的物流是否出库和签收 已都对接给ebs后 再将售后单推给ebs
            List<JkyOrderShip> jkyOrderShips = jkyOrderShipService.listByOriginalOrderId(originalJkyOrder.getOrderNo());
            if (CollectionUtils.isEmpty(jkyOrderShips)) {
                log.info("filterReturnJkyOrder: not jkyOrderShips, returnTradeId: {}, sourceAfterNo: {}, originalJkyOrder: {}",
                        tradeTypeReturnTradeId, jkyOrder.getSourceAfterNo(), originalJkyOrder.getOrderNo());
                // 查询不到退货单原单物流行 不发送ebs
                excludeSendEbsList.add(tradeTypeReturnTradeId);
                continue;
            }

            for (JkyOrderShip jkyOrderShip : jkyOrderShips) {
                // 原销售单的出库和签收是否都对接给ebs, 只要有一条没有对接给ebs则不推送ebs
                if (jkyOrderShip.getOut2EbsStatus() != 1 || jkyOrderShip.getSign2EbsStatus() != 1) {
                    log.info("filterReturnJkyOrder: jkyOrderShip not to Ebs, returnTradeId: {}, sourceAfterNo: {}, originalJkyOrder: {}",
                            tradeTypeReturnTradeId, jkyOrder.getSourceAfterNo(), originalJkyOrder.getOrderNo());
                    excludeSendEbsList.add(tradeTypeReturnTradeId);
                    break;
                }
            }
        }

        return excludeSendEbsList;
    }

    @Override
    public JkyOrderEbsVO queryJkyOrderEbsVOByTradeId(Long tradeId) {

        JkyOrderEbsVO orderVO = baseMapper.queryJkyOrderEbsVOByTradeId(tradeId);
        if (orderVO != null) {
            List<JkyOrderGoodsEbsVO> goodsVOList = orderGoodsService.queryJkyOrderGoodsEbsVOByTradeId(tradeId);
            orderVO.setGoodsDetail(goodsVOList);
            // 如果是售后单，且售后单的客户编码为空，则从原销售单上获取客户编码和分公司
            /*if (StringUtils.isNotEmpty(orderVO.getReturnSourceTradeNo()) && StringUtils.isEmpty(orderVO.getCustomerId())) {
                JkyOrder originOrder = orderMapper.queryJkyOrderByTradeNo(orderVO.getReturnSourceTradeNo());
                if (originOrder != null) {
                    orderVO.setCustomerId(originOrder.getCustomerId());
                    orderVO.setBranchName(originOrder.getBranchName());
                }
            }*/
        }

        return orderVO;
    }

    @Override
    public ResponseMessage updateJkyOrderSendEbsTimeCustBranch(JkyOrderEbsVO order) {
        int i = baseMapper.updateJkyOrderSendEbsTimeCustBranch(order);
        if (i > 0) {
            return ResponseMessage.ok();
        } else {
            return ResponseMessage.error("未查询到待更新发送EBS时间的吉客云销售单：" + order.getTradeId());
        }
    }

    @Override
    public ResponseMessage updateJkyOrderSendEbsStatusError(Long tradeId, String errorMsg) {
        int i = baseMapper.updateJkyOrderSendEbsStatusError(tradeId, errorMsg);
        if (i > 0) {
            return ResponseMessage.ok();
        } else {
            return ResponseMessage.error("未查询到待更新发送EBS异常信息的吉客云销售单：" + tradeId);
        }
    }

    @Override
    public int updateJkyOrderDoNotSendEbs() {
        return baseMapper.updateJkyOrderDoNotSendEbs();
    }

    @Override
    public String queryJkyOrderShopCodeByTradeNo(String tradeNo) {
        return baseMapper.selectShopCodeByTradeNo(tradeNo);
    }

    /**
     * 发送销售单信息至大数据MQ
     *
     * @param tradeNos tradeNos
     * <AUTHOR>
     * @date 2024/7/3
     */
    @Override
    public void sendJkyOrder2BigData(List<String> tradeNos) {

        JkyOrderGoodsPreVO preVO = new JkyOrderGoodsPreVO();
        preVO.setTradeNos(tradeNos);
        mqProducer.sendMessage(jkyOrderLinePre, preVO);
    }

    @Override
    public List<JkyOrder> queryJkyOrderByBillTradeNo(String billTradeNo) {
        return baseMapper.selectJkyOrderByBillTradeNo(billTradeNo);
    }

}