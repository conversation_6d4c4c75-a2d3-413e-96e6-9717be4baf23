package com.tl.rms.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.order.domain.po.JkyReturnGoods;
import com.tl.rms.order.domain.vo.JkyReturnGoodsVO;
import com.tl.rms.order.mapper.JkyReturnGoodsMapper;
import com.tl.rms.order.service.JkyReturnGoodsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class JkyReturnGoodsServiceImpl extends ServiceImpl<JkyReturnGoodsMapper, JkyReturnGoods> implements JkyReturnGoodsService {


    @Override
    public int deleteByTradeAfterIds(List<Long> tradeAfterId) {
        return baseMapper.deleteByTradeAfterIds(tradeAfterId);
    }

    @Override
    public int batchInsert(List<JkyReturnGoodsVO> rows) {
        return baseMapper.batchInsert(rows);
    }

}