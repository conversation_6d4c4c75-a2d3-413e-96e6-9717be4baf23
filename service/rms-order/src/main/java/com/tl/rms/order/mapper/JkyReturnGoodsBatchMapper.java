package com.tl.rms.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tl.rms.order.domain.po.JkyReturnGoodsBatch;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface JkyReturnGoodsBatchMapper extends BaseMapper<JkyReturnGoodsBatch> {

    int deleteByTradeAfterIds(List<Long> tradeAfterId);

    int batchInsert(List<JkyReturnGoodsBatch> rows);

}