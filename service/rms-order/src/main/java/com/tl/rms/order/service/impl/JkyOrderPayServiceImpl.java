package com.tl.rms.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.order.domain.po.JkyOrderPay;
import com.tl.rms.order.mapper.JkyOrderPayMapper;
import com.tl.rms.order.service.JkyOrderPayService;
import org.springframework.stereotype.Service;

@Service
public class JkyOrderPayServiceImpl extends ServiceImpl<JkyOrderPayMapper, JkyOrderPay> implements JkyOrderPayService {

    @Override
    public int deleteByTradeId(Long tradeId) {
        LambdaQueryWrapper<JkyOrderPay> lambdaQueryWrapper = Wrappers.lambdaQuery(JkyOrderPay.class);
        lambdaQueryWrapper.eq(JkyOrderPay::getTradeId, tradeId);
        return baseMapper.delete(lambdaQueryWrapper);
    }

    @Override
    public boolean create(JkyOrderPay pay) {
        return super.save(pay);
    }

}