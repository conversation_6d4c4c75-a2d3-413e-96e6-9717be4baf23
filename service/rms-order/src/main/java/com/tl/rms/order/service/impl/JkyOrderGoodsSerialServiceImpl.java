package com.tl.rms.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.order.domain.po.JkyOrderGoodsSerial;
import com.tl.rms.order.mapper.JkyOrderGoodsSerialMapper;
import com.tl.rms.order.service.JkyOrderGoodsSerialService;
import org.springframework.stereotype.Service;

@Service
public class JkyOrderGoodsSerialServiceImpl extends ServiceImpl<JkyOrderGoodsSerialMapper, JkyOrderGoodsSerial> implements JkyOrderGoodsSerialService {

    @Override
    public int deleteByTradeId(Long tradeId) {
        LambdaQueryWrapper<JkyOrderGoodsSerial> lambdaQueryWrapper = Wrappers.lambdaQuery(JkyOrderGoodsSerial.class);
        lambdaQueryWrapper.eq(JkyOrderGoodsSerial::getTradeId, tradeId);
        return baseMapper.delete(lambdaQueryWrapper);
    }

    @Override
    public boolean create(JkyOrderGoodsSerial serial) {
        return super.save(serial);
    }

}