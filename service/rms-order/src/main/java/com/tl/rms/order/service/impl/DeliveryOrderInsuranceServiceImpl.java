package com.tl.rms.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.order.domain.po.DeliveryOrderInsurance;
import com.tl.rms.order.mapper.DeliveryOrderInsuranceMapper;
import com.tl.rms.order.service.DeliveryOrderInsuranceService;
import org.springframework.stereotype.Service;

@Service
public class DeliveryOrderInsuranceServiceImpl extends ServiceImpl<DeliveryOrderInsuranceMapper, DeliveryOrderInsurance> implements DeliveryOrderInsuranceService {
}