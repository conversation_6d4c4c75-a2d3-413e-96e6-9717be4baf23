package com.tl.rms.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tl.rms.order.domain.po.JkyOrderPurchaseDiscountRate;

import java.math.BigDecimal;
import java.util.List;

public interface JkyOrderPurchaseDiscountRateService extends IService<JkyOrderPurchaseDiscountRate> {

    BigDecimal getRateByShopAndModel(String shopCode, String modelName);

    List<JkyOrderPurchaseDiscountRate> listRate();

}