package com.tl.rms.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.order.domain.po.JkyOrderGoodsDelivery;
import com.tl.rms.order.mapper.JkyOrderGoodsDeliveryMapper;
import com.tl.rms.order.service.JkyOrderGoodsDeliveryService;
import org.springframework.stereotype.Service;

@Service
public class JkyOrderGoodsDeliveryServiceImpl extends ServiceImpl<JkyOrderGoodsDeliveryMapper, JkyOrderGoodsDelivery> implements JkyOrderGoodsDeliveryService {

    @Override
    public int deleteByTradeId(Long tradeId) {
        LambdaQueryWrapper<JkyOrderGoodsDelivery> lambdaQueryWrapper = Wrappers.lambdaQuery(JkyOrderGoodsDelivery.class);
        lambdaQueryWrapper.eq(JkyOrderGoodsDelivery::getTradeId, tradeId);
        return baseMapper.delete(lambdaQueryWrapper);
    }

    @Override
    public boolean create(JkyOrderGoodsDelivery delivery) {
        return super.save(delivery);
    }

}