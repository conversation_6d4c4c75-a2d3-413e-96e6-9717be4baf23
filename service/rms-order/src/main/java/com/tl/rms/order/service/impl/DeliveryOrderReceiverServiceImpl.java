package com.tl.rms.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.order.domain.po.DeliveryOrderReceiver;
import com.tl.rms.order.mapper.DeliveryOrderReceiverMapper;
import com.tl.rms.order.service.DeliveryOrderReceiverService;
import org.springframework.stereotype.Service;

@Service
public class DeliveryOrderReceiverServiceImpl extends ServiceImpl<DeliveryOrderReceiverMapper, DeliveryOrderReceiver> implements DeliveryOrderReceiverService {
}