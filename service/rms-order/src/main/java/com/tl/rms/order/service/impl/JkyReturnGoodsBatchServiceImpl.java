package com.tl.rms.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.order.domain.po.JkyReturnGoodsBatch;
import com.tl.rms.order.mapper.JkyReturnGoodsBatchMapper;
import com.tl.rms.order.service.JkyReturnGoodsBatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class JkyReturnGoodsBatchServiceImpl extends ServiceImpl<JkyReturnGoodsBatchMapper, JkyReturnGoodsBatch> implements JkyReturnGoodsBatchService {


    @Override
    public int deleteByTradeAfterIds(List<Long> tradeAfterId) {
        return baseMapper.deleteByTradeAfterIds(tradeAfterId);
    }

    @Override
    public int batchInsert(List<JkyReturnGoodsBatch> rows) {
        return baseMapper.batchInsert(rows);
    }

}