package com.tl.rms.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tl.rms.order.domain.po.JkyReturn;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface JkyReturnMapper extends BaseMapper<JkyReturn> {

    int deleteByPrimaryKeys(List<Long> tradeAfterId);

    int batchInsert(List<JkyReturn> rows);

    String queryJkyReturnMaxAuditTime();

}