package com.tl.rms.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tl.rms.order.domain.po.JkyReturnGoods;
import com.tl.rms.order.domain.vo.JkyReturnGoodsVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface JkyReturnGoodsMapper extends BaseMapper<JkyReturnGoods> {

    int deleteByTradeAfterIds(List<Long> tradeAfterIds);

    int batchInsert(List<JkyReturnGoodsVO> rows);

}