package com.tl.rms.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.order.domain.po.JkyOrderPurchaseDiscountRate;
import com.tl.rms.order.mapper.JkyOrderPurchaseDiscountRateMapper;
import com.tl.rms.order.service.JkyOrderPurchaseDiscountRateService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service
public class JkyOrderPurchaseDiscountRateServiceImpl extends ServiceImpl<JkyOrderPurchaseDiscountRateMapper, JkyOrderPurchaseDiscountRate> implements JkyOrderPurchaseDiscountRateService {

    @Override
    public BigDecimal getRateByShopAndModel(String shopCode, String modelName) {
        BigDecimal rate = baseMapper.getRateByShopAndModel(shopCode, modelName);
        if (rate != null) {
            return rate;
        }
        return baseMapper.getRateByShop(shopCode);
    }

    @Override
    public List<JkyOrderPurchaseDiscountRate> listRate() {
        return baseMapper.listRate();
    }

}