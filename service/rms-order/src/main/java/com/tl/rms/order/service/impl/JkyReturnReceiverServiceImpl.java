package com.tl.rms.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.order.domain.po.JkyReturnReceiver;
import com.tl.rms.order.mapper.JkyReturnReceiverMapper;
import com.tl.rms.order.service.JkyReturnReceiverService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class JkyReturnReceiverServiceImpl extends ServiceImpl<JkyReturnReceiverMapper, JkyReturnReceiver> implements JkyReturnReceiverService {


    @Override
    public int deleteByTradeAfterIds(List<Long> tradeAfterId) {
        return baseMapper.deleteByTradeAfterIds(tradeAfterId);
    }

    @Override
    public int batchInsert(List<JkyReturnReceiver> rows) {
        return baseMapper.batchInsert(rows);
    }

}