package com.tl.rms.order.task;

import com.tl.rms.lib.fulfillment.client.service.FulfillmentMessageSender;
import com.tl.rms.lib.redis.key.RedisKey;
import com.tl.rms.lib.redis.util.RedisLock;
import com.tl.rms.order.domain.po.JkyOrderShip;
import com.tl.rms.order.mapper.JkyOrderShipMapper;
import com.tl.rms.order.service.JkyOrderService;
import com.tl.rms.util.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 对接吉客云定时任务
 *
 * <AUTHOR>
 * @date 2023/5/22
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JkyTask {

    private final RedisTemplate redisTemplate;

    private final FulfillmentMessageSender fulfillmentMessageSender;

    private final JkyOrderService jkyOrderService;

    private final JkyOrderShipMapper jkyOrderShipMapper;

    /**
     * 操作数据库分组大小
     */
    private static final int SQL_SIZE = 1000;

    /**
     * 间隔时间
     */
    private static final long INTERVAL = 3000L;

    /**
     * EBS对接暂停期间
     */
    @Value("#{${jackyun.ebs.suspendPeriod}}")
    private Map<String, String> suspendPeriod;


    /**
     * 吉客云销售单同步EBS
     * 每5分钟执行一次
     *
     * <AUTHOR>
     * @date 2023/5/22
     */
    @Async
    @Scheduled(cron = "10 0/5 * * * ?")
    public void sendJkyOrderEbsTask() {
        log.info("吉客云销售单同步EBS start");

        if (isSuspendPeriod("order")) {
            log.info("销售单对接EBS暂停");
            return;
        }

        long start = System.currentTimeMillis();
        RedisLock lock = null;
        try {
            lock = new RedisLock(redisTemplate, RedisKey.LOCK_JKY_SEND_ORDER_EBS, 0, 1000 * 60 * 3);
            if (lock.tryLock()) {
                log.info("吉客云销售单同步EBS lock");
                List<Long> tradeIds = jkyOrderService.listJkyOrderNeedSendEbs();

                if (CollectionUtils.isNotEmpty(tradeIds)) {
                    for (Long tradeId : tradeIds) {
                        fulfillmentMessageSender.sendJkyOrder2EBS(tradeId);
                    }
                }
                log.info("吉客云销售单同步EBS work done");
                threadSleep(start);
            } else {
                log.info("吉客云销售单同步EBS 锁等待: {}", RedisKey.LOCK_JKY_SEND_ORDER_EBS);
            }
        } catch (Exception e) {
            log.error("吉客云销售单同步EBS，异常：", e);
        } finally {
            if (lock != null) {
                log.info("吉客云销售单同步EBS unlock");
                lock.unlock();
            }
        }
    }


    /**
     * 吉客云订单出库发送EBS
     * 每5分钟执行一次
     */
    @Async
    @Scheduled(cron = "0 1/5 * * * ?")
    public void sendJkyOrderOutbound2EbsTask() {

        if (isSuspendPeriod("out")) {
            log.info("出库对接EBS暂停");
            return;
        }

        long start = System.currentTimeMillis();
        RedisLock lock = null;
        try {
            lock = new RedisLock(redisTemplate, RedisKey.LOCK_JKY_SEND_ORDER_OUT_EBS, 0, 1000 * 60 * 3);
            if (lock.tryLock()) {
                sendJkyOrderOutbound2Ebs();
                threadSleep(start);
            } else {
                log.info("吉客云订单出库发送EBS 锁等待: {}", RedisKey.LOCK_JKY_SEND_ORDER_OUT_EBS);
            }
        } catch (Exception e) {
            log.error("吉客云订单出库发送EBS，异常：", e);
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }

    }

    /**
     * 吉客云订单签收发送EBS
     * 每5分钟执行一次
     */
    @Async
    @Scheduled(cron = "0 2/5 * * * ?")
    public void sendJkyOrderSign2EbsTask() {

        if (isSuspendPeriod("sign")) {
            log.info("签收对接EBS暂停");
            return;
        }

        long start = System.currentTimeMillis();
        RedisLock lock = null;
        try {
            lock = new RedisLock(redisTemplate, RedisKey.LOCK_JKY_SEND_ORDER_SIGN_EBS, 0, 1000 * 60 * 3);
            if (lock.tryLock()) {
                sendJkyOrderSign2Ebs();
                threadSleep(start);
            } else {
                log.info("吉客云订单签收发送EBS 锁等待: {}", RedisKey.LOCK_JKY_SEND_ORDER_SIGN_EBS);
            }
        } catch (Exception e) {
            log.error("吉客云订单签收发送EBS，异常：", e);
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }

    }

    /**
     * 吉客云订单入库发送EBS
     * 每5分钟执行一次
     */
    @Async
    @Scheduled(cron = "0 3/5 * * * ?")
    public void sendJkyOrderInbound2EbsTask() {

        if (isSuspendPeriod("in")) {
            log.info("入库对接EBS暂停");
            return;
        }

        long start = System.currentTimeMillis();
        RedisLock lock = null;
        try {
            lock = new RedisLock(redisTemplate, RedisKey.LOCK_JKY_SEND_ORDER_IN_EBS, 0, 1000 * 60 * 3);
            if (lock.tryLock()) {
                log.info("getLock: {}", RedisKey.LOCK_JKY_SEND_ORDER_IN_EBS);
                sendJkyOrderInbound2Ebs();
                threadSleep(start);
            } else {
                log.info("锁等待：{}", RedisKey.LOCK_JKY_SEND_ORDER_IN_EBS);
            }
        } catch (Exception e) {
            log.error("吉客云订单入库发送EBS，异常：", e);
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }

    }

    /**
     * 吉客云订单行取消发送EBS
     * 每5分钟执行一次
     */
    @Async
    @Scheduled(cron = "0 4/5 * * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void sendJkyCancelLine2EbsTask() {

        long start = System.currentTimeMillis();
        RedisLock lock = null;
        try {
            lock = new RedisLock(redisTemplate, RedisKey.LOCK_JKY_SEND_CANCEL_LINE_EBS, 0, 1000 * 60 * 3);
            if (lock.tryLock()) {
                sendJkyCancelLine2Ebs();
                threadSleep(start);
            } else {
                log.info("吉客云订单行取消发送EBS 锁等待: {}", RedisKey.LOCK_JKY_SEND_CANCEL_LINE_EBS);
            }
        } catch (Exception e) {
            log.error("吉客云订单行取消发送EBS，异常：", e);
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }

    }

    /**
     * 吉客云订单入库生成退款单物流信息
     * 每5分钟执行一次
     */
    @Async
    @Scheduled(cron = "0 0/5 * * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void generateJkyOrderInboundReturnShipTask() {

        long start = System.currentTimeMillis();
        RedisLock lock = null;
        try {
            lock = new RedisLock(redisTemplate, RedisKey.LOCK_JKY_GENERATE_INBOUND_RETURN_SHIP, 0, 1000 * 60 * 3);
            if (lock.tryLock()) {
                generateJkyOrderInboundReturnShip();
                threadSleep(start);
            } else {
                log.info("吉客云订单入库生成退款单物流信息 锁等待: {}", RedisKey.LOCK_JKY_GENERATE_INBOUND_RETURN_SHIP);
            }
        } catch (Exception e) {
            log.error("吉客云订单入库生成退款单物流信息，异常：", e);
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }

    }

    private void sendJkyOrderOutbound2Ebs() {

        int offset = 0;
        List<String> allOrderIds = new ArrayList<>(SQL_SIZE);
        List<String> orderIds;
        do {
            orderIds = jkyOrderShipMapper.listOrderIdByOut2Ebs(offset, SQL_SIZE);
            if (CollectionUtils.isEmpty(orderIds)) {
                //查无数据，退出循环
                break;
            }
            allOrderIds.addAll(orderIds);
            offset += SQL_SIZE;
        } while (orderIds.size() == SQL_SIZE);

        if (CollectionUtils.isNotEmpty(allOrderIds)) {
            for (String orderId : allOrderIds) {
                fulfillmentMessageSender.sendJkyOrderOutbound2Ebs(orderId);
            }
        }

    }

    private void sendJkyOrderSign2Ebs() {

        int offset = 0;
        List<String> allOrderIds = new ArrayList<>(SQL_SIZE);
        List<String> orderIds;
        do {
            orderIds = jkyOrderShipMapper.listOrderIdBySign2Ebs(offset, SQL_SIZE);
            if (CollectionUtils.isEmpty(orderIds)) {
                //查无数据，退出循环
                break;
            }
            allOrderIds.addAll(orderIds);
            offset += SQL_SIZE;
        } while (orderIds.size() == SQL_SIZE);

        if (CollectionUtils.isNotEmpty(allOrderIds)) {
            for (String orderId : allOrderIds) {
                fulfillmentMessageSender.sendJkyOrderSign2Ebs(orderId);
            }
        }

    }

    private void sendJkyOrderInbound2Ebs() {

        int offset = 0;
        List<String> allOrderIds = new ArrayList<>(SQL_SIZE);
        List<String> orderIds;
        do {
            orderIds = jkyOrderShipMapper.listOrderIdByIn2Ebs(offset, SQL_SIZE);
            if (CollectionUtils.isEmpty(orderIds)) {
                //查无数据，退出循环
                break;
            }
            allOrderIds.addAll(orderIds);
            offset += SQL_SIZE;
        } while (orderIds.size() == SQL_SIZE);

        if (CollectionUtils.isNotEmpty(allOrderIds)) {
            for (String orderId : allOrderIds) {
                fulfillmentMessageSender.sendJkyOrderInbound2Ebs(orderId);
            }
        }

    }

    /**
     * 行取消是否发送EBS的逻辑：
     * 1、若订单号存在
     * 1）订单已发送EBS，则可以发送
     * 2）订单未发送EBS，则不发送，并且需要修改订单和订单行的发送状态为0（不发送）
     * 2、若订单号不存在，但存在订单号拼接-01等尾缀的订单
     * 1）订单已发送EBS，则可以发送
     * 2）订单未发送EBS，则不发送
     */
    private void sendJkyCancelLine2Ebs() {

        // 更新状态不发送EBS
        jkyOrderService.updateJkyOrderDoNotSendEbs();
        jkyOrderShipMapper.updateCancelLineDoNotSendEbs();

        int offset = 0;
        List<String> allOrderIds = new ArrayList<>(SQL_SIZE);
        List<String> orderIds;
        do {
            orderIds = jkyOrderShipMapper.listOrderIdByCancelLine2EbsEqual(offset, SQL_SIZE);
            if (CollectionUtils.isEmpty(orderIds)) {
                break;//查无数据，退出循环
            }
            allOrderIds.addAll(orderIds);
            offset += SQL_SIZE;
        } while (orderIds.size() == SQL_SIZE);

        offset = 0;
        do {
            orderIds = jkyOrderShipMapper.listOrderIdByCancelLine2EbsLike(offset, SQL_SIZE);
            if (CollectionUtils.isEmpty(orderIds)) {
                break;//查无数据，退出循环
            }
            allOrderIds.addAll(orderIds);
            offset += SQL_SIZE;
        } while (orderIds.size() == SQL_SIZE);

        if (CollectionUtils.isNotEmpty(allOrderIds)) {
            for (String orderId : allOrderIds) {
                fulfillmentMessageSender.sendJkyCancelLine2Ebs(orderId);
            }
        }

    }

    private void generateJkyOrderInboundReturnShip() {

        int offset = 0;
        List<JkyOrderShip> allJkyOrderShips = new ArrayList<>(SQL_SIZE);
        List<JkyOrderShip> jkyOrderShips;
        do {
            jkyOrderShips = jkyOrderShipMapper.listByInboundReturn(offset, SQL_SIZE);
            if (CollectionUtils.isEmpty(jkyOrderShips)) {
                //查无数据，退出循环
                break;
            }
            allJkyOrderShips.addAll(jkyOrderShips);
            offset += SQL_SIZE;
        } while (jkyOrderShips.size() == SQL_SIZE);

        Date newDate = new Date();
        LocalDateTime now = LocalDateTime.now();
        Integer createDate = Integer.valueOf(DateUtil.dateToStr(newDate, "yyyyMMdd"));
        if (CollectionUtils.isNotEmpty(allJkyOrderShips)) {

            List<Long> jkyOrderShipIds = new ArrayList<>(allJkyOrderShips.size());
            allJkyOrderShips.forEach(jkyOrderShip -> {
                jkyOrderShipIds.add(jkyOrderShip.getId());
                jkyOrderShip.setIn2EbsStatus(2);
                jkyOrderShip.setCreateDate(createDate);
                jkyOrderShip.setCreateTime(now);
                jkyOrderShip.setModifyTime(now);
            });
            jkyOrderShipMapper.updateIn2EbsStatus4Return(jkyOrderShipIds);
            jkyOrderShipMapper.insertBatch(allJkyOrderShips);

        }

    }

    /**
     * 不同节点中的定时任务启动时间有差异，会导致重复执行的问题
     *
     * @param start 开始时间
     * <AUTHOR>
     * @date 2023/12/18 17:50
     */
    private void threadSleep(long start) throws InterruptedException {
        long cost = System.currentTimeMillis() - start;
        if (cost < INTERVAL) {
            Thread.sleep(INTERVAL - cost);
        }
    }

    /**
     * 是否在EBS对接暂停期间内
     *
     * @param type 类型：out/sign/in/order
     * <AUTHOR>
     * @date 2024/2/5 9:56
     */
    private boolean isSuspendPeriod(String type) {
        if (MapUtils.isEmpty(suspendPeriod)) {
            log.info("未配置EBS对接暂停期间");
            return false;
        }

        String period = suspendPeriod.get(type);
        if (StringUtils.isEmpty(period)) {
            log.info("未配置{}的EBS对接暂停期间", type);
            return false;
        }
        String[] periods = period.split(",");
        Date now = new Date();
        if (periods.length == 1) {
            if (DateUtil.strToDate(periods[0]).compareTo(now) <= 0) {
                return true;
            }
        } else if (periods.length == 2) {
            if (DateUtil.strToDate(periods[0]).compareTo(now) <= 0 && DateUtil.strToDate(periods[1]).compareTo(now) >= 0) {
                return true;
            }
        } else {
            log.info("{}的EBS对接暂停期间，配置错误", type);
        }

        return false;
    }

}
