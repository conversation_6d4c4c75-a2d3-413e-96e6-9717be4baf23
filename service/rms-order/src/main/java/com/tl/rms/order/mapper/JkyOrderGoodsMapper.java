package com.tl.rms.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tl.rms.order.domain.po.JkyOrderGoods;
import com.tl.rms.order.domain.vo.JkyOrderGoodsEbsVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface JkyOrderGoodsMapper extends BaseMapper<JkyOrderGoods> {

    List<JkyOrderGoodsEbsVO> listOrderGoodsEbsByTradeId(Long tradeId);

    List<Long> listSubTradeIdByTradeId(Long tradeId);

}