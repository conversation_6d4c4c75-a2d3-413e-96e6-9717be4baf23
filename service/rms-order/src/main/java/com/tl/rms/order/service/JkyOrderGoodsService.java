package com.tl.rms.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tl.rms.order.domain.po.JkyOrderGoods;
import com.tl.rms.order.domain.vo.JkyOrderGoodsEbsVO;
import com.tl.rms.order.domain.vo.JkyOrderGoodsForSellCountVo;
import com.tl.rms.order.domain.vo.SellCountByDistributionRequestVo;

import java.util.List;

public interface JkyOrderGoodsService extends IService<JkyOrderGoods> {

    List<JkyOrderGoods> getByTradeId(Long tradeId);

    List<JkyOrderGoods> listByTradeIds(List<Long> tradeIds);

    List<JkyOrderGoodsForSellCountVo> querySellCountByDistributionParam(SellCountByDistributionRequestVo requestVo);

    /**
     * 查询吉客云商品
     *
     * <AUTHOR>
     * @date 2023/5/25
     */
    List<JkyOrderGoodsEbsVO> queryJkyOrderGoodsEbsVOByTradeId(Long tradeId);

    List<Long> listSubTradeIdByTradeId(Long tradeId);

    int deleteByTradeId(Long tradeId);

    boolean create(JkyOrderGoods goods);

}