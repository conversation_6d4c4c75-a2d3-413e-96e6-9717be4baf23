package com.tl.rms.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.lib.mq.producer.MqProducer;
import com.tl.rms.lib.redis.key.RedisKey;
import com.tl.rms.lib.redis.util.RedisLock;
import com.tl.rms.order.domain.enums.JkyOrderShipStatusEnum;
import com.tl.rms.order.domain.po.JkyOrderShip;
import com.tl.rms.order.domain.po.extend.JkyOrderShipExtend;
import com.tl.rms.order.domain.vo.JkyOrderGoodsPreVO;
import com.tl.rms.order.mapper.JkyOrderShipMapper;
import com.tl.rms.order.service.JkyOrderShipService;
import com.tl.rms.util.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class JkyOrderShipServiceImpl extends ServiceImpl<JkyOrderShipMapper, JkyOrderShip>
        implements JkyOrderShipService {

    private final MqProducer mqProducer;

    private final RedisTemplate redisTemplate;

    @Value("${mq.queue.jkyOrderLinePre}")
    private String jkyOrderLinePre;

    @Override
    public Map<String, LocalDateTime> getEarliestSignTimeMapByOrderNoList(List<String> orderNoList) {
        if (CollUtil.isEmpty(orderNoList)) {
            return new HashMap<>();
        }
        List<JkyOrderShip> jkyOrderShipList = this.baseMapper.listEarliestSignTimeGroupByOrderNo(orderNoList);
        return jkyOrderShipList.stream()
                .filter(ship -> ship.getSignTime() != null)
                .collect(Collectors.toMap(JkyOrderShip::getOrderId, JkyOrderShip::getSignTime));
    }

    @Override
    public List<JkyOrderShip> listByOriginalOrderId(String orderId) {
        return baseMapper.listByOriginalOrderId(orderId);
    }

    @Override
    public List<JkyOrderShipExtend> queryByOrderId(String orderId) {
        return baseMapper.listByOrderId(orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveJkyOrderShip(List<JkyOrderShip> jkyOrderShips) {

        String orderId = jkyOrderShips.get(0).getOrderId();
        RedisLock lock = null;
        try {
            lock = new RedisLock(redisTemplate, RedisKey.LOCK_JKY_SAVE_ORDER_SHIP + orderId, 1000 * 60, 1000 * 60);
            if (lock.lock(60000)) {

                Date newDate = new Date();
                LocalDateTime now = LocalDateTime.now();
                Integer createDate = Integer.valueOf(DateUtil.dateToStr(newDate, "yyyyMMdd"));
                for (JkyOrderShip jkyOrderShip : jkyOrderShips) {
                    JkyOrderShip sJkyOrderShip = baseMapper.selectByOrderAndMaterial(jkyOrderShip.getOrderId(), jkyOrderShip.getMaterialCode());
                    jkyOrderShip.setModifyTime(now);
                    jkyOrderShip.setModifiedBy("TW");
                    if (sJkyOrderShip == null) {
                        jkyOrderShip.setCreateDate(createDate);
                        jkyOrderShip.setCreateTime(jkyOrderShip.getModifyTime());
                        jkyOrderShip.setCreatedBy(jkyOrderShip.getModifiedBy());
                        super.save(jkyOrderShip);
                    } else {
                        //判断状态
                        String maxStatus = JkyOrderShipStatusEnum.max(jkyOrderShip.getStatus(), sJkyOrderShip.getStatus());
                        jkyOrderShip.setStatus(maxStatus);
                        jkyOrderShip.setId(sJkyOrderShip.getId());
                        baseMapper.updateByPrimaryKeySelective(jkyOrderShip);
                    }

                }

            } else {
                log.info("保存吉客云销售单物流信息，锁等待: {}", RedisKey.LOCK_JKY_SAVE_ORDER_SHIP + orderId);
            }
        } catch (Exception e) {
            log.error("保存吉客云销售单物流信息，异常 {} ：", orderId, e);
        } finally {
            if (lock != null) {
                lock.unlock();
            }
        }

    }

    /**
     * 发送销售单物流信息至大数据MQ
     *
     * @param jkyOrderShips jkyOrderShips
     * <AUTHOR>
     * @date 2024/6/28
     */
    @Override
    public void sendJkyOrderShip2BigData(List<JkyOrderShip> jkyOrderShips) {
        for (JkyOrderShip jkyOrderShip : jkyOrderShips) {
            JkyOrderGoodsPreVO preVO = new JkyOrderGoodsPreVO();
            preVO.setTradeNo(jkyOrderShip.getOrderId());
            preVO.setMatCode(jkyOrderShip.getMaterialCode());
            mqProducer.sendMessage(jkyOrderLinePre, preVO);
        }
    }

    @Override
    public void updateOut2EbsStatus(List<Long> ids) {
        baseMapper.updateOut2EbsStatus(ids);
    }

    @Override
    public void updateSign2EbsStatus(List<Long> ids) {
        baseMapper.updateSign2EbsStatus(ids);
    }

    @Override
    public void updateIn2EbsStatus(List<Long> ids) {
        baseMapper.updateIn2EbsStatus(ids);
    }

    @Override
    public void updateCancelLine2EbsStatus(List<Long> ids) {
        baseMapper.updateCancelLine2EbsStatus(ids);
    }

}