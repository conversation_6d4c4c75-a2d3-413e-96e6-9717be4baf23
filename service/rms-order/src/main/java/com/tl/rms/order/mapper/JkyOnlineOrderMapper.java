package com.tl.rms.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tl.rms.order.domain.vo.JkyOnlineOrderQueryVo;
import com.tl.rms.order.domain.po.JkyOnlineOrder;
import com.tl.rms.order.domain.vo.JkyOnlineOrderExportVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface JkyOnlineOrderMapper extends BaseMapper<JkyOnlineOrder> {
    IPage<JkyOnlineOrderExportVo> pageWithGoods(Page<JkyOnlineOrder> page, @Param("dto") JkyOnlineOrderQueryVo dto);

    void batchInsertOrUpdate(List<JkyOnlineOrder> list);
}