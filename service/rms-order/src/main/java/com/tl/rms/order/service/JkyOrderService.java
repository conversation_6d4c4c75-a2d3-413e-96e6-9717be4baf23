package com.tl.rms.order.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tl.rms.lib.beans.web.restful.ResponseMessage;
import com.tl.rms.order.domain.po.JkyOrder;
import com.tl.rms.order.domain.vo.*;

import java.util.List;

public interface JkyOrderService extends IService<JkyOrder> {

    Page<JkyOrderVo> page(JkyOrderQueryReqVo jkyOrderQueryReqVo);

    JkyOrderVo getByTradeId(Long tradeId);

    List<DistributionSalesStatisticsRespVo> distributionSalesStatisticsByGbCode(DistributionSalesStatisticsGbCodeReqVo requestVo);

    List<DistributionSalesStatisticsRespVo> distributionSalesStatisticsByMaterialCode(DistributionSalesStatisticsMaterialCodeReqVo requestVo);

    /**
     * 同步吉客云销售单
     *
     * <AUTHOR>
     * @date 2023/5/22
     */
    void syncJkyOrder(List<JkyOrderSyncVO> orderVOs, String userId);

    /**
     * 同步吉客云销售单状态
     *
     * <AUTHOR>
     * @date 2024/6/24
     */
    void syncJkyOrderStatus(List<JkyOrderSyncVO> orderVOs, String userId);

    /**
     * 查询吉客云销售单最大审核时间
     *
     * <AUTHOR>
     * @date 2023/5/22
     */
    String queryJkyOrderMaxAuditTime(String type);

    /**
     * 查询需要更新的吉客云销售单号列表
     *
     * <AUTHOR>
     * @date 2023/11/17 17:56
     */
    List<String> listTradeNo2Update();

    /**
     * 查询需要更新的吉客云销售单号列表
     *
     * @param startDate startDate
     * @param endDate   endDate
     * <AUTHOR>
     * @date 2024/6/24
     */
    List<String> listTradeNoByDate(String startDate, String endDate);

    /**
     * 查询待同步EBS的吉客云销售单
     *
     * <AUTHOR>
     * @date 2023/5/22
     */
    List<Long> listJkyOrderNeedSendEbs();

    /**
     * 查询吉客云销售单
     *
     * <AUTHOR>
     * @date 2023/5/25
     */
    JkyOrderEbsVO queryJkyOrderEbsVOByTradeId(Long tradeId);

    /**
     * 吉客云销售单发送EBS时间
     *
     * <AUTHOR>
     * @date 2023/5/25
     */
    ResponseMessage updateJkyOrderSendEbsTimeCustBranch(JkyOrderEbsVO order);

    /**
     * 吉客云销售单发送EBS状态信息异常
     *
     * @date 2023/10/23
     */
    ResponseMessage updateJkyOrderSendEbsStatusError(Long tradeId, String errorMsg);

    int updateJkyOrderDoNotSendEbs();

    /**
     * 查询吉客云销售单
     *
     * <AUTHOR>
     * @date 2023/5/25
     */
    String queryJkyOrderShopCodeByTradeNo(String tradeNo);

    List<JkyOrder> queryJkyOrderByBillTradeNo(String billTradeNo);

    void sendJkyOrder2BigData(List<String> tradeNos);

}