package com.tl.rms.order.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tl.rms.order.domain.vo.JkyOnlineOrderQueryVo;
import com.tl.rms.order.domain.po.JkyOnlineOrder;
import com.tl.rms.order.domain.vo.JkyOnlineOrderDataVo;
import com.tl.rms.order.domain.vo.JkyOnlineOrderDetailVo;
import com.tl.rms.order.domain.vo.JkyOnlineOrderExportVo;

import java.util.List;

public interface JkyOnlineOrderService extends IService<JkyOnlineOrder> {

    void batchSyncInternetShopOrderData(List<JkyOnlineOrderDataVo> jkyOnlineOrderDatumVos);

    IPage<JkyOnlineOrderExportVo> pageWithGoods(JkyOnlineOrderQueryVo dto);

    JkyOnlineOrderDetailVo detail(String tradeNo);
}