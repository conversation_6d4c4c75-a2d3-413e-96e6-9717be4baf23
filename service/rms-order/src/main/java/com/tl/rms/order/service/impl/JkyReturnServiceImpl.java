package com.tl.rms.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.order.domain.po.JkyReturn;
import com.tl.rms.order.domain.po.JkyReturnGoodsBatch;
import com.tl.rms.order.domain.po.JkyReturnPay;
import com.tl.rms.order.domain.po.JkyReturnReceiver;
import com.tl.rms.order.domain.vo.JkyReturnGoodsVO;
import com.tl.rms.order.domain.vo.JkyReturnVO;
import com.tl.rms.order.mapper.JkyReturnMapper;
import com.tl.rms.order.service.*;
import com.tl.rms.util.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class JkyReturnServiceImpl extends ServiceImpl<JkyReturnMapper, JkyReturn> implements JkyReturnService {

    private final JkyReturnGoodsService returnGoodsService;

    private final JkyReturnGoodsBatchService returnGoodsBatchService;

    private final JkyReturnPayService returnPayService;

    private final JkyReturnReceiverService returnReceiverService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncJkyReturn(List<JkyReturnVO> returnVOs, String userId) {

        long time = System.currentTimeMillis();

        // 封装各级数据
        List<JkyReturn> returnList = new ArrayList<>();
        List<JkyReturnGoodsVO> goodsList = new ArrayList<>();
        List<JkyReturnGoodsBatch> goodsBatchList = new ArrayList<>();
        List<JkyReturnPay> payList = new ArrayList<>();
        List<JkyReturnReceiver> receiverList = new ArrayList<>();

        for (JkyReturnVO returnVO : returnVOs) {
            JkyReturn jkyReturn = new JkyReturn();
            BeanUtil.copy(returnVO, jkyReturn);
            jkyReturn.setIsDelete(returnVO.getDelete());
            jkyReturn.setProblemDesc(returnVO.getProbleamDesc());
            jkyReturn.setCreatedBy(userId);
            jkyReturn.setModifiedBy(userId);
            if (CollectionUtils.isNotEmpty(returnVO.getSendTradeNo())) {
                jkyReturn.setSendTradeNos(String.join(",", returnVO.getSendTradeNo()));
            }
            if (CollectionUtils.isNotEmpty(returnVO.getReturnTradeNo())) {
                jkyReturn.setReturnTradeNos(String.join(",", returnVO.getReturnTradeNo()));
            }

            returnList.add(jkyReturn);

            // 插入前，先删除
            List<JkyReturnGoodsVO> goodsVOList = returnVO.getReturnChangeGoodsDetail();
            if (CollectionUtils.isNotEmpty(goodsVOList)) {

                for (JkyReturnGoodsVO goodsVO : goodsVOList) {
                    goodsVO.setCreatedBy(userId);
                    goodsVO.setModifiedBy(userId);
                    List<JkyReturnGoodsBatch> batchList = goodsVO.getGoodsBatchInfoList();
                    if (CollectionUtils.isNotEmpty(batchList)) {
                        for (JkyReturnGoodsBatch batch : batchList) {
                            batch.setSubTradeId(goodsVO.getSubTradeId());
                            batch.setTradeAfterId(goodsVO.getTradeAfterId());
                            batch.setCreatedBy(userId);
                            batch.setModifiedBy(userId);
                        }
                        goodsBatchList.addAll(batchList);
                    }
                }

                goodsList.addAll(goodsVOList);
            }

            JkyReturnPay pay = returnVO.getReturnChangePay();
            if (pay != null) {
                // 从测试数据中看，需要 setTradeAfterId
                pay.setTradeAfterId(returnVO.getTradeAfterId());
                pay.setCreatedBy(userId);
                pay.setModifiedBy(userId);
                payList.add(pay);
            }

            JkyReturnReceiver receiver = returnVO.getReturnChangeReceiver();
            if (receiver != null) {
                receiver.setCreatedBy(userId);
                receiver.setModifiedBy(userId);
                receiverList.add(receiver);
            }
        }

        // 拿到tradeAfterId数组
        List<Long> tradeAfterIds = returnVOs.stream().map(JkyReturnVO::getTradeAfterId).toList();

        // 根据tradeAfterId数组删除各级数据
        baseMapper.deleteBatchIds(tradeAfterIds);
        returnGoodsService.deleteByTradeAfterIds(tradeAfterIds);
        returnGoodsBatchService.deleteByTradeAfterIds(tradeAfterIds);
        returnPayService.deleteByTradeAfterIds(tradeAfterIds);
        returnReceiverService.deleteByTradeAfterIds(tradeAfterIds);

        // 批量插入封装好的数据
        baseMapper.batchInsert(returnList);
        if (CollectionUtils.isNotEmpty(goodsList)) {
            returnGoodsService.batchInsert(goodsList);
        }
        if (CollectionUtils.isNotEmpty(goodsBatchList)) {
            returnGoodsBatchService.batchInsert(goodsBatchList);
        }
        if (CollectionUtils.isNotEmpty(payList)) {
            returnPayService.batchInsert(payList);
        }
        if (CollectionUtils.isNotEmpty(receiverList)) {
            returnReceiverService.batchInsert(receiverList);
        }

        log.info("syncJkyReturn cost: {}ms", System.currentTimeMillis() - time);

    }

    @Override
    public String queryJkyReturnMaxAuditTime() {
        return baseMapper.queryJkyReturnMaxAuditTime();
    }

}