package com.tl.rms.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.order.domain.po.JkyOrderGoods;
import com.tl.rms.order.domain.vo.JkyOrderGoodsEbsVO;
import com.tl.rms.order.domain.vo.JkyOrderGoodsForSellCountVo;
import com.tl.rms.order.domain.vo.SellCountByDistributionRequestVo;
import com.tl.rms.order.mapper.JkyOrderGoodsMapper;
import com.tl.rms.order.service.JkyOrderGoodsService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class JkyOrderGoodsServiceImpl extends ServiceImpl<JkyOrderGoodsMapper, JkyOrderGoods> implements JkyOrderGoodsService {

    @Override
    public List<JkyOrderGoods> getByTradeId(Long tradeId) {
        return this.list(
                new LambdaQueryWrapper<JkyOrderGoods>()
                        .eq(JkyOrderGoods::getTradeId, tradeId));
    }

    @Override
    public List<JkyOrderGoods> listByTradeIds(List<Long> tradeIds) {
        return this.list(
                new LambdaQueryWrapper<JkyOrderGoods>()
                        .in(JkyOrderGoods::getTradeId, tradeIds));
    }

    @Override
    public List<JkyOrderGoodsForSellCountVo> querySellCountByDistributionParam(SellCountByDistributionRequestVo requestVo) {
        return List.of();
    }

    @Override
    public List<JkyOrderGoodsEbsVO> queryJkyOrderGoodsEbsVOByTradeId(Long tradeId) {
        return baseMapper.listOrderGoodsEbsByTradeId(tradeId);
    }

    @Override
    public List<Long> listSubTradeIdByTradeId(Long tradeId) {
        return baseMapper.listSubTradeIdByTradeId(tradeId);
    }


    @Override
    public int deleteByTradeId(Long tradeId) {
        LambdaQueryWrapper<JkyOrderGoods> lambdaQueryWrapper = Wrappers.lambdaQuery(JkyOrderGoods.class);
        lambdaQueryWrapper.eq(JkyOrderGoods::getTradeId, tradeId);
        return baseMapper.delete(lambdaQueryWrapper);
    }

    @Override
    public boolean create(JkyOrderGoods goods) {
        return super.save(goods);
    }

}