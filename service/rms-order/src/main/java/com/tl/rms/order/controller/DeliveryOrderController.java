package com.tl.rms.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tl.rms.order.api.DeliveryOrderClient;
import com.tl.rms.order.domain.vo.*;
import com.tl.rms.order.service.DeliveryOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/deliveryOrder")
@RequiredArgsConstructor
public class DeliveryOrderController implements DeliveryOrderClient {

    private final DeliveryOrderService deliveryOrderService;

    @Override
    public Page<DeliveryOrderVo> page(DeliveryOrderQueryVo dto) {
        return deliveryOrderService.selectPage(dto);
    }

    @Override
    public List<DeliveryOrderDetailVo> detail(Long id) {
        return deliveryOrderService.detail(id);
    }

    @Override
    public Page<DeliveryOrderExportVo> pageForExport(DeliveryOrderQueryVo dto) {
        return deliveryOrderService.selectExportPage(dto);
    }

    @Override
    public void create(QMDeliveryOrderCreateVo createVo) {
        deliveryOrderService.create(createVo);
    }
}
