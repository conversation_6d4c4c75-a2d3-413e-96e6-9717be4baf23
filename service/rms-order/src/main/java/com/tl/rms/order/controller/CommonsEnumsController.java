package com.tl.rms.order.controller;

import com.tl.rms.common.model.BaseEnum;
import com.tl.rms.common.model.EnumInfoVo;
import com.tl.rms.common.utils.AbstractCommonEnumUtils;
import com.tl.rms.order.api.CommonEnumsClient;
import com.tl.rms.order.domain.enums.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * <AUTHOR>
 */
@Tag(name = "通用枚举管理", description = "枚举查询")
@RestController
@RequestMapping("/jky/commonEnums")
public class CommonsEnumsController implements CommonEnumsClient {

    @SuppressWarnings("rawtypes")
    Map<String, Class<? extends BaseEnum>> map;

    {
        map = new HashMap<>();
        map.put("ExpressCompany", ExpressCompanyEnum.class);
        map.put("LogisticsUploadStatus", LogisticsUploadStatusEnum.class);
        map.put("OnlineOrderStatus", OnlineOrderStatusEnum.class);
        map.put("OnlineOrderTag", OnlineOrderTagEnum.class);
        map.put("ProcessStatus", ProcessStatusEnum.class);
    }

    @Operation(description = "根据枚举编码获取枚举信息")
    @SuppressWarnings("rawtypes")
    @Override
    public List<EnumInfoVo> getEnumsByCode(@Parameter(description = "枚举编码") String code) {
        Class<? extends BaseEnum> clazz = map.get(code);
        if (clazz == null) {
            return Collections.emptyList();
        }
        return AbstractCommonEnumUtils.toEnumInfos(clazz);
    }

    @Operation(description = "根据枚举编码集合获取枚举信息")
    @SuppressWarnings("rawtypes")
    @Override
    public Map<String, List<EnumInfoVo>> getEnumsByCodes(@RequestBody(description = "枚举编码集合") List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptyMap();
        }
        Map<String, List<EnumInfoVo>> result = new HashMap<>();
        if (codes.size() == 1 && Objects.equals(codes.get(0), "all")) {
            map.forEach((k, v) -> {
                result.put(k, AbstractCommonEnumUtils.toEnumInfos(v));
            });
            return result;
        }

        for (String code : codes) {
            Class<? extends BaseEnum> clazz = map.get(code);
            if (clazz == null) {
                continue;
            }
            result.put(code, AbstractCommonEnumUtils.toEnumInfos(clazz));
        }
        return result;
    }
}
