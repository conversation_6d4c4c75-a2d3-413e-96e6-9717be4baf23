package com.tl.rms.order.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tl.rms.common.model.EnumInfoVo;
import com.tl.rms.lib.beans.web.restful.ResponseMessage;
import com.tl.rms.order.api.JkyOrderClient;
import com.tl.rms.order.domain.converter.JkyOrderFlagConverter;
import com.tl.rms.order.domain.enums.*;
import com.tl.rms.order.domain.po.JkyOrder;
import com.tl.rms.order.domain.po.JkyOrderFlag;
import com.tl.rms.order.domain.po.JkyOrderShip;
import com.tl.rms.order.domain.po.extend.JkyOrderShipExtend;
import com.tl.rms.order.domain.vo.*;
import com.tl.rms.order.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping
@RequiredArgsConstructor
public class JkyOrderController implements JkyOrderClient {

    private final JkyOrderService jkyOrderService;

    private final JkyOrderShipService jkyOrderShipService;

    private final JkyOrderGoodsService jkyOrderGoodsService;

    private final JkyOrderPurchaseDiscountRateService jkyOrderPurchaseDiscountRateService;

    private final JkyOrderFlagService jkyOrderFlagService;

    private final JkyOnlineOrderService jkyOnlineOrderService;

    private final JkyReturnService jkyReturnService;

    @Override
    public List<JkyOrderFlagVo> flagList() {
        List<JkyOrderFlag> jkyOrderFlagList = jkyOrderFlagService.list();
        return JkyOrderFlagConverter.MAPPER.toVoList(jkyOrderFlagList);
    }

    @Override
    public List<EnumInfoVo> tradeStatusList() {
        return JkyTradeStatusEnum.list();
    }

    @Override
    public List<EnumInfoVo> tradeTypeList() {
        return JkyTradeTypeEnum.list();
    }

    @Override
    public List<EnumInfoVo> tradeLogisticTypeList() {
        return JkyTradeLogisticTypeEnum.list();
    }

    @Override
    public List<EnumInfoVo> tradeFromList() {
        return JkyTradeFromEnum.list();
    }

    @Override
    public List<EnumInfoVo> tradePayStatusList() {
        return JkyTradePayStatusEnum.list();
    }

    @Override
    public Page<JkyOrderVo> page(@RequestBody JkyOrderQueryReqVo jkyOrderQueryReqVo) {
        return jkyOrderService.page(jkyOrderQueryReqVo);
    }

    @Override
    public JkyOrderVo getByTradeId(@PathVariable("tradeId") Long tradeId) {
        return jkyOrderService.getByTradeId(tradeId);
    }

    @Override
    public List<DistributionSalesStatisticsRespVo> distributionSalesStatisticsByGbCode(DistributionSalesStatisticsGbCodeReqVo requestVo) {
        return jkyOrderService.distributionSalesStatisticsByGbCode(requestVo);
    }

    @Override
    public List<DistributionSalesStatisticsRespVo> distributionSalesStatisticsByMaterialCode(DistributionSalesStatisticsMaterialCodeReqVo requestVo) {
        return jkyOrderService.distributionSalesStatisticsByMaterialCode(requestVo);
    }

    @Override
    public void batchSyncInternetShopOrderData(@RequestBody List<JkyOnlineOrderDataVo> jkyOnlineOrderDatumVos) {
        jkyOnlineOrderService.batchSyncInternetShopOrderData(jkyOnlineOrderDatumVos);
    }

    @Override
    public ResponseMessage syncJkyOrder(@RequestBody List<JkyOrderSyncVO> orderVOs, @RequestParam("userId") String userId) {
        jkyOrderService.syncJkyOrder(orderVOs, userId);
        // 发送大数据
        List<String> tradeNos = orderVOs.stream().map(JkyOrderSyncVO::getTradeNo).collect(Collectors.toList());
        jkyOrderService.sendJkyOrder2BigData(tradeNos);
        return ResponseMessage.ok();
    }

    @Override
    public ResponseMessage syncJkyOrderStatus(@RequestBody List<JkyOrderSyncVO> orderVOs, @RequestParam("userId") String userId) {
        jkyOrderService.syncJkyOrderStatus(orderVOs, userId);
        return ResponseMessage.ok();
    }

    @Override
    public ResponseMessage<String> queryJkyOrderMaxAuditTime(@RequestParam(value = "type", required = false) String type) {
        return ResponseMessage.ok(jkyOrderService.queryJkyOrderMaxAuditTime(type));
    }

    @Override
    public ResponseMessage<List<String>> listTradeNo2Update() {
        return ResponseMessage.ok(jkyOrderService.listTradeNo2Update());
    }

    @Override
    public ResponseMessage<List<String>> listTradeNoByDate(@RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate) {
        return ResponseMessage.ok(jkyOrderService.listTradeNoByDate(startDate, endDate));
    }

    @Override
    public ResponseMessage<JkyOrderEbsVO> queryJkyOrderEbsVOByTradeId(@RequestParam("tradeId") Long tradeId) {
        return ResponseMessage.ok(jkyOrderService.queryJkyOrderEbsVOByTradeId(tradeId));
    }

    @Override
    public ResponseMessage<List<JkyOrderGoodsEbsVO>> queryJkyOrderGoodsEbsVOByTradeId(@RequestParam("tradeId") Long tradeId) {
        return ResponseMessage.ok(jkyOrderGoodsService.queryJkyOrderGoodsEbsVOByTradeId(tradeId));
    }

    @Override
    public ResponseMessage updateJkyOrderSendEbsTimeCustBranch(@RequestBody JkyOrderEbsVO order) {
        return jkyOrderService.updateJkyOrderSendEbsTimeCustBranch(order);
    }

    @Override
    public ResponseMessage updateJkyOrderSendEbsStatusError(@RequestParam("tradeId") Long tradeId, @RequestParam("errorMsg") String errorMsg) {
        return jkyOrderService.updateJkyOrderSendEbsStatusError(tradeId, errorMsg);
    }

    @Override
    public ResponseMessage<String> queryJkyOrderShopCodeByTradeNo(@RequestParam("tradeNo") String tradeNo) {
        return ResponseMessage.ok(jkyOrderService.queryJkyOrderShopCodeByTradeNo(tradeNo));
    }

    @Override
    public ResponseMessage<List<JkyOrder>> queryJkyOrderByBillTradeNo(@RequestParam("billTradeNo") String billTradeNo) {
        return ResponseMessage.ok(jkyOrderService.queryJkyOrderByBillTradeNo(billTradeNo));
    }

    @Override
    public ResponseMessage syncJkyReturn(@RequestBody List<JkyReturnVO> returnVOs, @RequestParam("userId") String userId) {
        jkyReturnService.syncJkyReturn(returnVOs, userId);
        return ResponseMessage.ok();
    }

    @Override
    public ResponseMessage<String> queryJkyReturnMaxAuditTime() {
        return ResponseMessage.ok(jkyReturnService.queryJkyReturnMaxAuditTime());
    }

    @Override
    public ResponseMessage saveJkyOrderShip(@RequestBody List<JkyOrderShip> jkyOrderShips) {
        jkyOrderShipService.saveJkyOrderShip(jkyOrderShips);

        jkyOrderShipService.sendJkyOrderShip2BigData(jkyOrderShips);

        return ResponseMessage.ok();
    }

    @Override
    public List<JkyOrderShipExtend> queryByOrderId(@RequestParam("orderId") String orderId) {
        return jkyOrderShipService.queryByOrderId(orderId);
    }

    @Override
    public void updateOut2EbsStatus(@RequestBody List<Long> ids) {
        jkyOrderShipService.updateOut2EbsStatus(ids);
    }

    @Override
    public void updateSign2EbsStatus(@RequestBody List<Long> ids) {
        jkyOrderShipService.updateSign2EbsStatus(ids);
    }

    @Override
    public void updateIn2EbsStatus(@RequestBody List<Long> ids) {
        jkyOrderShipService.updateIn2EbsStatus(ids);
    }

    @Override
    public void updateCancelLine2EbsStatus(@RequestBody List<Long> ids) {
        jkyOrderShipService.updateCancelLine2EbsStatus(ids);
    }

    @Override
    public ResponseMessage<BigDecimal> getRateByShopAndModel(@RequestParam("shopCode") String shopCode, @RequestParam("modelName") String modelName) {
        return ResponseMessage.ok(jkyOrderPurchaseDiscountRateService.getRateByShopAndModel(shopCode, modelName));
    }

}
