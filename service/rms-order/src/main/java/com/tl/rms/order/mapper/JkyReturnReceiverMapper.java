package com.tl.rms.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tl.rms.order.domain.po.JkyReturnReceiver;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface JkyReturnReceiverMapper extends BaseMapper<JkyReturnReceiver> {

    int deleteByTradeAfterIds(List<Long> tradeAfterId);

    int batchInsert(List<JkyReturnReceiver> rows);

}