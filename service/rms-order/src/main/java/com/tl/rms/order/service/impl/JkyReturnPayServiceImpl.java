package com.tl.rms.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tl.rms.order.domain.po.JkyReturnPay;
import com.tl.rms.order.mapper.JkyReturnPayMapper;
import com.tl.rms.order.service.JkyReturnPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class JkyReturnPayServiceImpl extends ServiceImpl<JkyReturnPayMapper, JkyReturnPay> implements JkyReturnPayService {


    @Override
    public int deleteByTradeAfterIds(List<Long> tradeAfterId) {
        return baseMapper.deleteByTradeAfterIds(tradeAfterId);
    }

    @Override
    public int batchInsert(List<JkyReturnPay> rows) {
        return baseMapper.batchInsert(rows);
    }

}