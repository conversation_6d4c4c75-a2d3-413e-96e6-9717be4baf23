<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tl.rms.order.mapper.JkyReturnMapper">
  <select id="queryJkyReturnMaxAuditTime" resultType="java.lang.String">
    select max(audit_time) from t_jky_return
  </select>
  <delete id="deleteByPrimaryKeys" parameterType="java.lang.Long">
    delete from t_jky_return where trade_after_id in
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">#{item}</foreach>
  </delete>
  <insert id="batchInsert">
    insert into t_jky_return (trade_after_id, return_change_no, trade_after_from, 
      trade_after_status, trade_id, trade_no, 
      source_trade_no, source_trade_after_no, warehouse_id, 
      warehouse_code, warehouse_name, logistic_id, 
      logistic_name, send_warehouse_id, send_warehouse_name, 
      main_postid, shop_id, shop_name, 
      shop_code, company_id, company_name, 
      source_shop_id, source_shop_name, reason_desc, 
      cancel_reason_desc, reject_reason_desc, problem_desc,
      customer_remark, service_remark, audit_time, 
      delivery_time, consign_time, goodslist, 
      flag_ids, flag_names, register_id, 
      registrant, responsible_person_code, responsible_person_desc, 
      stock_in_no, delivery_no, delivery_person, 
      auditor_id, auditor, source_warehouse_id, 
      source_warehouse_name, trade_order_summary, 
      is_freeze, freeze_reason, gmt_create, 
      gmt_modified, source_trade_status, send_trade_nos,
      return_trade_nos, is_delete, return_logistic_type,
      return_logistic_type_explain, created_by, modified_by)
    values
    <foreach collection="list" item="item" index="index" separator=",">
     (#{item.tradeAfterId,jdbcType=BIGINT}, #{item.returnChangeNo,jdbcType=VARCHAR}, #{item.tradeAfterFrom,jdbcType=TINYINT},
      #{item.tradeAfterStatus,jdbcType=VARCHAR}, #{item.tradeId,jdbcType=BIGINT}, #{item.tradeNo,jdbcType=VARCHAR}, 
      #{item.sourceTradeNo,jdbcType=VARCHAR}, #{item.sourceTradeAfterNo,jdbcType=VARCHAR}, #{item.warehouseId,jdbcType=BIGINT}, 
      #{item.warehouseCode,jdbcType=VARCHAR}, #{item.warehouseName,jdbcType=VARCHAR}, #{item.logisticId,jdbcType=BIGINT}, 
      #{item.logisticName,jdbcType=VARCHAR}, #{item.sendWarehouseId,jdbcType=BIGINT}, #{item.sendWarehouseName,jdbcType=VARCHAR}, 
      #{item.mainPostid,jdbcType=VARCHAR}, #{item.shopId,jdbcType=BIGINT}, #{item.shopName,jdbcType=VARCHAR}, 
      #{item.shopCode,jdbcType=VARCHAR}, #{item.companyId,jdbcType=BIGINT}, #{item.companyName,jdbcType=VARCHAR}, 
      #{item.sourceShopId,jdbcType=BIGINT}, #{item.sourceShopName,jdbcType=VARCHAR}, #{item.reasonDesc,jdbcType=VARCHAR}, 
      #{item.cancelReasonDesc,jdbcType=VARCHAR}, #{item.rejectReasonDesc,jdbcType=VARCHAR}, #{item.problemDesc,jdbcType=VARCHAR},
      #{item.customerRemark,jdbcType=VARCHAR}, #{item.serviceRemark,jdbcType=VARCHAR}, #{item.auditTime,jdbcType=TIMESTAMP}, 
      #{item.deliveryTime,jdbcType=TIMESTAMP}, #{item.consignTime,jdbcType=TIMESTAMP}, #{item.goodslist,jdbcType=VARCHAR}, 
      #{item.flagIds,jdbcType=VARCHAR}, #{item.flagNames,jdbcType=VARCHAR}, #{item.registerId,jdbcType=BIGINT}, 
      #{item.registrant,jdbcType=VARCHAR}, #{item.responsiblePersonCode,jdbcType=BIGINT}, #{item.responsiblePersonDesc,jdbcType=VARCHAR}, 
      #{item.stockInNo,jdbcType=VARCHAR}, #{item.deliveryNo,jdbcType=VARCHAR}, #{item.deliveryPerson,jdbcType=VARCHAR}, 
      #{item.auditorId,jdbcType=BIGINT}, #{item.auditor,jdbcType=VARCHAR}, #{item.sourceWarehouseId,jdbcType=BIGINT}, 
      #{item.sourceWarehouseName,jdbcType=VARCHAR}, #{item.tradeOrderSummary,jdbcType=VARCHAR}, 
      #{item.isFreeze,jdbcType=TINYINT}, #{item.freezeReason,jdbcType=VARCHAR}, #{item.gmtCreate,jdbcType=TIMESTAMP}, 
      #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.sourceTradeStatus,jdbcType=TINYINT}, #{item.sendTradeNos,jdbcType=VARCHAR},
      #{item.returnTradeNos,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=TINYINT}, #{item.returnLogisticType,jdbcType=VARCHAR},
      #{item.returnLogisticTypeExplain,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR}, #{item.modifiedBy,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>