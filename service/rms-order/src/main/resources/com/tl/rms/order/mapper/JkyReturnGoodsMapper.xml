<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tl.rms.order.mapper.JkyReturnGoodsMapper">
  <delete id="deleteByTradeAfterIds" parameterType="java.lang.Long">
    delete from t_jky_return_goods where trade_after_id in
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">#{item}</foreach>
  </delete>
  <insert id="batchInsert">
    insert into t_jky_return_goods (sub_trade_id, trade_after_id, goods_id, 
      goods_no, goods_name, spec_id, 
      spec_name, unit, price, 
      sell_count, reason_desc, return_count, 
      return_fee, send_count, return_discounts, 
      send_discounts, send_fee, should_return_fee, 
      remark, goods_attribute, gmt_modified, 
      gmt_create, barcode, sell_total, 
      delivery_count, is_fit, is_gift, 
      share_return_fee, share_send_fee, share_should_return_fee, 
      created_by, modified_by)
    values
    <foreach collection="list" item="item" index="index" separator=",">
     (#{item.subTradeId,jdbcType=BIGINT}, #{item.tradeAfterId,jdbcType=BIGINT}, #{item.goodsId,jdbcType=BIGINT},
      #{item.goodsNo,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR}, #{item.specId,jdbcType=BIGINT}, 
      #{item.specName,jdbcType=VARCHAR}, #{item.unit,jdbcType=VARCHAR}, #{item.price,jdbcType=DECIMAL}, 
      #{item.sellCount,jdbcType=INTEGER}, #{item.reasonDesc,jdbcType=VARCHAR}, #{item.returnCount,jdbcType=INTEGER}, 
      #{item.returnFee,jdbcType=DECIMAL}, #{item.sendCount,jdbcType=INTEGER}, #{item.returnDiscounts,jdbcType=DECIMAL}, 
      #{item.sendDiscounts,jdbcType=DECIMAL}, #{item.sendFee,jdbcType=DECIMAL}, #{item.shouldReturnFee,jdbcType=DECIMAL}, 
      #{item.remark,jdbcType=VARCHAR}, #{item.goodsAttribute,jdbcType=TINYINT}, #{item.gmtModified,jdbcType=TIMESTAMP}, 
      #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.barcode,jdbcType=VARCHAR}, #{item.sellTotal,jdbcType=DECIMAL}, 
      #{item.deliveryCount,jdbcType=INTEGER}, #{item.isFit,jdbcType=TINYINT}, #{item.isGift,jdbcType=TINYINT}, 
      #{item.shareReturnFee,jdbcType=DECIMAL}, #{item.shareSendFee,jdbcType=DECIMAL}, #{item.shareShouldReturnFee,jdbcType=DECIMAL}, 
      #{item.createdBy,jdbcType=VARCHAR}, #{item.modifiedBy,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>