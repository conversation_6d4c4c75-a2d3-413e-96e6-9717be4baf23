<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tl.rms.order.mapper.JkyOrderMapper">
  <resultMap id="BaseResultMap" type="com.tl.rms.order.domain.po.JkyOrder">
    <id column="trade_id" jdbcType="BIGINT" property="tradeId" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="check_total" jdbcType="DECIMAL" property="checkTotal" />
    <result column="other_fee" jdbcType="DECIMAL" property="otherFee" />
    <result column="charge_currency" jdbcType="VARCHAR" property="chargeCurrency" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="pay_no" jdbcType="VARCHAR" property="payNo" />
    <result column="seller_memo" jdbcType="VARCHAR" property="sellerMemo" />
    <result column="buyer_memo" jdbcType="VARCHAR" property="buyerMemo" />
    <result column="append_memo" jdbcType="VARCHAR" property="appendMemo" />
    <result column="trade_from" jdbcType="INTEGER" property="tradeFrom" />
    <result column="register" jdbcType="VARCHAR" property="register" />
    <result column="seller" jdbcType="VARCHAR" property="seller" />
    <result column="auditor" jdbcType="VARCHAR" property="auditor" />
    <result column="reviewer" jdbcType="VARCHAR" property="reviewer" />
    <result column="estimate_weight" jdbcType="DECIMAL" property="estimateWeight" />
    <result column="package_weight" jdbcType="DECIMAL" property="packageWeight" />
    <result column="trade_count" jdbcType="DECIMAL" property="tradeCount" />
    <result column="goods_type_count" jdbcType="DECIMAL" property="goodsTypeCount" />
    <result column="freeze_reason" jdbcType="VARCHAR" property="freezeReason" />
    <result column="abnormal_description" jdbcType="VARCHAR" property="abnormalDescription" />
    <result column="online_trade_no" jdbcType="VARCHAR" property="onlineTradeNo" />
    <result column="goodslist" jdbcType="VARCHAR" property="goodslist" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="stockout_no" jdbcType="VARCHAR" property="stockoutNo" />
    <result column="confirm_time" jdbcType="TIMESTAMP" property="confirmTime" />
    <result column="depart_name" jdbcType="VARCHAR" property="departName" />
    <result column="last_ship_time" jdbcType="TIMESTAMP" property="lastShipTime" />
    <result column="pay_status" jdbcType="INTEGER" property="payStatus" />
    <result column="charge_currency_code" jdbcType="VARCHAR" property="chargeCurrencyCode" />
    <result column="charge_exchange_rate" jdbcType="DECIMAL" property="chargeExchangeRate" />
    <result column="trade_status" jdbcType="INTEGER" property="tradeStatus" />
    <result column="gross_profit" jdbcType="DECIMAL" property="grossProfit" />
    <result column="estimate_volume" jdbcType="DECIMAL" property="estimateVolume" />
    <result column="customer_type_name" jdbcType="VARCHAR" property="customerTypeName" />
    <result column="customer_grade_name" jdbcType="VARCHAR" property="customerGradeName" />
    <result column="customer_tags" jdbcType="VARCHAR" property="customerTags" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="customer_discount" jdbcType="DECIMAL" property="customerDiscount" />
    <result column="special_reminding" jdbcType="VARCHAR" property="specialReminding" />
    <result column="black_list" jdbcType="INTEGER" property="blackList" />
    <result column="trade_time" jdbcType="TIMESTAMP" property="tradeTime" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="state" jdbcType="VARCHAR" property="state" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="district" jdbcType="VARCHAR" property="district" />
    <result column="town" jdbcType="VARCHAR" property="town" />
    <result column="zip" jdbcType="VARCHAR" property="zip" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="country_code" jdbcType="VARCHAR" property="countryCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="invoice_type" jdbcType="INTEGER" property="invoiceType" />
    <result column="payer_name" jdbcType="VARCHAR" property="payerName" />
    <result column="payer_regno" jdbcType="VARCHAR" property="payerRegno" />
    <result column="payer_bank_account" jdbcType="VARCHAR" property="payerBankAccount" />
    <result column="payer_phone" jdbcType="VARCHAR" property="payerPhone" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="payer_address" jdbcType="VARCHAR" property="payerAddress" />
    <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo" />
    <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="invoice_status" jdbcType="INTEGER" property="invoiceStatus" />
    <result column="payer_bank_name" jdbcType="VARCHAR" property="payerBankName" />
    <result column="pre_typedetail" jdbcType="VARCHAR" property="preTypedetail" />
    <result column="first_payment" jdbcType="DECIMAL" property="firstPayment" />
    <result column="final_payment" jdbcType="DECIMAL" property="finalPayment" />
    <result column="first_paytime" jdbcType="TIMESTAMP" property="firstPaytime" />
    <result column="final_paytime" jdbcType="TIMESTAMP" property="finalPaytime" />
    <result column="review_time" jdbcType="TIMESTAMP" property="reviewTime" />
    <result column="activation_time" jdbcType="TIMESTAMP" property="activationTime" />
    <result column="customer_total_fee" jdbcType="DECIMAL" property="customerTotalFee" />
    <result column="customer_discount_fee" jdbcType="DECIMAL" property="customerDiscountFee" />
    <result column="notify_pick_time" jdbcType="TIMESTAMP" property="notifyPickTime" />
    <result column="consign_time" jdbcType="TIMESTAMP" property="consignTime" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="customer_post_fee" jdbcType="DECIMAL" property="customerPostFee" />
    <result column="shop_id" jdbcType="VARCHAR" property="shopId" />
    <result column="shop_code" jdbcType="VARCHAR" property="shopCode" />
    <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
    <result column="customer_payment" jdbcType="DECIMAL" property="customerPayment" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="is_bill_check" jdbcType="TINYINT" property="isBillCheck" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="logistic_name" jdbcType="VARCHAR" property="logisticName" />
    <result column="bill_date" jdbcType="TIMESTAMP" property="billDate" />
    <result column="logistic_type" jdbcType="INTEGER" property="logisticType" />
    <result column="main_postid" jdbcType="VARCHAR" property="mainPostid" />
    <result column="trade_type" jdbcType="INTEGER" property="tradeType" />
    <result column="total_fee" jdbcType="DECIMAL" property="totalFee" />
    <result column="tax_fee" jdbcType="DECIMAL" property="taxFee" />
    <result column="received_post_fee" jdbcType="DECIMAL" property="receivedPostFee" />
    <result column="discount_fee" jdbcType="DECIMAL" property="discountFee" />
    <result column="payment" jdbcType="DECIMAL" property="payment" />
    <result column="coupon_fee" jdbcType="DECIMAL" property="couponFee" />
    <result column="received_total" jdbcType="DECIMAL" property="receivedTotal" />
    <result column="post_fee" jdbcType="DECIMAL" property="postFee" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="signing_time" jdbcType="TIMESTAMP" property="signingTime" />
    <result column="settle_audit_time" jdbcType="TIMESTAMP" property="settleAuditTime" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="local_payment" jdbcType="DECIMAL" property="localPayment" />
    <result column="local_exchange_rate" jdbcType="DECIMAL" property="localExchangeRate" />
    <result column="customer_account" jdbcType="VARCHAR" property="customerAccount" />
    <result column="local_currency_code" jdbcType="VARCHAR" property="localCurrencyCode" />
    <result column="plat_complete_time" jdbcType="TIMESTAMP" property="platCompleteTime" />
    <result column="shop_type_code" jdbcType="VARCHAR" property="shopTypeCode" />
    <result column="flag_ids" jdbcType="VARCHAR" property="flagIds" />
    <result column="flag_names" jdbcType="VARCHAR" property="flagNames" />
    <result column="sys_flag_ids" jdbcType="VARCHAR" property="sysFlagIds" />
    <result column="source_after_no" jdbcType="VARCHAR" property="sourceAfterNo" />
    <result column="gov_subsidy_amount" jdbcType="VARCHAR" property="govSubsidyAmount" />
    <result column="gov_subsidy_amount_merchant" jdbcType="VARCHAR" property="govSubsidyAmountMerchant" />
    <result column="ebs_amount" jdbcType="VARCHAR" property="ebsAmount" />
    <result column="purchase_discount_rate" jdbcType="DECIMAL" property="purchaseDiscountRate" />
    <result column="consign_code" jdbcType="VARCHAR" property="consignCode" />
    <result column="send_ebs_status" jdbcType="TINYINT" property="sendEbsStatus" />
    <result column="send_ebs_time" jdbcType="TIMESTAMP" property="sendEbsTime" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="branch_name" jdbcType="VARCHAR" property="branchName" />
    <result column="create_date" jdbcType="INTEGER" property="createDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.tl.rms.order.domain.po.JkyOrder">
    <result column="gov_subsidy" jdbcType="LONGVARCHAR" property="govSubsidy" />
  </resultMap>
  <sql id="Base_Column_List">
    trade_id, trade_no, check_total, other_fee, charge_currency, account_name, pay_type,
    pay_no, seller_memo, buyer_memo, append_memo, trade_from, register, seller, auditor,
    reviewer, estimate_weight, package_weight, trade_count, goods_type_count, freeze_reason,
    abnormal_description, online_trade_no, goodslist, gmt_create, gmt_modified, stockout_no,
    confirm_time, depart_name, last_ship_time, pay_status, charge_currency_code, charge_exchange_rate,
    trade_status, gross_profit, estimate_volume, customer_type_name, customer_grade_name,
    customer_tags, customer_code, customer_discount, special_reminding, black_list, trade_time,
    country, state, city, district, town, zip, pay_time, country_code, city_code, invoice_type,
    payer_name, payer_regno, payer_bank_account, payer_phone, audit_time, payer_address,
    invoice_no, invoice_code, invoice_status, payer_bank_name, pre_typedetail, first_payment,
    final_payment, first_paytime, final_paytime, review_time, activation_time, customer_total_fee,
    customer_discount_fee, notify_pick_time, consign_time, order_no, customer_post_fee,
    shop_id, shop_code, shop_name, customer_payment, company_name, is_bill_check, warehouse_code,
    warehouse_name, logistic_name, bill_date, logistic_type, main_postid, trade_type,
    total_fee, tax_fee, received_post_fee, discount_fee, payment, coupon_fee, received_total,
    post_fee, complete_time, signing_time, settle_audit_time, is_delete, local_payment,
    local_exchange_rate, customer_account, local_currency_code, plat_complete_time, shop_type_code,
    flag_ids, flag_names, sys_flag_ids, source_after_no, gov_subsidy_amount, gov_subsidy_amount_merchant,
    ebs_amount, purchase_discount_rate, consign_code, send_ebs_status, send_ebs_time,
    customer_id, branch_name, create_date, create_time, created_by, modify_time, modified_by,
    remark
  </sql>

  <select id="distributionSalesStatisticsByGbCode"
          resultType="com.tl.rms.order.domain.vo.DistributionSalesStatisticsRespVo">
    SELECT
      h.shop_code AS shopCode,
      l.goods_no AS `code`,
      sum(l.sell_count) AS sellCount
    FROM
      t_jky_order_goods l
        LEFT JOIN t_jky_order h ON h.trade_id = l.trade_id
    WHERE
      h.trade_type = '1'
      AND h.trade_status NOT IN ('5010', '5020', '5030')
      AND h.payment != 0
      AND h.pay_time > #{requestVo.startTime}
      AND h.pay_time &lt; #{requestVo.endTime}
      <if test="requestVo.gbCodes != null and requestVo.gbCodes.size() != 0" >
        AND l.goods_no IN
        <foreach collection="requestVo.gbCodes" item="gbCode" open="(" separator="," close=")">
          #{gbCode}
        </foreach>
      </if>
      <if test="requestVo.shopCodes != null and requestVo.shopCodes.size() != 0">
        AND h.shop_code IN
        <foreach collection="requestVo.shopCodes" item="shopCode" open="(" separator="," close=")">
          #{shopCode}
        </foreach>
      </if>
    GROUP BY
    h.shop_code,
    l.goods_no
  </select>

  <select id="distributionSalesStatisticsByMaterialCode"
          resultType="com.tl.rms.order.domain.vo.DistributionSalesStatisticsRespVo">
    SELECT
    h.shop_code AS shopCode,
    l.barcode AS `code`,
    sum(l.sell_count) AS sellCount
    FROM
    t_jky_order_goods l
    LEFT JOIN t_jky_order h ON h.trade_id = l.trade_id
    WHERE
    h.trade_type = '1'
    AND h.trade_status NOT IN ('5010', '5020', '5030')
    AND h.payment != 0
    AND h.pay_time > #{requestVo.startTime}
    AND h.pay_time &lt; #{requestVo.endTime}
    <if test="requestVo.materialCodes != null and requestVo.materialCodes.size() != 0" >
      AND l.barcode IN
      <foreach collection="requestVo.materialCodes" item="materialCode" open="(" separator="," close=")">
        #{materialCode}
      </foreach>
    </if>
    <if test="requestVo.shopCodes != null and requestVo.shopCodes.size() != 0">
      AND h.shop_code IN
      <foreach collection="requestVo.shopCodes" item="shopCode" open="(" separator="," close=")">
        #{shopCode}
      </foreach>
    </if>
    GROUP BY
    h.shop_code,
    l.barcode
  </select>

  <select id="queryJkyOrderMaxAuditTime" resultType="java.lang.String">
    select max(audit_time) from t_jky_order <if test="type != null and type != ''">where trade_no like concat(#{type}, '%')</if>
  </select>
  <select id="listTradeNo2Update" resultType="java.lang.String">
    select trade_no from t_jky_order where create_date >= date_format(date_add(now(), interval -7 day), '%Y%m%d') and trade_status in <foreach collection="status" item="item" open="(" separator="," close=")">#{item}</foreach>
  </select>
  <select id="listTradeNoByDate" resultType="java.lang.String">
    select trade_no from t_jky_order where create_time >= #{startDate} and concat(#{endDate}, ' 23:59:59') >= create_time
  </select>
  <select id="listJkyOrderNeedSendEbs" resultType="java.lang.Long">
    select o.trade_id from t_jky_order o
     where o.modify_time >= date_add(now(), interval -7 day) and o.audit_time is not null
       and (o.send_ebs_status = 2 or (o.send_ebs_status = 3 and o.remark = '查询物料信息失败' and o.modify_time > '2024-11-07 20'))
       and (o.trade_type in (1, 2, 7) or
           exists(select 1 from t_jky_return r where r.return_change_no = o.source_after_no and length(r.trade_no) > 0))
  </select>
  <resultMap id="OrderEbsResultMap" type="com.tl.rms.order.domain.vo.JkyOrderEbsVO">
    <result column="trade_id" jdbcType="BIGINT" property="tradeId" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="online_trade_no" jdbcType="VARCHAR" property="onlineTradeNo" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="trade_type" jdbcType="INTEGER" property="tradeType" />
    <result column="shop_code" jdbcType="VARCHAR" property="shopCode" />
    <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="branch_name" jdbcType="VARCHAR" property="branchName" />
    <result column="gov_subsidy_amount" jdbcType="VARCHAR" property="govSubsidyAmount" />
    <result column="gov_subsidy_amount_merchant" jdbcType="VARCHAR" property="govSubsidyAmountMerchant" />
    <result column="ebs_amount" jdbcType="VARCHAR" property="ebsAmount" />
    <result column="purchase_discount_rate" jdbcType="DECIMAL" property="purchaseDiscountRate" />
    <result column="state" jdbcType="VARCHAR" property="state" />
    <result column="flag_names" jdbcType="VARCHAR" property="flagNames" />
    <result column="consign_code" jdbcType="VARCHAR" property="consignCode" />
    <result column="source_after_no" jdbcType="VARCHAR" property="sourceAfterNo" />
    <result column="return_source_trade_no" jdbcType="VARCHAR" property="returnSourceTradeNo" />
  </resultMap>
  <select id="queryJkyOrderEbsVOByTradeId" parameterType="java.lang.Long" resultMap="OrderEbsResultMap">
    select o.trade_id, o.trade_no, o.trade_type, o.online_trade_no, o.audit_time, o.warehouse_code, o.ebs_amount,
           coalesce(o.purchase_discount_rate, oo.purchase_discount_rate) purchase_discount_rate, o.gov_subsidy_amount_merchant,
           coalesce(o.customer_id, oo.customer_id) customer_id, coalesce(o.branch_name, oo.branch_name) branch_name,
           coalesce(o.gov_subsidy_amount, o.gov_subsidy ->> '$.govSubsidyAmount', o.gov_subsidy ->> '$.govSubsidyAmountExact') gov_subsidy_amount,
           coalesce(oo.consign_code, oo.gov_subsidy ->> '$.goodInfos[0].companyName', oo.gov_subsidy ->> '$.govMainSubject',
                    o.consign_code, o.gov_subsidy ->> '$.goodInfos[0].companyName', o.gov_subsidy ->> '$.govMainSubject') consign_code,
           o.source_after_no, o.shop_code, o.shop_name, o.state, o.flag_names, r.trade_no return_source_trade_no
    from t_jky_order o left join t_jky_return r on r.return_change_no = o.source_after_no
                       left join t_jky_order oo on oo.trade_no = r.trade_no where o.trade_id = #{tradeId,jdbcType=BIGINT}
  </select>
  <update id="updateJkyOrderSendEbsTimeCustBranch" parameterType="com.tl.rms.order.domain.vo.JkyOrderEbsVO">
    update t_jky_order set send_ebs_status = 1, send_ebs_time = now(), remark = null, modify_time = now(),
    ebs_amount = #{ebsAmount,jdbcType=VARCHAR},
    <if test="purchaseDiscountRate != null">
      purchase_discount_rate = #{purchaseDiscountRate,jdbcType=DECIMAL},
    </if>
    customer_id = #{customerId,jdbcType=VARCHAR}, branch_name = #{branchName,jdbcType=VARCHAR}
    where trade_id = #{tradeId,jdbcType=BIGINT} and (send_ebs_status = 2 or (send_ebs_status = 3 and remark = '查询物料信息失败'))
  </update>
  <select id="selectShopCodeByTradeNo" resultType="java.lang.String" >
    select shop_code from t_jky_order where trade_no = #{tradeNo} limit 1
  </select>

  <update id="updateJkyOrderDoNotSendEbs">
    update t_jky_order set send_ebs_status = 0, modify_time = now()
    where trade_id in (select t.trade_id from (select o.trade_id
     from t_jky_order_ship s left join t_jky_order o on o.trade_no = s.order_id
    where s.create_date >= date_format(date_sub(now(), interval 30 day), '%Y%m%d')
      and o.send_ebs_status = 2 and s.cancel_2_ebs_status = 2 group by o.trade_id) t)
  </update>
  <select id="selectJkyOrderByBillTradeNo"  resultMap="BaseResultMap">
    select trade_id,trade_no,shop_code from t_jky_order o where FIND_IN_SET(#{billTradeNo},o.online_trade_no)
  </select>

  <resultMap id="BigDataResultMap" type="com.tl.rms.order.domain.vo.JkyOrderGoodsBigDataVO">
    <result column="sub_trade_id" jdbcType="BIGINT" property="lineId" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="seller_memo" jdbcType="VARCHAR" property="sellerMemo" />
    <result column="buyer_memo" jdbcType="VARCHAR" property="buyerMemo" />
    <result column="trade_count" jdbcType="BIGINT" property="tradeCount" />
    <result column="online_trade_no" jdbcType="VARCHAR" property="onlineTradeNo" />
    <result column="goodslist" jdbcType="VARCHAR" property="goodslist" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="trade_status" jdbcType="INTEGER" property="tradeStatus" />
    <result column="customer_type_name" jdbcType="VARCHAR" property="customerTypeName" />
    <result column="customer_grade_name" jdbcType="VARCHAR" property="customerGradeName" />
    <result column="customer_tags" jdbcType="VARCHAR" property="customerTags" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="customer_discount" jdbcType="DECIMAL" property="customerDiscount" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="consign_time" jdbcType="TIMESTAMP" property="consignTime" />
    <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="logistic_name" jdbcType="VARCHAR" property="logisticName" />
    <result column="main_postid" jdbcType="VARCHAR" property="mainPostid" />
    <result column="state" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="flag_names" jdbcType="VARCHAR" property="flagNames" />
    <result column="trade_type" jdbcType="INTEGER" property="tradeType" />
    <result column="received_post_fee" jdbcType="DECIMAL" property="receivedPostFee" />
    <result column="discount_fee" jdbcType="DECIMAL" property="discountFee" />
    <result column="payment" jdbcType="DECIMAL" property="payment" />
    <result column="coupon_fee" jdbcType="DECIMAL" property="couponFee" />
    <result column="received_total" jdbcType="DECIMAL" property="receivedTotal" />
    <result column="local_payment" jdbcType="DECIMAL" property="localPayment" />
    <result column="customer_account" jdbcType="VARCHAR" property="customerAccount" />
    <result column="source_after_no" jdbcType="VARCHAR" property="sourceAfterNo" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="spec_name" jdbcType="VARCHAR" property="specName" />
    <result column="barcode" jdbcType="VARCHAR" property="matCode" />
    <result column="sell_count" jdbcType="DECIMAL" property="sellCount" />
    <result column="sell_price" jdbcType="DECIMAL" property="sellPrice" />
    <result column="sell_total" jdbcType="DECIMAL" property="sellTotal" />
    <result column="discount_total" jdbcType="DECIMAL" property="discountTotal" />
    <result column="is_gift" jdbcType="INTEGER" property="isGift" />
    <result column="plat_author_id" jdbcType="VARCHAR" property="platAuthorId" />
    <result column="plat_author_name" jdbcType="VARCHAR" property="platAuthorName" />
    <result column="share_favourable_after_fee" jdbcType="DECIMAL" property="shareFavourableAfterFee" />
    <result column="l_status" jdbcType="INTEGER" property="lineStatus" />
    <result column="out_qty" jdbcType="INTEGER" property="outQty" />
    <result column="out_time" jdbcType="TIMESTAMP" property="outDate" />
    <result column="sign_qty" jdbcType="INTEGER" property="signQty" />
    <result column="sign_time" jdbcType="TIMESTAMP" property="signDate" />
    <result column="in_qty" jdbcType="INTEGER" property="inQty" />
    <result column="in_time" jdbcType="TIMESTAMP" property="inDate" />
  </resultMap>
  <select id="listJkyOrder2BigData" resultMap="BigDataResultMap" parameterType="com.tl.rms.order.domain.vo.JkyOrderGoodsPreVO">
    select o.trade_no,o.trade_status,o.gmt_create,o.trade_type,o.shop_name,o.audit_time,o.pay_time,o.warehouse_name,
    o.logistic_name,o.online_trade_no,o.consign_time,o.local_payment,o.trade_count,o.goodslist,o.customer_account,
    o.seller_memo,o.buyer_memo,o.customer_discount,o.received_post_fee,o.discount_fee,o.payment,o.coupon_fee,
    o.received_total,o.pay_type,o.source_after_no,o.customer_type_name,o.customer_grade_name,o.customer_tags,
    o.customer_code,o.main_postid,o.state,o.city,o.flag_names,g.goods_no,g.goods_name,g.spec_name,g.barcode,g.sell_count,g.sell_price,
    g.discount_total,g.is_gift,g.share_favourable_after_fee,g.sell_total,g.plat_author_id,g.plat_author_name,
    g.sub_trade_id,s.status l_status,s.out_qty,s.sign_qty,s.in_qty,s.out_time,s.sign_time,s.in_time
    from t_jky_order o join t_jky_order_goods g on o.trade_id = g.trade_id
    left join t_jky_order_ship s on s.order_id = o.trade_no and s.material_code = g.barcode
    <where>
      <if test="tradeNos != null and tradeNos.size > 0">
        and o.trade_no in
        <foreach collection="tradeNos" index="index" item="item" separator="," open="(" close=")">#{item}</foreach>
      </if>
      <if test="tradeNo != null">
        and o.trade_no = #{tradeNo,jdbcType=VARCHAR}
      </if>
      <if test="matCode != null">
        and g.barcode = #{matCode,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

  <select id="queryOrderByCreateTime" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from t_jky_order
     where create_time >= #{startTime} and create_time &lt;= #{endTime}
  </select>

  <select id="queryJkyOrderByTradeNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from t_jky_order where trade_no = #{tradeNo,jdbcType=VARCHAR}
  </select>

  <select id="listTradeIdsByTradeTypeReturn" resultType="java.lang.Long" parameterType="java.util.List">
    SELECT trade_id FROM t_jky_order WHERE trade_id IN <foreach collection="tradeIds" item="item" open="(" separator="," close=")">#{item}</foreach> AND trade_type = 8
  </select>

  <select id="queryJkyOrderBySourceAfterNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" /> FROM t_jky_order WHERE trade_id = (SELECT trade_id FROM t_jky_return WHERE return_change_no= #{sourceAfterNo,jdbcType=VARCHAR})
  </select>

  <select id="queryJkyOrderByAfterSaleTradeId" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select oo.* from t_jky_order oo join t_jky_return r on oo.trade_id = r.trade_id
                                    join t_jky_order o on r.return_change_no = o.source_after_no where o.trade_id = #{tradeId,jdbcType=BIGINT}
  </select>

  <update id="updateByPrimaryKeySelective" parameterType="com.tl.rms.order.domain.po.JkyOrder">
    update t_jky_order
    <set>
      <if test="tradeNo != null">
        trade_no = #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="checkTotal != null">
        check_total = #{checkTotal,jdbcType=DECIMAL},
      </if>
      <if test="otherFee != null">
        other_fee = #{otherFee,jdbcType=DECIMAL},
      </if>
      <if test="chargeCurrency != null">
        charge_currency = #{chargeCurrency,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null">
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      <if test="payNo != null">
        pay_no = #{payNo,jdbcType=VARCHAR},
      </if>
      <if test="sellerMemo != null">
        seller_memo = #{sellerMemo,jdbcType=VARCHAR},
      </if>
      <if test="buyerMemo != null">
        buyer_memo = #{buyerMemo,jdbcType=VARCHAR},
      </if>
      <if test="appendMemo != null">
        append_memo = #{appendMemo,jdbcType=VARCHAR},
      </if>
      <if test="tradeFrom != null">
        trade_from = #{tradeFrom,jdbcType=INTEGER},
      </if>
      <if test="register != null">
        register = #{register,jdbcType=VARCHAR},
      </if>
      <if test="seller != null">
        seller = #{seller,jdbcType=VARCHAR},
      </if>
      <if test="auditor != null">
        auditor = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="reviewer != null">
        reviewer = #{reviewer,jdbcType=VARCHAR},
      </if>
      <if test="estimateWeight != null">
        estimate_weight = #{estimateWeight,jdbcType=DECIMAL},
      </if>
      <if test="packageWeight != null">
        package_weight = #{packageWeight,jdbcType=DECIMAL},
      </if>
      <if test="tradeCount != null">
        trade_count = #{tradeCount,jdbcType=DECIMAL},
      </if>
      <if test="goodsTypeCount != null">
        goods_type_count = #{goodsTypeCount,jdbcType=DECIMAL},
      </if>
      <if test="freezeReason != null">
        freeze_reason = #{freezeReason,jdbcType=VARCHAR},
      </if>
      <if test="abnormalDescription != null">
        abnormal_description = #{abnormalDescription,jdbcType=VARCHAR},
      </if>
      <if test="onlineTradeNo != null">
        online_trade_no = #{onlineTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="goodslist != null">
        goodslist = #{goodslist,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="stockoutNo != null">
        stockout_no = #{stockoutNo,jdbcType=VARCHAR},
      </if>
      <if test="confirmTime != null">
        confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="departName != null">
        depart_name = #{departName,jdbcType=VARCHAR},
      </if>
      <if test="lastShipTime != null">
        last_ship_time = #{lastShipTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payStatus != null">
        pay_status = #{payStatus,jdbcType=INTEGER},
      </if>
      <if test="chargeCurrencyCode != null">
        charge_currency_code = #{chargeCurrencyCode,jdbcType=VARCHAR},
      </if>
      <if test="chargeExchangeRate != null">
        charge_exchange_rate = #{chargeExchangeRate,jdbcType=DECIMAL},
      </if>
      <if test="tradeStatus != null">
        trade_status = #{tradeStatus,jdbcType=INTEGER},
      </if>
      <if test="grossProfit != null">
        gross_profit = #{grossProfit,jdbcType=DECIMAL},
      </if>
      <if test="estimateVolume != null">
        estimate_volume = #{estimateVolume,jdbcType=DECIMAL},
      </if>
      <if test="customerTypeName != null">
        customer_type_name = #{customerTypeName,jdbcType=VARCHAR},
      </if>
      <if test="customerGradeName != null">
        customer_grade_name = #{customerGradeName,jdbcType=VARCHAR},
      </if>
      <if test="customerTags != null">
        customer_tags = #{customerTags,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        customer_code = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerDiscount != null">
        customer_discount = #{customerDiscount,jdbcType=DECIMAL},
      </if>
      <if test="specialReminding != null">
        special_reminding = #{specialReminding,jdbcType=VARCHAR},
      </if>
      <if test="blackList != null">
        black_list = #{blackList,jdbcType=INTEGER},
      </if>
      <if test="tradeTime != null">
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="country != null">
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="district != null">
        district = #{district,jdbcType=VARCHAR},
      </if>
      <if test="town != null">
        town = #{town,jdbcType=VARCHAR},
      </if>
      <if test="zip != null">
        zip = #{zip,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="countryCode != null">
        country_code = #{countryCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        invoice_type = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="payerName != null">
        payer_name = #{payerName,jdbcType=VARCHAR},
      </if>
      <if test="payerRegno != null">
        payer_regno = #{payerRegno,jdbcType=VARCHAR},
      </if>
      <if test="payerBankAccount != null">
        payer_bank_account = #{payerBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="payerPhone != null">
        payer_phone = #{payerPhone,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payerAddress != null">
        payer_address = #{payerAddress,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceCode != null">
        invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceStatus != null">
        invoice_status = #{invoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="payerBankName != null">
        payer_bank_name = #{payerBankName,jdbcType=VARCHAR},
      </if>
      <if test="preTypedetail != null">
        pre_typedetail = #{preTypedetail,jdbcType=VARCHAR},
      </if>
      <if test="firstPayment != null">
        first_payment = #{firstPayment,jdbcType=DECIMAL},
      </if>
      <if test="finalPayment != null">
        final_payment = #{finalPayment,jdbcType=DECIMAL},
      </if>
      <if test="firstPaytime != null">
        first_paytime = #{firstPaytime,jdbcType=TIMESTAMP},
      </if>
      <if test="finalPaytime != null">
        final_paytime = #{finalPaytime,jdbcType=TIMESTAMP},
      </if>
      <if test="reviewTime != null">
        review_time = #{reviewTime,jdbcType=TIMESTAMP},
      </if>
      <if test="activationTime != null">
        activation_time = #{activationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerTotalFee != null">
        customer_total_fee = #{customerTotalFee,jdbcType=DECIMAL},
      </if>
      <if test="customerDiscountFee != null">
        customer_discount_fee = #{customerDiscountFee,jdbcType=DECIMAL},
      </if>
      <if test="notifyPickTime != null">
        notify_pick_time = #{notifyPickTime,jdbcType=TIMESTAMP},
      </if>
      <if test="consignTime != null">
        consign_time = #{consignTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="customerPostFee != null">
        customer_post_fee = #{customerPostFee,jdbcType=DECIMAL},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=VARCHAR},
      </if>
      <if test="shopCode != null">
        shop_code = #{shopCode,jdbcType=VARCHAR},
      </if>
      <if test="shopName != null">
        shop_name = #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="customerPayment != null">
        customer_payment = #{customerPayment,jdbcType=DECIMAL},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="isBillCheck != null">
        is_bill_check = #{isBillCheck,jdbcType=TINYINT},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="logisticName != null">
        logistic_name = #{logisticName,jdbcType=VARCHAR},
      </if>
      <if test="billDate != null">
        bill_date = #{billDate,jdbcType=TIMESTAMP},
      </if>
      <if test="logisticType != null">
        logistic_type = #{logisticType,jdbcType=INTEGER},
      </if>
      <if test="mainPostid != null">
        main_postid = #{mainPostid,jdbcType=VARCHAR},
      </if>
      <if test="tradeType != null">
        trade_type = #{tradeType,jdbcType=INTEGER},
      </if>
      <if test="totalFee != null">
        total_fee = #{totalFee,jdbcType=DECIMAL},
      </if>
      <if test="taxFee != null">
        tax_fee = #{taxFee,jdbcType=DECIMAL},
      </if>
      <if test="receivedPostFee != null">
        received_post_fee = #{receivedPostFee,jdbcType=DECIMAL},
      </if>
      <if test="discountFee != null">
        discount_fee = #{discountFee,jdbcType=DECIMAL},
      </if>
      <if test="payment != null">
        payment = #{payment,jdbcType=DECIMAL},
      </if>
      <if test="couponFee != null">
        coupon_fee = #{couponFee,jdbcType=DECIMAL},
      </if>
      <if test="receivedTotal != null">
        received_total = #{receivedTotal,jdbcType=DECIMAL},
      </if>
      <if test="postFee != null">
        post_fee = #{postFee,jdbcType=DECIMAL},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signingTime != null">
        signing_time = #{signingTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settleAuditTime != null">
        settle_audit_time = #{settleAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="localPayment != null">
        local_payment = #{localPayment,jdbcType=DECIMAL},
      </if>
      <if test="localExchangeRate != null">
        local_exchange_rate = #{localExchangeRate,jdbcType=DECIMAL},
      </if>
      <if test="customerAccount != null">
        customer_account = #{customerAccount,jdbcType=VARCHAR},
      </if>
      <if test="localCurrencyCode != null">
        local_currency_code = #{localCurrencyCode,jdbcType=VARCHAR},
      </if>
      <if test="platCompleteTime != null">
        plat_complete_time = #{platCompleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="shopTypeCode != null">
        shop_type_code = #{shopTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="flagIds != null">
        flag_ids = #{flagIds,jdbcType=VARCHAR},
      </if>
      <if test="flagNames != null">
        flag_names = #{flagNames,jdbcType=VARCHAR},
      </if>
      <if test="sysFlagIds != null">
        sys_flag_ids = #{sysFlagIds,jdbcType=VARCHAR},
      </if>
      <if test="sourceAfterNo != null">
        source_after_no = #{sourceAfterNo,jdbcType=VARCHAR},
      </if>
      <if test="govSubsidyAmount != null">
        gov_subsidy_amount = #{govSubsidyAmount,jdbcType=VARCHAR},
      </if>
      <if test="govSubsidyAmountMerchant != null">
        gov_subsidy_amount_merchant = #{govSubsidyAmountMerchant,jdbcType=VARCHAR},
      </if>
      <if test="ebsAmount != null">
        ebs_amount = #{ebsAmount,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDiscountRate != null">
        purchase_discount_rate = #{purchaseDiscountRate,jdbcType=DECIMAL},
      </if>
      <if test="consignCode != null">
        consign_code = #{consignCode,jdbcType=VARCHAR},
      </if>
      <if test="sendEbsStatus != null">
        send_ebs_status = #{sendEbsStatus,jdbcType=TINYINT},
      </if>
      <if test="sendEbsTime != null">
        send_ebs_time = #{sendEbsTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="branchName != null">
        branch_name = #{branchName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null">
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="govSubsidy != null">
        gov_subsidy = #{govSubsidy,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where trade_id = #{tradeId,jdbcType=BIGINT}
  </update>
  <update id="updateJkyOrderSendEbsStatusError" >
    update t_jky_order set send_ebs_status = 3, remark = #{errorMsg,jdbcType=VARCHAR}, modify_time = now()
    where trade_id = #{tradeId,jdbcType=BIGINT} and send_ebs_status = 2
  </update>
  <update id="batchUpdateStatus">
    <foreach close="" collection="list" index="index" item="item" open="" separator=";">
      update t_jky_order set trade_status = #{item.tradeStatus}, modified_by = #{user},
      <if test="item.completeTime != null">complete_time = #{item.completeTime},</if>
      <if test="item.signingTime != null">signing_time = #{item.signingTime},</if>
      modify_time = now() where trade_no = #{item.tradeNo}
    </foreach>
  </update>

</mapper>