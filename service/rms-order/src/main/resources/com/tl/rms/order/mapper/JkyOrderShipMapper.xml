<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tl.rms.order.mapper.JkyOrderShipMapper">
  <resultMap id="BaseResultMap" type="com.tl.rms.order.domain.po.JkyOrderShip">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="qty" jdbcType="INTEGER" property="qty" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="delivery_id" jdbcType="VARCHAR" property="deliveryId" />
    <result column="out_qty" jdbcType="INTEGER" property="outQty" />
    <result column="out_time" jdbcType="TIMESTAMP" property="outTime" />
    <result column="out_inv" jdbcType="VARCHAR" property="outInv" />
    <result column="out_2_ebs_status" jdbcType="TINYINT" property="out2EbsStatus" />
    <result column="out_2_ebs_time" jdbcType="TIMESTAMP" property="out2EbsTime" />
    <result column="sign_qty" jdbcType="INTEGER" property="signQty" />
    <result column="sign_time" jdbcType="TIMESTAMP" property="signTime" />
    <result column="sign_2_ebs_status" jdbcType="TINYINT" property="sign2EbsStatus" />
    <result column="sign_2_ebs_time" jdbcType="TIMESTAMP" property="sign2EbsTime" />
    <result column="in_qty" jdbcType="INTEGER" property="inQty" />
    <result column="in_locator" jdbcType="VARCHAR" property="inLocator" />
    <result column="in_time" jdbcType="TIMESTAMP" property="inTime" />
    <result column="in_code" jdbcType="VARCHAR" property="inCode" />
    <result column="in_inv" jdbcType="VARCHAR" property="inInv" />
    <result column="in_2_ebs_status" jdbcType="TINYINT" property="in2EbsStatus" />
    <result column="in_2_ebs_time" jdbcType="TIMESTAMP" property="in2EbsTime" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="cancel_detail" jdbcType="VARCHAR" property="cancelDetail" />
    <result column="cancel_2_ebs_status" jdbcType="TINYINT" property="cancel2EbsStatus" />
    <result column="cancel_2_ebs_time" jdbcType="TIMESTAMP" property="cancel2EbsTime" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="create_date" jdbcType="INTEGER" property="createDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
  </resultMap>
  <resultMap id="ExtendResultMap" type="com.tl.rms.order.domain.po.extend.JkyOrderShipExtend" extends="BaseResultMap">
    <result column="shop_code" jdbcType="VARCHAR" property="shopCode" />
    <result column="branch_name" jdbcType="VARCHAR" property="branchName" />
  </resultMap>

  <insert id="insertBatch" parameterType="java.util.List">
    insert into t_jky_order_ship (
      customer_id, order_id, qty, status, material_code, delivery_id, out_qty, out_time,
      out_inv, out_2_ebs_status, out_2_ebs_time, sign_qty, sign_time, sign_2_ebs_status,
      sign_2_ebs_time, in_qty, in_locator, in_time, in_code, in_inv, in_2_ebs_status, in_2_ebs_time, cancel_time,
      cancel_detail, cancel_2_ebs_status, cancel_2_ebs_time, warehouse_code, remark, address,
      create_date, create_time, modify_time, created_by, modified_by
    ) values
    <foreach collection="list" item="item" index="index" open="" separator="," close="">
      (
        #{item.customerId,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR},
        #{item.qty,jdbcType=INTEGER}, #{item.status,jdbcType=VARCHAR}, #{item.materialCode,jdbcType=VARCHAR},
        #{item.deliveryId,jdbcType=VARCHAR}, #{item.outQty,jdbcType=INTEGER}, #{item.outTime,jdbcType=TIMESTAMP},
        #{item.outInv,jdbcType=VARCHAR}, #{item.out2EbsStatus,jdbcType=TINYINT}, #{item.out2EbsTime,jdbcType=TIMESTAMP},
        #{item.signQty,jdbcType=INTEGER}, #{item.signTime,jdbcType=TIMESTAMP}, #{item.sign2EbsStatus,jdbcType=TINYINT},
        #{item.sign2EbsTime,jdbcType=TIMESTAMP}, #{item.inQty,jdbcType=INTEGER}, #{item.inLocator,jdbcType=VARCHAR},
        #{item.inTime,jdbcType=TIMESTAMP}, #{item.inCode,jdbcType=VARCHAR}, #{item.inInv,jdbcType=VARCHAR}, #{item.in2EbsStatus,jdbcType=TINYINT}, #{item.in2EbsTime,jdbcType=TIMESTAMP},
        #{item.cancelTime,jdbcType=TIMESTAMP}, #{item.cancelDetail,jdbcType=VARCHAR}, #{item.cancel2EbsStatus,jdbcType=TINYINT},
        #{item.cancel2EbsTime,jdbcType=TIMESTAMP}, #{item.warehouseCode,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR},
        #{item.address,jdbcType=VARCHAR}, #{item.createDate,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.modifyTime,jdbcType=TIMESTAMP}, #{item.createdBy,jdbcType=VARCHAR}, #{item.modifiedBy,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.tl.rms.order.domain.po.JkyOrderShip">
    update t_jky_order_ship
    <set>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="qty != null">
        qty = #{qty,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        material_code = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="deliveryId != null">
        delivery_id = #{deliveryId,jdbcType=VARCHAR},
      </if>
      <if test="outQty != null">
        out_qty = #{outQty,jdbcType=INTEGER},
      </if>
      <if test="outTime != null">
        out_time = #{outTime,jdbcType=TIMESTAMP},
      </if>
      <if test="outInv != null">
        out_inv = #{outInv,jdbcType=VARCHAR},
      </if>
      <if test="out2EbsStatus != null">
        out_2_ebs_status = #{out2EbsStatus,jdbcType=TINYINT},
      </if>
      <if test="out2EbsTime != null">
        out_2_ebs_time = #{out2EbsTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signQty != null">
        sign_qty = #{signQty,jdbcType=INTEGER},
      </if>
      <if test="signTime != null">
        sign_time = #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sign2EbsStatus != null">
        sign_2_ebs_status = #{sign2EbsStatus,jdbcType=TINYINT},
      </if>
      <if test="sign2EbsTime != null">
        sign_2_ebs_time = #{sign2EbsTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inQty != null">
        in_qty = #{inQty,jdbcType=INTEGER},
      </if>
      <if test="inLocator != null">
        in_locator = #{inLocator,jdbcType=VARCHAR},
      </if>
      <if test="inTime != null">
        in_time = #{inTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inCode != null">
        in_code = #{inCode,jdbcType=VARCHAR},
      </if>
      <if test="inInv != null">
        in_inv = #{inInv,jdbcType=VARCHAR},
      </if>
      <if test="in2EbsStatus != null">
        in_2_ebs_status = #{in2EbsStatus,jdbcType=TINYINT},
      </if>
      <if test="in2EbsTime != null">
        in_2_ebs_time = #{in2EbsTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTime != null">
        cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelDetail != null">
        cancel_detail = #{cancelDetail,jdbcType=VARCHAR},
      </if>
      <if test="cancel2EbsStatus != null">
        cancel_2_ebs_status = #{cancel2EbsStatus,jdbcType=TINYINT},
      </if>
      <if test="cancel2EbsTime != null">
        cancel_2_ebs_time = #{cancel2EbsTime,jdbcType=TIMESTAMP},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedBy != null">
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listByOrderId" parameterType="java.lang.String" resultMap="ExtendResultMap">
    select s.*, o.shop_code, o.branch_name from t_jky_order_ship s, t_jky_order o
    where o.trade_no like concat(substring_index(s.order_id, '-', 1), '%')
    and s.order_id = #{orderId} and o.trade_no like concat(substring_index(#{orderId}, '-', 1), '%')
  </select>

  <select id="selectByOrderAndMaterial" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_jky_order_ship
    where order_id = #{orderId} and material_code = #{materialCode}
    limit 1
  </select>

  <select id="listOrderIdByOut2Ebs" parameterType="map" resultType="java.lang.String">
    select s.order_id
    from t_jky_order_ship s
    left join t_jky_order o on o.trade_no = s.order_id
    where s.create_date >= date_format(date_sub(now(),interval 30 day),'%Y%m%d')
    and s.out_2_ebs_status = 2
    and o.send_ebs_status = 1
    group by s.order_id
    <if test="offset != null and limit != null">
      limit #{offset},#{limit}
    </if>
  </select>

  <select id="listOrderIdBySign2Ebs" parameterType="map" resultType="java.lang.String">
    select order_id
    from t_jky_order_ship
    where create_date >= date_format(date_sub(now(),interval 30 day),'%Y%m%d')
    and out_2_ebs_status = 1 and sign_2_ebs_status = 2
    group by order_id
    <if test="offset != null and limit != null">
      limit #{offset},#{limit}
    </if>
  </select>

  <select id="listOrderIdByIn2Ebs" parameterType="map" resultType="java.lang.String">
    select order_id
    from t_jky_order_ship
    where create_date >= date_format(date_sub(now(),interval 30 day),'%Y%m%d')
    and in_2_ebs_status = 2
    group by order_id
    <if test="offset != null and limit != null">
      limit #{offset},#{limit}
    </if>
  </select>

  <select id="listByInboundReturn" parameterType="map" resultMap="BaseResultMap">
    select o.trade_no order_id, s.* from t_jky_order_ship s
    join t_jky_return r on r.trade_no = s.order_id
    join t_jky_order o on o.source_after_no = r.return_change_no
    join t_jky_order_goods g on g.trade_id = o.trade_id and g.barcode = s.material_code
    where o.create_date >= date_format(date_sub(now(), interval 30 day), '%Y%m%d')
    and o.send_ebs_status = 1 and o.trade_type = 8 and s.in_2_ebs_status = 3
    <if test="offset != null and limit != null">
      limit #{offset},#{limit}
    </if>
  </select>

  <update id="updateCancelLineDoNotSendEbs">
    update t_jky_order_ship
    set cancel_2_ebs_status = 0, modify_time = now()
    where id in (
      select t.id from (
        select s.id
        from t_jky_order_ship s
        left join t_jky_order o on o.trade_no = s.order_id
        where s.create_date >= date_format(date_sub(now(), interval 30 day), '%Y%m%d')
          and o.send_ebs_status = 0
          and s.cancel_2_ebs_status = 2
      ) t
    )
  </update>

  <select id="listOrderIdByCancelLine2EbsEqual" parameterType="map" resultType="java.lang.String">
    select s.order_id
    from t_jky_order_ship s
    left join t_jky_order o on o.trade_no = s.order_id
    where s.create_date >= date_format(date_sub(now(),interval 30 day),'%Y%m%d')
    and o.send_ebs_status = 1
    and s.cancel_2_ebs_status = 2
    group by s.order_id
    <if test="offset != null and limit != null">
      limit #{offset},#{limit}
    </if>
  </select>

  <select id="listOrderIdByCancelLine2EbsLike" parameterType="map" resultType="java.lang.String">
    select s.order_id
    from t_jky_order_ship s,t_jky_order o
    where o.trade_no like concat(substring_index(s.order_id, '-', 1), '%')
    and o.trade_no != s.order_id
    and s.create_date >= date_format(date_sub(now(),interval 30 day),'%Y%m%d')
    and o.send_ebs_status = 1
    and s.cancel_2_ebs_status = 2
    group by s.order_id
    <if test="offset != null and limit != null">
      limit #{offset},#{limit}
    </if>
  </select>

  <update id="updateOut2EbsStatus" parameterType="java.util.List">
    update t_jky_order_ship
    set out_2_ebs_status = 1,
        out_2_ebs_time = now(),
        modify_time = now()
    where id in <foreach collection="list" item="item" open="(" separator=","  close=")">#{item}</foreach>
  </update>

  <update id="updateSign2EbsStatus" parameterType="java.util.List">
    update t_jky_order_ship
    set sign_2_ebs_status = 1,
        sign_2_ebs_time = now(),
        modify_time = now()
    where id in <foreach collection="list" item="item" open="(" separator=","  close=")">#{item}</foreach>
  </update>

  <update id="updateIn2EbsStatus" parameterType="java.util.List">
    update t_jky_order_ship
    set in_2_ebs_status = 1,
        in_2_ebs_time = now(),
        modify_time = now()
    where id in <foreach collection="list" item="item" open="(" separator=","  close=")">#{item}</foreach>
  </update>

  <update id="updateIn2EbsStatus4Return" parameterType="java.util.List">
    update t_jky_order_ship
    set in_2_ebs_status = 0,
        modify_time = now()
    where id in <foreach collection="list" item="item" open="(" separator=","  close=")">#{item}</foreach>
  </update>

  <update id="updateCancelLine2EbsStatus" parameterType="java.util.List">
    update t_jky_order_ship
    set cancel_2_ebs_status = 1,
        cancel_2_ebs_time = now(),
        modify_time = now()
    where id in <foreach collection="list" item="item" open="(" separator=","  close=")">#{item}</foreach>
  </update>

  <select id="listByOriginalOrderId" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT * FROM t_jky_order_ship WHERE order_id =  #{orderId}
  </select>
</mapper>