<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tl.rms.order.mapper.JkyOrderGoodsMapper">
  <resultMap id="OrderGoodsEbsResultMap" type="com.tl.rms.order.domain.vo.JkyOrderGoodsEbsVO">
    <result column="barcode" jdbcType="VARCHAR" property="barcode" />
    <result column="sell_count" jdbcType="DECIMAL" property="sellCount" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="customer_trade_no" jdbcType="VARCHAR" property="customerTradeNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="tax_rate" jdbcType="DECIMAL" property="taxRate" />
    <result column="share_favourable_after_fee" jdbcType="DECIMAL" property="shareFavourableAfterFee" />
  </resultMap>
  <select id="listOrderGoodsEbsByTradeId" parameterType="java.lang.Long" resultMap="OrderGoodsEbsResultMap">
    select barcode, sell_count, unit, customer_trade_no, tax_rate, share_favourable_after_fee, goods_name
    from t_jky_order_goods where trade_id = #{tradeId,jdbcType=BIGINT}
  </select>

  <select id="listSubTradeIdByTradeId" parameterType="java.lang.Long" resultType="java.lang.Long">
    SELECT sub_trade_id FROM t_jky_order_goods WHERE trade_id = #{tradeId,jdbcType=BIGINT}
  </select>

</mapper>