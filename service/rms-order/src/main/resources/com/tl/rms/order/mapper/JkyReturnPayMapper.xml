<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tl.rms.order.mapper.JkyReturnPayMapper">
  <delete id="deleteByTradeAfterIds" parameterType="java.lang.Long">
    delete from t_jky_return_pay where trade_after_id in
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">#{item}</foreach>
  </delete>
  <insert id="batchInsert">
    insert into t_jky_return_pay (trade_after_id, return_accounts,
      send_accounts, receive_post_fee, return_total, 
      settlement_currency, discount_fee, pay_account, 
      customer_bank_name, pay_status, pay_accounts, 
      pay_time, payee, customer_account_type, 
      customer_bank_account, resend_type, settlement_type, 
      refund_type, refund_type_code, settlement_person, 
      settlement_person_id, settlement_string, 
      gmt_create, gmt_modified, settlement_currency_code, 
      pay_account_id, payment_no, created_by, modified_by)
    values
    <foreach collection="list" item="item" index="index" separator=",">
     (#{item.tradeAfterId,jdbcType=BIGINT}, #{item.returnAccounts,jdbcType=DECIMAL},
      #{item.sendAccounts,jdbcType=DECIMAL}, #{item.receivePostFee,jdbcType=DECIMAL}, #{item.returnTotal,jdbcType=DECIMAL}, 
      #{item.settlementCurrency,jdbcType=VARCHAR}, #{item.discountFee,jdbcType=DECIMAL}, #{item.payAccount,jdbcType=VARCHAR}, 
      #{item.customerBankName,jdbcType=VARCHAR}, #{item.payStatus,jdbcType=TINYINT}, #{item.payAccounts,jdbcType=DECIMAL}, 
      #{item.payTime,jdbcType=TIMESTAMP}, #{item.payee,jdbcType=VARCHAR}, #{item.customerAccountType,jdbcType=TINYINT}, 
      #{item.customerBankAccount,jdbcType=VARCHAR}, #{item.resendType,jdbcType=TINYINT}, #{item.settlementType,jdbcType=VARCHAR}, 
      #{item.refundType,jdbcType=VARCHAR}, #{item.refundTypeCode,jdbcType=TINYINT}, #{item.settlementPerson,jdbcType=VARCHAR}, 
      #{item.settlementPersonId,jdbcType=BIGINT}, #{item.settlementString,jdbcType=TIMESTAMP}, 
      #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.settlementCurrencyCode,jdbcType=VARCHAR}, 
      #{item.payAccountId,jdbcType=BIGINT}, #{item.paymentNo,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR}, #{item.modifiedBy,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>