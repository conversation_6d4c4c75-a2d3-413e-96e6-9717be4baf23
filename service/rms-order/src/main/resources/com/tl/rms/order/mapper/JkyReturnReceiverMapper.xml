<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tl.rms.order.mapper.JkyReturnReceiverMapper">
  <delete id="deleteByTradeAfterIds" parameterType="java.lang.Long">
    delete from t_jky_return_receiver where trade_after_id in
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">#{item}</foreach>
  </delete>
  <insert id="batchInsert">
    insert into t_jky_return_receiver (trade_after_id, customer_id,
      customer_name, customer_account, customer_code, 
      zip, country, state, 
      city, district, town, 
      gmt_modified, gmt_create, created_by, modified_by)
    values
    <foreach collection="list" item="item" index="index" separator=",">
     (#{item.tradeAfterId,jdbcType=BIGINT}, #{item.customerId,jdbcType=BIGINT},
      #{item.customerName,jdbcType=VARCHAR}, #{item.customerAccount,jdbcType=VARCHAR}, #{item.customerCode,jdbcType=VARCHAR}, 
      #{item.zip,jdbcType=VARCHAR}, #{item.country,jdbcType=VARCHAR}, #{item.state,jdbcType=VARCHAR}, 
      #{item.city,jdbcType=VARCHAR}, #{item.district,jdbcType=VARCHAR}, #{item.town,jdbcType=VARCHAR}, 
      #{item.gmtModified,jdbcType=TIMESTAMP}, #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.createdBy,jdbcType=VARCHAR}, #{item.modifiedBy,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>