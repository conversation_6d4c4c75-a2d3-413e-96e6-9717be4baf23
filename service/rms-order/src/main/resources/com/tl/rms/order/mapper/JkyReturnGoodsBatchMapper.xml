<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tl.rms.order.mapper.JkyReturnGoodsBatchMapper">
  <delete id="deleteByTradeAfterIds" parameterType="java.lang.Long">
    delete from t_jky_return_goods_batch where trade_after_id in
    <foreach collection="list" index="index" item="item" open="(" separator="," close=")">#{item}</foreach>
  </delete>
  <insert id="batchInsert">
    insert into t_jky_return_goods_batch (sub_trade_id, trade_after_id,
      batch_no, batch_amount, production_date, 
      expiration_date, created_by, modified_by)
    values
    <foreach collection="list" item="item" index="index" separator=",">
     (#{item.subTradeId,jdbcType=BIGINT}, #{item.tradeAfterId,jdbcType=BIGINT},
      #{item.batchNo,jdbcType=VARCHAR}, #{item.batchAmount,jdbcType=INTEGER}, #{item.productionDate,jdbcType=TIMESTAMP}, 
      #{item.expirationDate,jdbcType=TIMESTAMP}, #{item.createdBy,jdbcType=VARCHAR}, #{item.modifiedBy,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>