<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tl.rms.order.mapper.JkyOrderPurchaseDiscountRateMapper">

  <resultMap id="BaseResultMap" type="com.tl.rms.order.domain.po.JkyOrderPurchaseDiscountRate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_code" jdbcType="VARCHAR" property="shopCode" />
    <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
    <result column="model_name" jdbcType="VARCHAR" property="modelName" />
    <result column="rate" jdbcType="DECIMAL" property="rate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="modified_by" jdbcType="VARCHAR" property="modifiedBy" />
  </resultMap>

  <select id="listRate" resultMap="BaseResultMap">
    select concat(shop_code, ifnull(model_name, '')) shop_code, rate from t_jky_order_purchase_discount_rate
  </select>

  <select id="getRateByShopAndModel" resultType="java.math.BigDecimal">
    select rate from t_jky_order_purchase_discount_rate where shop_code = #{shopCode} and model_name = #{modelName} limit 1
  </select>

  <select id="getRateByShop" resultType="java.math.BigDecimal">
    select rate from t_jky_order_purchase_discount_rate where shop_code = #{shopCode} limit 1
  </select>

</mapper>