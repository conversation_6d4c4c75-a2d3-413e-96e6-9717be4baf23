-- 数据库: tl-mysql-m-wr-rms.pttl.com:3352/rms_order

CREATE TABLE `t_jky_online_order`
(
    `id`                   bigint       NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `sys_trade_id_string`  varchar(255) NOT NULL COMMENT '销售单唯一ID（主订单标识）',
    `logistic_no`          varchar(255)   DEFAULT NULL COMMENT '物流单号',
    `gmt_modified`         datetime       DEFAULT NULL COMMENT '系统更新时间（字符串存储）',
    `pay_time`             datetime   DEFAULT NULL COMMENT '付款时间（字符串存储）',
    `shop_name`            varchar(255)   DEFAULT NULL COMMENT '销售渠道（如api-蚂蚁销客）',
    `pay_no`               varchar(255)   DEFAULT NULL COMMENT '支付单号',
    `online_modified`      datetime       DEFAULT NULL COMMENT '线上修改时间（字符串存储）',
    `snd_dead_line`        datetime       DEFAULT NULL COMMENT '承诺发货时间（字符串存储）',
    `payment`              decimal(10, 2) DEFAULT NULL COMMENT '应收合计',
    `shop_id`              varchar(255)   DEFAULT NULL COMMENT '店铺ID',
    `tax_fee`              decimal(10, 2) DEFAULT NULL COMMENT '税额',
    `sys_flag_ids`         varchar(255)   DEFAULT NULL COMMENT '订单标记ID（JSON数组字符串，如["1","2"]）',
    `sys_flag_explain`     varchar(255)   DEFAULT NULL COMMENT '订单标记（如["退款","测试"]）',
    `trade_status_explain` varchar(255)   DEFAULT NULL COMMENT '订单状态描述（如待买家确认收货）',
    `commission_fee`       decimal(10, 2) DEFAULT NULL COMMENT '佣金',
    `trade_no`             varchar(255) NOT NULL COMMENT '网店订单号',
    `complete_time`        datetime       DEFAULT NULL COMMENT '交易完成时间（字符串存储）',
    `gmt_create`           datetime       DEFAULT NULL COMMENT '系统创建时间（字符串存储）',
    `shop_coupon_fee`      decimal(10, 2) DEFAULT NULL COMMENT '网店优惠金额',
    `syn_status_explain`   varchar(255)   DEFAULT NULL COMMENT '发货状态描述（如未上传）',
    `goods_count`          decimal(10, 0) DEFAULT NULL COMMENT '商品总数量',
    `logistic_name`        varchar(255)   DEFAULT NULL COMMENT '物流名称（如顺丰速运）',
    `total_fee`            decimal(10, 2) DEFAULT NULL COMMENT '贷款合计',
    `plat_coupon_fee`      decimal(10, 2) DEFAULT NULL COMMENT '平台优惠金额',
    `seller_memo`          text COMMENT '客服备注（长文本）',
    `cur_status_explain`   varchar(255)   DEFAULT NULL COMMENT '处理状态描述（如待上传物流信息）',
    `buyer_memo`           text COMMENT '客户备注（长文本）',
    `context_id`           varchar(255)   DEFAULT NULL COMMENT '接口上下文ID（用于追踪）',
    `create_by`            bigint         DEFAULT NULL COMMENT '创建人',
    `create_time`          datetime       DEFAULT NULL COMMENT '下单时间（字符串存储）',
    `update_by`            bigint         DEFAULT NULL COMMENT '更新人',
    `update_time`          datetime       DEFAULT NULL COMMENT '更新时间',
    `deleted`              tinyint        DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_trade_no` (`trade_no`),
    KEY `idx_shop_id` (`shop_id`),
    KEY `idx_create_time` (`create_time`) COMMENT '按下单时间查询索引（字符串索引）'
) ENGINE=InnoDB CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='吉客云对接网店订单主表';

CREATE TABLE `t_jky_online_order_goods`
(
    `id`                  bigint       NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `sys_trade_id_string` varchar(255) NOT NULL COMMENT '关联的销售单ID（外键，关联order_main表）',
    `trade_no`            varchar(255) NOT NULL COMMENT '网店订单号',
    `discount_fee`        decimal(10, 2) DEFAULT NULL COMMENT '商品优惠金额',
    `sell_total`          decimal(10, 2) DEFAULT NULL COMMENT '商品总金额',
    `sys_goods_id`        bigint         DEFAULT NULL COMMENT '系统商品ID',
    `refund_status`       varchar(255)   DEFAULT NULL COMMENT '退款状态（如NO_REFUND）',
    `sell_price`          decimal(10, 2) DEFAULT NULL COMMENT '商品单价',
    `plat_sku_id`         varchar(255)   DEFAULT NULL COMMENT '平台规格ID',
    `syn_cause`           varchar(255)   DEFAULT NULL COMMENT '发货状态说明（如未发货）',
    `sys_unit`            varchar(255)   DEFAULT NULL COMMENT '系统单位',
    `is_gift`             tinyint(1)     DEFAULT NULL COMMENT '是否赠品（1:是 0:否）',
    `plat_warehouse_code` varchar(255)   DEFAULT NULL COMMENT '平台指定仓库',
    `goods_name`          text COMMENT '商品名称（长文本）',
    `palt_logistic_code`  varchar(255)   DEFAULT NULL COMMENT '平台指定物流',
    `goods_memo`          text COMMENT '商品交易备注（长文本）',
    `goods_spec`          text COMMENT '商品交易规格（长文本）',
    `sub_trade_id`        varchar(255)   DEFAULT NULL COMMENT '网店子订单ID',
    `send_time`           varchar(255)   DEFAULT NULL COMMENT '发货时间（字符串存储）',
    `plat_goods_id`       varchar(255)   DEFAULT NULL COMMENT '平台商品ID',
    `sub_trade_no`        varchar(255)   DEFAULT NULL COMMENT '网店子单号',
    `sys_spec_id`         bigint         DEFAULT NULL COMMENT '系统规格ID',
    `sell_count`          decimal(10, 0) DEFAULT NULL COMMENT '商品数量',
    `outer_id`            varchar(255)   DEFAULT NULL COMMENT '商品商家编码',
    `goods_barcode`       varchar(255)   DEFAULT NULL COMMENT '货品编码',
    `status`              varchar(255)   DEFAULT NULL COMMENT '商品交易状态（如OTHER）',
    `create_by`           bigint         DEFAULT NULL COMMENT '创建人',
    `create_time`         datetime       DEFAULT NULL COMMENT '创建时间',
    `update_by`           bigint         DEFAULT NULL COMMENT '更新人',
    `update_time`         datetime       DEFAULT NULL COMMENT '更新时间',
    `deleted`             tinyint        DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_trade_no` (`trade_no`),
    KEY `jky_order_goods_detail_trade` (`sys_trade_id_string`),
    KEY `idx_plat_goods_id` (`plat_goods_id`),
    KEY `idx_sub_trade_no` (`sub_trade_no`)
) ENGINE=InnoDB CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='吉客云对接网店订单商品详情子表';


-- 出库单主表
CREATE TABLE `t_delivery_order`
(
    `id`                      bigint       NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `delivery_order_code`     varchar(50)  NOT NULL COMMENT '出库单号',
    `pre_delivery_order_code` varchar(50)    DEFAULT NULL COMMENT '原出库单号（ERP 分配）',
    `pre_delivery_order_id`   varchar(50)    DEFAULT NULL COMMENT '原出库单号（WMS 分配）',
    `order_type`              varchar(50)  NOT NULL COMMENT '出库单类型 JYCK=一般交易出库单, HHCK=换货出库单, BFCK=补发出库单，QTCK=其他出库单',
    `warehouse_code`          varchar(50)  NOT NULL COMMENT '仓库编码',
    `source_platform_code`    varchar(50)  NOT NULL COMMENT '订单来源平台编码 TB=淘宝、TM=天猫、JD=京东等',
    `source_platform_name`    varchar(200)   DEFAULT NULL COMMENT '订单来源平台名称',
    `create_time`             datetime     NOT NULL COMMENT '发货单创建时间',
    `latest_delivery_time`    datetime       DEFAULT NULL COMMENT '最晚发货时间',
    `latest_collection_time`  datetime       DEFAULT NULL COMMENT '最晚揽收时间',
    `place_order_time`        datetime     NOT NULL COMMENT '前台订单创建时间(下单时间)',
    `pay_time`                datetime       DEFAULT NULL COMMENT '订单支付时间',
    `pay_no`                  varchar(50)    DEFAULT NULL COMMENT '支付平台交易号',
    `service_code`            varchar(50)    DEFAULT NULL COMMENT '服务编码 NCWLJH=集包',
    `operator_code`           varchar(50)    DEFAULT NULL COMMENT '操作员(审核员)编码',
    `operator_name`           varchar(50)    DEFAULT NULL COMMENT '操作员(审核员)名称',
    `operate_time`            datetime     NOT NULL COMMENT '操作(审核)时间',
    `shop_nick`               varchar(200) NOT NULL COMMENT '店铺名称',
    `seller_nick`             varchar(200)   DEFAULT NULL COMMENT '卖家名称',
    `buyer_nick`              varchar(200)   DEFAULT NULL COMMENT '买家昵称',
    `total_amount`            decimal(18, 2) DEFAULT NULL COMMENT '订单总金额(元)',
    `item_amount`             decimal(18, 2) DEFAULT NULL COMMENT '商品总金额(元)',
    `discount_amount`         decimal(18, 2) DEFAULT NULL COMMENT '订单折扣金额(元)',
    `freight`                 decimal(18, 2) DEFAULT NULL COMMENT '快递费用(元)',
    `ar_amount`               decimal(18, 2) DEFAULT NULL COMMENT '应收金额(元)',
    `got_amount`              decimal(18, 2) DEFAULT NULL COMMENT '已收金额(元)',
    `service_fee`             decimal(18, 2) DEFAULT NULL COMMENT 'COD服务费',
    `logistics_code`          varchar(50)  NOT NULL COMMENT '物流公司编码 SF=顺丰、EMS=标准快递等',
    `logistics_name`          varchar(200)   DEFAULT NULL COMMENT '物流公司名称',
    `express_code`            varchar(50)    DEFAULT NULL COMMENT '运单号',
    `logistics_area_code`     varchar(50)    DEFAULT NULL COMMENT '快递区域编码,大头笔信息',
    `schedule_type`           int            DEFAULT NULL COMMENT '投递时延要求 1=工作日,2=节假日,101=当日达,102=次晨达,103=次日达,104=预约达',
    `schedule_day`            date           DEFAULT NULL COMMENT '要求送达日期',
    `schedule_start_time`     time           DEFAULT NULL COMMENT '投递时间范围要求(开始时间)',
    `schedule_end_time`       time           DEFAULT NULL COMMENT '投递时间范围要求(结束时间)',
    `delivery_type`           varchar(50)    DEFAULT NULL COMMENT '发货服务类型 PTPS（普通配送），LLPS（冷链配送），HBP(环保配)',
    `is_urgency`              char(1)        DEFAULT 'N' COMMENT '是否紧急 Y/N',
    `invoice_flag`            char(1)        DEFAULT 'N' COMMENT '是否需要发票 Y/N',
    `insurance_flag`          char(1)        DEFAULT 'N' COMMENT '是否需要保险 Y/N',
    `buyer_message`           varchar(500)   DEFAULT NULL COMMENT '买家留言',
    `seller_message`          varchar(500)   DEFAULT NULL COMMENT '卖家留言',
    `remark`                  varchar(500)   DEFAULT NULL COMMENT '备注',
    `create_by`               bigint COMMENT '创建人',
    `update_by`               bigint COMMENT '更新人',
    `update_time`             datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`                 tinyint        DEFAULT 0 COMMENT '是否删除 0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_delivery_order_code` (`delivery_order_code`)
) ENGINE=InnoDB CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='出库单主表';

-- 出库单发件人信息表
CREATE TABLE `t_delivery_order_sender`
(
    `id`                bigint       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `delivery_order_id` bigint       NOT NULL COMMENT '出库单ID',
    `company`           varchar(200) DEFAULT NULL COMMENT '公司名称',
    `name`              varchar(50)  NOT NULL COMMENT '姓名',
    `zip_code`          varchar(50)  DEFAULT NULL COMMENT '邮编',
    `tel`               varchar(50)  DEFAULT NULL COMMENT '固定电话',
    `mobile`            varchar(50)  NOT NULL COMMENT '移动电话',
    `email`             varchar(50)  DEFAULT NULL COMMENT '电子邮箱',
    `country_code`      varchar(50)  DEFAULT NULL COMMENT '国家二字码',
    `province`          varchar(50)  NOT NULL COMMENT '省份',
    `city`              varchar(50)  NOT NULL COMMENT '城市',
    `area`              varchar(50)  DEFAULT NULL COMMENT '区域',
    `town`              varchar(50)  DEFAULT NULL COMMENT '村镇',
    `detail_address`    varchar(200) NOT NULL COMMENT '详细地址',
    `create_by`         bigint       DEFAULT NULL COMMENT '创建人',
    `create_time`       datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`         bigint       DEFAULT NULL COMMENT '更新人',
    `update_time`       datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`           tinyint      DEFAULT 0 COMMENT '是否删除 0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    KEY                 `idx_delivery_order_id` (`delivery_order_id`)
) ENGINE=InnoDB CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='出库单发件人信息表';

-- 出库单收件人信息表
CREATE TABLE `t_delivery_order_receiver`
(
    `id`                bigint       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `delivery_order_id` bigint       NOT NULL COMMENT '出库单ID',
    `oaid`              varchar(100)  DEFAULT NULL COMMENT '订单收件人ID',
    `company`           varchar(200) DEFAULT NULL COMMENT '公司名称',
    `name`              varchar(50)  NOT NULL COMMENT '姓名',
    `zip_code`          varchar(50)  DEFAULT NULL COMMENT '邮编',
    `tel`               varchar(50)  DEFAULT NULL COMMENT '固定电话',
    `mobile`            varchar(50)  NOT NULL COMMENT '移动电话',
    `id_type`           varchar(50)  DEFAULT NULL COMMENT '收件人证件类型 1-身份证 2-军官证 3-护照 4-其他',
    `id_number`         varchar(50)  DEFAULT NULL COMMENT '收件人证件号码',
    `email`             varchar(50)  DEFAULT NULL COMMENT '电子邮箱',
    `country_code`      varchar(50)  DEFAULT NULL COMMENT '国家二字码',
    `province`          varchar(50)  NOT NULL COMMENT '省份',
    `city`              varchar(50)  NOT NULL COMMENT '城市',
    `area`              varchar(50)  DEFAULT NULL COMMENT '区域',
    `town`              varchar(50)  DEFAULT NULL COMMENT '村镇',
    `detail_address`    varchar(200) NOT NULL COMMENT '详细地址',
    `create_by`         bigint       DEFAULT NULL COMMENT '创建人',
    `create_time`       datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`         bigint       DEFAULT NULL COMMENT '更新人',
    `update_time`       datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`           tinyint      DEFAULT 0 COMMENT '是否删除 0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    KEY                 `idx_delivery_order_id` (`delivery_order_id`)
) ENGINE=InnoDB CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='出库单收件人信息表';

-- 出库单发票信息表
CREATE TABLE `t_delivery_order_invoice`
(
    `id`                bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `delivery_order_id` bigint NOT NULL COMMENT '出库单ID',
    `type`              varchar(50)    DEFAULT NULL COMMENT '发票类型 INVOICE=普通发票，VINVOICE=增值税普通发票, EVINVOICE=电子增票',
    `header`            varchar(200)   DEFAULT NULL COMMENT '发票抬头',
    `tax_number`        varchar(50)    DEFAULT NULL COMMENT '税号',
    `amount`            decimal(18, 2) DEFAULT NULL COMMENT '发票总金额',
    `content`           varchar(500)   DEFAULT NULL COMMENT '发票内容',
    `create_by`         bigint         DEFAULT NULL COMMENT '创建人',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_by`         bigint         DEFAULT NULL COMMENT '更新人',
    `update_time`       datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`           tinyint        DEFAULT 0 COMMENT '是否删除 0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    KEY                 `idx_delivery_order_id` (`delivery_order_id`)
) ENGINE=InnoDB CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='出库单发票信息表';

-- 出库单发票明细表
CREATE TABLE `t_delivery_order_invoice_detail`
(
    `id`          bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `invoice_id`  bigint NOT NULL COMMENT '发票ID',
    `item_name`   varchar(50)    DEFAULT NULL COMMENT '商品名称',
    `unit`        varchar(50)    DEFAULT NULL COMMENT '商品单位',
    `price`       decimal(18, 2) DEFAULT NULL COMMENT '商品单价',
    `quantity`    int            DEFAULT NULL COMMENT '数量',
    `amount`      decimal(18, 2) DEFAULT NULL COMMENT '金额',
    `create_by`   bigint         DEFAULT NULL COMMENT '创建人',
    `create_time` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_by`   bigint         DEFAULT NULL COMMENT '更新人',
    `update_time` datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`     tinyint        DEFAULT 0 COMMENT '是否删除 0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    KEY           `idx_invoice_id` (`invoice_id`)
) ENGINE=InnoDB CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='出库单发票明细表';

-- 出库单保险信息表
CREATE TABLE `t_delivery_order_insurance`
(
    `id`                bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `delivery_order_id` bigint NOT NULL COMMENT '出库单ID',
    `type`              varchar(50)    DEFAULT NULL COMMENT '保险类型',
    `amount`            decimal(18, 2) DEFAULT NULL COMMENT '保险金额',
    `create_by`         bigint         DEFAULT NULL COMMENT '创建人',
    `create_time`       datetime       DEFAULT NULL COMMENT '创建时间',
    `update_by`         bigint         DEFAULT NULL COMMENT '更新人',
    `update_time`       datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`           tinyint        DEFAULT 0 COMMENT '是否删除 0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    KEY                 `idx_delivery_order_id` (`delivery_order_id`)
) ENGINE=InnoDB CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='出库单保险信息表';

-- 出库单订单行表
CREATE TABLE `t_delivery_order_line`
(
    `id`                    bigint         NOT NULL AUTO_INCREMENT COMMENT '主键',
    `delivery_order_id`     bigint         NOT NULL COMMENT '出库单ID',
    `order_line_no`         varchar(50)    DEFAULT NULL COMMENT '单据行号',
    `source_order_code`     varchar(50)    DEFAULT NULL COMMENT '交易平台订单编码',
    `sub_source_order_code` varchar(50)    DEFAULT NULL COMMENT '交易平台子订单编码',
    `pay_no`                varchar(50)    DEFAULT NULL COMMENT '支付平台交易号',
    `owner_code`            varchar(50)    NOT NULL COMMENT '货主编码',
    `item_code`             varchar(50)    NOT NULL COMMENT '商品编码',
    `item_id`               varchar(50)    DEFAULT NULL COMMENT '仓储系统商品编码',
    `inventory_type`        varchar(50)    DEFAULT NULL COMMENT '库存类型 ZP=正品, CC=残次, JS=机损, XS=箱损, ZT=在途库存，DJ=冻结',
    `item_name`             varchar(200)   DEFAULT NULL COMMENT '商品名称',
    `ext_code`              varchar(50)    DEFAULT NULL COMMENT '交易平台商品编码',
    `plan_qty`              int            NOT NULL COMMENT '应发商品数量',
    `retail_price`          decimal(18, 2) DEFAULT NULL COMMENT '零售价',
    `actual_price`          decimal(18, 2) NOT NULL COMMENT '实际成交价',
    `discount_amount`       decimal(18, 2) DEFAULT NULL COMMENT '单件商品折扣金额',
    `batch_code`            varchar(50)    DEFAULT NULL COMMENT '批次编码',
    `produce_code`          varchar(50)    DEFAULT NULL COMMENT '生产批号',
    `product_date`          date           DEFAULT NULL COMMENT '生产日期',
    `expire_date`           date           DEFAULT NULL COMMENT '过期日期',
    `create_by`             bigint         DEFAULT NULL COMMENT '创建人',
    `create_time`           datetime       DEFAULT NULL COMMENT '创建时间',
    `update_by`             bigint         DEFAULT NULL COMMENT '更新人',
    `update_time`           datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`               tinyint        DEFAULT 0 COMMENT '是否删除 0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    KEY                     `idx_delivery_order_id` (`delivery_order_id`)
) ENGINE=InnoDB CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT='出库单订单行表';