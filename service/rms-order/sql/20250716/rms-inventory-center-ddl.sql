-- 数据库: tl-mysql-m-wr-rms.pttl.com:3352/rms_order

-- 库存中心 Start
ALTER TABLE `rms_order`.`t_material`
    ADD COLUMN `is_distribution_support` int NOT NULL DEFAULT 0 COMMENT '是否支持分货' AFTER `warehouse_name`,
    ADD COLUMN `item_code`               varchar(40) DEFAULT NULL COMMENT 'ITEM码' AFTER `gb_code`;

CREATE TABLE `t_distribution`
(
    `id`                      bigint      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `no`                      varchar(40) NOT NULL COMMENT '分货单号 FHD+年月日+流水号',
    `industry_id`             varchar(40)  DEFAULT NULL COMMENT '产业ID',
    `industry_name`           varchar(255) DEFAULT NULL COMMENT '产业名称',
    `type`                    varchar(20)  DEFAULT NULL COMMENT '分货类型 MATERIAL-物料 SKU-SKU',
    `effective_time`          datetime     DEFAULT NULL COMMENT '生效时间',
    `invalid_time`            datetime     DEFAULT NULL COMMENT '失效时间',
    `status`                  varchar(20)  DEFAULT NULL COMMENT '状态 PENDING-待创建 INVALID-已失效 VALID-生效中',
    `pre_id`                  bigint       DEFAULT NULL COMMENT '上一批次分货单ID',
    `is_use_pre_distribution` tinyint      DEFAULT '0' COMMENT '是否沿用上一次分货单 0-否 1-是',
    `create_by`               bigint       DEFAULT NULL COMMENT '创建人',
    `create_time`             datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`               bigint       DEFAULT NULL COMMENT '更新人',
    `update_time`             datetime     DEFAULT NULL COMMENT '更新时间',
    `deleted`                 tinyint      DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_no` (`no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分货单主表';

CREATE TABLE `t_distribution_line`
(
    `id`                 bigint      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `distribution_id`    bigint      NOT NULL COMMENT '分货单ID',
    `distribution_no`    varchar(40) NOT NULL COMMENT '分货单号(冗余)',
    `industry_id`        varchar(40)  DEFAULT NULL COMMENT '产业ID(TODO 待确认是否需要冗余)',
    `industry_name`      varchar(255) DEFAULT NULL COMMENT '产业名称(TODO 待确认是否需要冗余)',
    `category_id`        varchar(40)  DEFAULT NULL COMMENT '品类ID',
    `category_name`      varchar(255) DEFAULT NULL COMMENT '品类名称',
    `gb_code`            varchar(40)  DEFAULT NULL COMMENT '国标码',
    `material_code`      varchar(40) NOT NULL COMMENT '物料编码',
    `material_name`      varchar(255) DEFAULT NULL COMMENT '物料名称',
    `item_code`          varchar(40)  DEFAULT NULL COMMENT 'ITEM码',
    `model_id`           varchar(40)  DEFAULT NULL COMMENT '机型ID',
    `model_name`         varchar(255) DEFAULT NULL COMMENT '机型名称',
    `color`              varchar(255) DEFAULT NULL COMMENT '颜色',
    `project_id`         varchar(40)  DEFAULT NULL COMMENT '项目ID',
    `project_name`       varchar(255) DEFAULT NULL COMMENT '项目名称',
    `available_quantity` int          DEFAULT NULL COMMENT '可用库存',
    `reserved_quantity`  int          DEFAULT NULL COMMENT '预留库存',
    `create_by`          bigint       DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`          bigint       DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime     DEFAULT NULL COMMENT '更新时间',
    `deleted`            tinyint      DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    KEY                  `idx_distribution_id` (`distribution_id`),
    KEY                  `idx_distribution_no` (`distribution_no`),
    KEY                  `idx_gb_code` (`gb_code`),
    KEY                  `idx_material_code` (`material_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分货单行表';

-- 增加列
ALTER TABLE `rms_order`.`t_distribution_line`
    ADD COLUMN `warehouse_names` varchar(255) DEFAULT NULL COMMENT '仓库名称' AFTER `available_quantity`;
ALTER TABLE `rms_order`.`t_distribution_line`
    ADD COLUMN `product_series_id` varchar(40) DEFAULT NULL COMMENT '产品系列' AFTER `project_name`;
ALTER TABLE `rms_order`.`t_distribution_line`
    ADD COLUMN `product_series_name` varchar(255) DEFAULT NULL COMMENT '产品系列名称' AFTER `product_series_id`;

CREATE TABLE `t_distribution_line_shop`
(
    `id`                            bigint      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `distribution_id`               bigint      NOT NULL COMMENT '分货单ID',
    `distribution_no`               varchar(40) NOT NULL COMMENT '分货单号(冗余)',
    `distribution_line_id`          bigint      NOT NULL COMMENT '分货单行项目ID',
    `gb_code`                       varchar(40) NOT NULL COMMENT '国标码(冗余)',
    `material_code`                 varchar(40)  DEFAULT NULL COMMENT '物料编码(冗余)',
    `shop_id`                       bigint       DEFAULT NULL COMMENT '店铺ID',
    `shop_code`                     varchar(40)  DEFAULT NULL COMMENT '店铺编码(销售单中店铺编码可以对应上)',
    `shop_name`                     varchar(255) DEFAULT NULL COMMENT '店铺名称',
    `product_series_id`             varchar(40)  DEFAULT NULL COMMENT '产品系列',
    `product_series_name`           varchar(255) DEFAULT NULL COMMENT '产品系列名称',
    `last_distribution_quantity`    int          DEFAULT NULL COMMENT '上期分货数量',
    `last_sales_quantity`           int          DEFAULT NULL COMMENT '上期销售数量',
    `current_distribution_quantity` int          DEFAULT NULL COMMENT '当期分货数量',
    `current_sales_quantity`        int          DEFAULT NULL COMMENT '当期销售数量',
    `last_7_day_sales_quantity`     int          DEFAULT NULL COMMENT '近7天销量',
    `last_14_day_sales_quantity`    int          DEFAULT NULL COMMENT '近14天销量',
    `create_by`                     bigint       DEFAULT NULL COMMENT '创建人',
    `create_time`                   datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`                     bigint       DEFAULT NULL COMMENT '更新人',
    `update_time`                   datetime     DEFAULT NULL COMMENT '更新时间',
    `deleted`                       tinyint      DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    KEY                             `idx_distribution_line_id` (`distribution_line_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分货单行店铺明细表';

CREATE TABLE `t_distribution_adjust`
(
    `id`              bigint      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `adjust_no`       varchar(40) NOT NULL COMMENT '调整单号 TZDYYYYMMDD+流水号',
    `distribution_id` bigint      NOT NULL COMMENT '分货单ID',
    `distribution_no` varchar(40) NOT NULL COMMENT '分货单号(冗余)',
    `create_by`       bigint   DEFAULT NULL COMMENT '创建人',
    `create_time`     datetime DEFAULT NULL COMMENT '创建时间',
    `update_by`       bigint   DEFAULT NULL COMMENT '更新人',
    `update_time`     datetime DEFAULT NULL COMMENT '更新时间',
    `deleted`         tinyint  DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_adjust_no` (`adjust_no`),
    KEY               `idx_distribution_id` (`distribution_id`),
    KEY               `idx_distribution_no` (`distribution_no`),
    KEY               `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分货调整单表';

CREATE TABLE `t_distribution_adjust_line`
(
    `id`                   bigint      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `adjust_id`            bigint      NOT NULL COMMENT '调整单ID',
    `adjust_no`            varchar(40) NOT NULL COMMENT '调整单号(冗余)',
    `distribution_id`      bigint      NOT NULL COMMENT '分货单ID',
    `distribution_no`      varchar(40) NOT NULL COMMENT '分货单号(冗余)',
    `distribution_line_id` bigint      NOT NULL COMMENT '分货单行项目ID',
    `reserved_quantity`    int         DEFAULT NULL COMMENT '预留库存',
    `create_by`            bigint   DEFAULT NULL COMMENT '创建人',
    `create_time`          datetime DEFAULT NULL COMMENT '创建时间',
    `update_by`            bigint   DEFAULT NULL COMMENT '更新人',
    `update_time`          datetime DEFAULT NULL COMMENT '更新时间',
    `deleted`              tinyint  DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    KEY                    `idx_adjust_id` (`adjust_id`),
    KEY                    `idx_adjust_no` (`adjust_no`),
    KEY                    `idx_distribution_id` (`distribution_id`),
    KEY                    `idx_distribution_no` (`distribution_no`),
    KEY                    `idx_distribution_line_id` (`distribution_line_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分货调整单行表';

CREATE TABLE `t_distribution_adjust_line_shop`
(
    `id`                            bigint      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `adjust_id`                     bigint      NOT NULL COMMENT '调整单ID',
    `adjust_no`                     varchar(40) NOT NULL COMMENT '调整单号(冗余)',
    `adjust_line_id`                bigint      NOT NULL COMMENT '调整单行ID',
    `distribution_id`               bigint      NOT NULL COMMENT '分货单ID',
    `distribution_no`               varchar(40) NOT NULL COMMENT '分货单号(冗余)',
    `distribution_line_id`          bigint      NOT NULL COMMENT '分货单行项目ID',
    `shop_id`                       bigint       DEFAULT NULL COMMENT '店铺ID',
    `shop_code`                     varchar(40)  DEFAULT NULL COMMENT '店铺编码(销售单中店铺编码可以对应上)',
    `shop_name`                     varchar(255) DEFAULT NULL COMMENT '店铺名称',
    `current_distribution_quantity` int          DEFAULT NULL COMMENT '当期分货数量',
    `current_sales_quantity`        int          DEFAULT NULL COMMENT '当期销售数量',
    `last_7_day_sales_quantity`     int          DEFAULT NULL COMMENT '近7天销量',
    `last_14_day_sales_quantity`    int          DEFAULT NULL COMMENT '近14天销量',
    `create_by`                     bigint       DEFAULT NULL COMMENT '创建人',
    `create_time`                   datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`                     bigint       DEFAULT NULL COMMENT '更新人',
    `update_time`                   datetime     DEFAULT NULL COMMENT '更新时间',
    `deleted`                       tinyint      DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    KEY                             `idx_adjust_id` (`adjust_id`),
    KEY                             `idx_adjust_no` (`adjust_no`),
    KEY                             `idx_adjust_line_id` (`adjust_line_id`),
    KEY                             `idx_distribution_id` (`distribution_id`),
    KEY                             `idx_distribution_no` (`distribution_no`),
    KEY                             `idx_distribution_line_id` (`distribution_line_id`),
    KEY                             `idx_shop_id` (`shop_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='调整单行店铺明细表';

CREATE TABLE t_inventory_age
(
    id                   bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    expiry_date          date   NOT NULL COMMENT '账龄日期',
    ou_name              varchar(100) COMMENT '业务实体',
    department           varchar(50) COMMENT '部门ID',
    department_name      varchar(100) COMMENT '部门',
    second_dep_name      varchar(100) COMMENT '二级部门',
    business_mode_name   varchar(100) COMMENT '生意模式',
    project              varchar(50) COMMENT '项目ID',
    project_name         varchar(100) COMMENT '项目',
    product_genera       varchar(100) COMMENT '产业',
    product_category     varchar(100) COMMENT '品类',
    product_subclass     varchar(100) COMMENT '产品系列',
    material_type        varchar(100) COMMENT '产品类型',
    model_series         varchar(100) COMMENT '机型系列',
    material_model       varchar(100) COMMENT '机型',
    color                varchar(50) COMMENT '颜色',
    is_prototype         varchar(10) COMMENT '是否样机',
    item_code            varchar(50) COMMENT '物料id',
    description          varchar(255) COMMENT '物料描述',
    total_qty            int COMMENT '总库存数量',
    total_amount         decimal(19, 2) COMMENT '总库存金额',
    total_currentinv_qty int COMMENT '本仓数量',
    total_packinv_qty    int COMMENT '铺货数量',
    first_interval       int COMMENT '0-30天数量',
    first_amount         decimal(19, 2) COMMENT '0-30天金额',
    first_rate           decimal(10, 2) COMMENT '0-30天占比',
    second_interval      int COMMENT '31-60天数量',
    second_amount        decimal(19, 2) COMMENT '31-60天金额',
    second_rate          decimal(10, 2) COMMENT '31-60天占比',
    thirdly_interval     int COMMENT '61-90天数量',
    thirdly_amount       decimal(19, 2) COMMENT '61-90天金额',
    thirdly_rate         decimal(10, 2) COMMENT '61-90天占比',
    fourthly_interval    int COMMENT '91-120天数量',
    fourthly_amount      decimal(19, 2) COMMENT '91-120天金额',
    fourthly_rate        decimal(10, 2) COMMENT '91-120天占比',
    eleventh_interval    int COMMENT '121-150天数量',
    eleventh_amount      decimal(19, 2) COMMENT '121-150天金额',
    eleventh_rate        decimal(10, 2) COMMENT '121-150天占比',
    twelfth_interval     int COMMENT '151-180天数量',
    twelfth_amount       decimal(19, 2) COMMENT '151-180天金额',
    twelfth_rate         decimal(10, 2) COMMENT '151-180天占比',
    thirteenth_interval  int COMMENT '181-210天数量',
    thirteenth_amount    decimal(19, 2) COMMENT '181-210天金额',
    thirteenth_rate      decimal(10, 2) COMMENT '181-210天占比',
    fourteenth_interval  int COMMENT '211-240天数量',
    fourteenth_amount    decimal(19, 2) COMMENT '211-240天金额',
    fourteenth_rate      decimal(10, 2) COMMENT '211-240天占比',
    fifteenth_interval   int COMMENT '241-270天数量',
    fifteenth_amount     decimal(19, 2) COMMENT '241-270天金额',
    fifteenth_rate       decimal(10, 2) COMMENT '241-270天占比',
    sixteenth_interval   int COMMENT '271-300天数量',
    sixteenth_amount     decimal(19, 2) COMMENT '271-300天金额',
    sixteenth_rate       decimal(10, 2) COMMENT '271-300天占比',
    seventeenth_interval int COMMENT '301-330天数量',
    seventeenth_amount   decimal(19, 2) COMMENT '301-330天金额',
    seventeenth_rate     decimal(10, 2) COMMENT '301-330天占比',
    eighteenth_interval  int COMMENT '331-365天数量',
    eighteenth_amount    decimal(19, 2) COMMENT '331-365天金额',
    eighteenth_rate      decimal(10, 2) COMMENT '331-365天占比',
    eighth_interval      int COMMENT '1年-2年数量',
    eighth_amount        decimal(19, 2) COMMENT '1年-2年金额',
    eighth_rate          decimal(10, 2) COMMENT '1年-2年占比',
    ninth_interval       int COMMENT '2年以上数量',
    ninth_amount         decimal(19, 2) COMMENT '2年以上金额',
    ninth_rate           decimal(10, 2) COMMENT '2年以上占比',
    tenth_interval       int COMMENT '60天以上合计数量',
    tenth_amount         decimal(19, 2) COMMENT '60天以上合计金额',
    tenth_rate           decimal(10, 2) COMMENT '60天以上合计占比',
    nineteenth_interval  int COMMENT '90天以上合计数量',
    nineteenth_amount    decimal(19, 2) COMMENT '90天以上合计金额',
    nineteenth_rate      decimal(10, 2) COMMENT '90天以上合计占比',
    sixth_interval       int COMMENT '181天以上合计数量',
    sixth_amount         decimal(19, 2) COMMENT '181天以上合计金额',
    sixth_rate           decimal(10, 2) COMMENT '181天以上合计占比',
    fifth_interval       int COMMENT '121-180天合计数量',
    fifth_amount         decimal(19, 2) COMMENT '121-180天合计金额',
    fifth_rate           decimal(10, 2) COMMENT '121-180天合计占比',
    seventh_interval     int COMMENT '181天-1年合计数量',
    seventh_amount       decimal(19, 2) COMMENT '181天-1年合计金额',
    seventh_rate         decimal(10, 2) COMMENT '181天-1年合计占比',
    create_by            varchar(40) default null comment '创建人名称',
    create_time          datetime    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by            varchar(40) default null comment '更新人名称',
    update_time          datetime    DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted              tinyint     DEFAULT 0 COMMENT '是否删除 0-未删除 1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_expiry_date` (`expiry_date`),
    KEY `idx_product_genera` (`product_genera`),
    KEY `idx_item_code` (`item_code`),
    KEY `idx_description` (`description`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT '库龄信息表(大数据)';


create table t_stock
(
    id                   bigint not null auto_increment comment 'id',
    material_code        varchar(40) comment '物料编码',
    gb_code              varchar(40) comment '国标码',
    warehouse_code       varchar(40) comment '仓库编码',
    warehouse_name       varchar(255) comment '仓库名称',
    ok_qty               int comment '良品数量',
    damage_qty           int comment '不良品数量',
    box_loss_qty         int comment '盒损数量',
    after_sale_qty       int comment '售后数量',
    demo_back_qty        int comment '演示机退数量',
    occupy_qty           int comment '销售占用数量',
    create_time          datetime default CURRENT_TIMESTAMP comment '创建时间',
    create_by            bigint comment '创建人',
    update_time          datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP comment '更新时间',
    update_by            bigint comment '更新人',
    primary key (id)
)
    ENGINE = InnoDB,
    DEFAULT CHARACTER SET = utf8mb4,
    COLLATE = utf8mb4_general_ci,
    auto_increment = 10;

alter table t_stock comment '在库库存表';

create unique index idx_mat_warehouse on t_stock
(
     material_code,
     warehouse_code
);

create index idx_gb_warehouse on t_stock
(
     gb_code,
     warehouse_code
);

create table t_stock_change_log
(
    id                   bigint not null auto_increment comment 'id',
    material_code        varchar(40) comment '物料编码',
    warehouse_code       varchar(40) comment '仓库编码',
    warehouse_name       varchar(255) comment '仓库名称',
    code                 varchar(40) comment '出入库单号',
    type                 varchar(40) comment '出入库类型:CGRK=采购入库,DBRK=跨项目调拨入库,JJRK=借机拒签入库,HJRK=还机入库,QTRK=其他入库,XTRK=销售退货,PYRK=盘盈入库,DBCK=跨项目调拨出库,JJCK=借机出库,QTCK=其他出库,XSCK=销售出库,PKCK=盘亏出库',
    handle_time          datetime comment '出入库时间',
    reason               varchar(255) comment '销售退货原因',
    qty                  int comment '变更数量:入库正数,出库负数',
    inventory_type       varchar(40) null comment '库存类型: ZP=正品, CC=残次, JS=机损, XS=箱损, ZT=在途库存，DJ=冻结',
    goods_type           varchar(40) comment '货品属性:OK=良品,BOXLOSS=盒损,DAMAGE=不良品,DEMO BACK=演示机退,AFTER SALES=售后',
    tracking_number      varchar(40) comment '物流单号',
    order_id             varchar(40) comment '订单号',
    operator             varchar(40) comment '制单人',
    create_time          datetime default CURRENT_TIMESTAMP comment '创建时间',
    create_by            bigint comment '创建人',
    update_time          datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP comment '更新时间',
    update_by            bigint comment '更新人',
    primary key (id)
)
    ENGINE = InnoDB,
    DEFAULT CHARACTER SET = utf8mb4,
    COLLATE = utf8mb4_general_ci,
    auto_increment = 10;

alter table t_stock_change_log comment '库存变动记录表';

create index idx_mat_warehouse on t_stock_change_log
(
     material_code,
     warehouse_code
);

create table t_stock_occupy_log
(
    id                   bigint not null auto_increment comment 'id',
    order_id             varchar(50) comment '订单号',
    warehouse_code       varchar(40) comment '仓库编码',
    material_code        varchar(40) comment '物料编码',
    qty                  int comment '数量:占用正数,释放负数',
    create_time          datetime default CURRENT_TIMESTAMP comment '创建时间',
    create_by            bigint comment '创建人',
    update_time          datetime default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP comment '更新时间',
    update_by            bigint comment '更新人',
    primary key (id)
)
    ENGINE = InnoDB,
    DEFAULT CHARACTER SET = utf8mb4,
    COLLATE = utf8mb4_general_ci,
    auto_increment = 10;

alter table t_stock_occupy_log comment '库存销售占用记录表';

create index idx_mat_warehouse on t_stock_occupy_log
(
     material_code,
     warehouse_code
);

create index idx_order on t_stock_occupy_log
(
     order_id
);

-- 库存中心 End