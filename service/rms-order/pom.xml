<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.tl.rms.service</groupId>
        <artifactId>service</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>rms-order</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <easyexcel>4.0.3</easyexcel>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.51</version>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.api</groupId>
            <artifactId>rms-order-api</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-mq-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-fulfillment-client</artifactId>
        </dependency>
    </dependencies>

</project>