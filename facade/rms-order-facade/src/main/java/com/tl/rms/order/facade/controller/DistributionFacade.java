package com.tl.rms.order.facade.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tl.rms.common.exception.CommonException;
import com.tl.rms.common.model.EnumInfoVo;
import com.tl.rms.common.model.ResponseVo;
import com.tl.rms.lib.session.user.LoginUser;
import com.tl.rms.order.api.*;
import com.tl.rms.order.domain.constant.Constants;
import com.tl.rms.order.domain.enums.DistributionPageSaveTypeEnum;
import com.tl.rms.order.domain.enums.DistributionStatusEnum;
import com.tl.rms.order.domain.enums.DistributionTypeEnum;
import com.tl.rms.order.domain.request.DistributionPageRequestVo;
import com.tl.rms.order.domain.vo.*;
import com.tl.rms.order.facade.listener.DistributionSkuImportDataListener;
import com.tl.rms.order.facade.listener.vo.DistributionSkuImportDataVo;
import com.tl.rms.order.facade.util.ExcelExportUtil;
import com.tl.rms.user.api.ShopClient;
import com.tl.rms.user.api.UserClient;
import com.tl.rms.user.domain.vo.ShopVo;
import com.tl.rms.user.domain.vo.UserVo;
import com.tl.rms.util.CollectionUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 分货管理
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/distribution")
@RequiredArgsConstructor
public class DistributionFacade {

    @Resource
    private DistributionClient distributionClient;

    @Resource
    private DistributionLineClient distributionLineClient;

    @Resource
    private IndustryCategoryClient industryCategoryClient;

    @Resource
    private ShopClient shopClient;

    @Resource
    private DistributionAdjustClient distributionAdjustClient;

    @Resource
    private UserClient userClient;

    @Resource
    private MaterialClient materialClient;

    @Resource
    private StockClient stockClient;

    /**
     * 分页查询 分货管理列表
     */
    @PostMapping(value = "/queryDistributionPage")
    public ResponseVo<Page<DistributionVo>> page(@RequestBody DistributionPageRequestVo requestVo) {
        Page<DistributionVo> pages = distributionClient.queryDistributionPage(requestVo);
        if(null != pages && !CollectionUtil.isEmpty(pages.getRecords())) {
            List<DistributionVo> list = pages.getRecords();
            // 获取创建人、更新人的id去重集合
            List<Long> personIdList = new ArrayList<>();
            personIdList.addAll(list.stream().map(DistributionVo::getCreateBy).distinct().toList());
            personIdList.addAll(list.stream().map(DistributionVo::getUpdateBy).distinct().toList());
            List<UserVo> userByIds = userClient.getUserByIds(personIdList);
            Map<Long, String> responsiblePersonMap = userByIds.stream().collect(Collectors.toMap(UserVo::getId, UserVo::getUserName));
            for (DistributionVo item : list) {
                item.setCreateByName(responsiblePersonMap.get(item.getCreateBy()));
                item.setUpdateByName(responsiblePersonMap.get(item.getUpdateBy()));
                item.setStatusName(DistributionStatusEnum.getLabelByValue(item.getStatus()));
            }
        }
        return ResponseVo.suc(pages);
    }

    /**
     * 列表行展开-查询本批次销售任务中的调整单集合
     */
    @GetMapping(value = "/getDistributionAdjustRecords")
    public ResponseVo<List<DistributionAdjustRecordVo>> getDistributionAdjustRecords(@RequestParam("distributionId") Long distributionId) {
        List<DistributionAdjustRecordVo> list = distributionAdjustClient.getDistributionAdjustRecords(distributionId);
        if (!CollectionUtil.isEmpty(list)){
            List<Long> responsiblePersonList = list.stream().map(DistributionAdjustRecordVo::getCreateBy).toList();
            List<UserVo> userByIds = userClient.getUserByIds(responsiblePersonList);
            Map<Long, String> responsiblePersonMap = userByIds.stream().collect(Collectors.toMap(UserVo::getId, UserVo::getUserName));
            list.forEach(item -> item.setCreateByName(responsiblePersonMap.get(item.getCreateBy())));
        }
        return ResponseVo.suc(list);
    }

    @GetMapping(value = "/getDistributionNo")
    @Operation(summary = "获取分货创建批次号")
    public ResponseVo<String> getDistributionNo() {
        return ResponseVo.suc(distributionClient.getDistributionNo());
    }

    @GetMapping(value = "/getDistributionStatusEnums")
    @Operation(summary = "获取分货状态下拉")
    public ResponseVo<List<EnumInfoVo>> getDistributionStatusEnums() {
        return ResponseVo.suc(DistributionStatusEnum.list());
    }

    @GetMapping(value = "/getIndustrySelect4tDistributionCreate")
    @Operation(summary = "获取分货创建时关联产业下拉")
    public ResponseVo<List<EnumInfoVo>> getIndustry4tDistributionCreate() {
        Long userId = LoginUser.getCurrentUser().getUserId();
        if(userId == null) {
            // 默认管理员
            userId = 1L;
        }
        return ResponseVo.suc(industryCategoryClient.getIndustryByPersonInChargeId(userId));
    }

    @GetMapping(value = "/getShopList4tDistributionCreate")
    @Operation(summary = "获取分货创建时可用商店")
    public ResponseVo<List<EnumInfoVo>> getShopList4tDistributionCreate() {
        List<ShopVo> list = shopClient.list(Constants.PUBLIC_YES);
        if(CollUtil.isEmpty(list)) {
            throw new CommonException("无分货可用商店");
        }
        List<EnumInfoVo> enumInfoVoList = list.stream().map(item -> new EnumInfoVo(item.getChannelCode(), item.getChannelName())).toList();
        return ResponseVo.suc(enumInfoVoList);
    }

    @GetMapping(value = "/getDistribution4tDistributionCreate/{id}")
    @Operation(summary = "获取分货调整时分货详情")
    public ResponseVo<DistributionCreateDetailsVo> getDistribution4tDistributionCreate(@PathVariable("id") Long id) {
        if(id == null) {
            return ResponseVo.failure("参数错误");
        }

        DistributionCreateDetailsVo distributionCreateDetailsVo = distributionClient.getDistribution4tDistributionCreate(id);
        // 创建人
        distributionCreateDetailsVo.setCreateByName(userClient.getUserById(distributionCreateDetailsVo.getCreateBy()).getUserName());
        // 调整单号
        distributionCreateDetailsVo.setAdjustNo(distributionAdjustClient.getDistributionAdjustNo(id));
        return ResponseVo.suc(distributionCreateDetailsVo);
    }

    @GetMapping(value = "/getPreviousDistribution4DistributionCreate/{industryId}")
    @Operation(summary = "获取上次分货单详情")
    public ResponseVo<PreviousDistributionByIndustryIdVo> getPreviousDistribution4DistributionCreateByIndustryId(@PathVariable("industryId") String industryId) {
        if(industryId == null) {
            return ResponseVo.failure("参数错误");
        }

        Long userId = LoginUser.getCurrentUser().getUserId();
        if(userId == null) {
            // 默认管理员
            userId = 1L;
        }

        PreviousDistributionByIndustryIdVo previousDistributionByIndustryIdVo = distributionClient.getPreviousDistribution4DistributionCreateByIndustryId(industryId, userId);
        if(previousDistributionByIndustryIdVo == null) {
            return ResponseVo.failure("无该行业分货记录");
        }

        return ResponseVo.suc(previousDistributionByIndustryIdVo);
    }

    @PostMapping(value = "/createDistributionConfirm")
    @Operation(summary = "创建确认分货前提示")
    public ResponseVo<String> createDistributionConfirm(@RequestBody DistributionCreateRequestVo requestVo) {
        // 参数校验
        ResponseVo<String> checkParams = checkParams(requestVo);
        if(checkParams.getCode() != ResponseVo.SUCCESS_CODE) {
            return checkParams;
        }
        String distributionType = DistributionTypeEnum.getLabelByValue(requestVo.getDistributionType());
        String distributionTypeDesc = DistributionTypeEnum.MATERIAL.getValue().equals(requestVo.getDistributionType()) ? "物料种类" : "SKU";
        String prompt = "本次销售任务按照%s维度销售任务，销售任务%s是%d种。是否确认销售任务？确认销售任务之后，立即通知店铺销售任务信息。";
        if(Objects.isNull(requestVo.getDistributionId())) {
            return ResponseVo.suc(String.format(prompt, distributionType, distributionTypeDesc, requestVo.getDistributionLineList().size()));
        }

        Integer countMaterial = distributionLineClient.countByDistributionId(requestVo.getDistributionId());
        // 检查countMaterial是否大于0
        if(countMaterial <= 0) {
            return ResponseVo.failure("无分货单行数据");
        }
        return ResponseVo.suc(String.format(prompt, distributionType, distributionTypeDesc, countMaterial));
    }

    @PostMapping(value = "/adjustDistributionConfirm")
    @Operation(summary = "调整确认分货前提示")
    public ResponseVo<String> adjustDistributionConfirm(@RequestBody List<AdjustDistributionConfirmRequestVo> requestVo) {
        // 参数校验
        if(CollUtil.isEmpty(requestVo)) {
            return ResponseVo.failure("参数错误");
        }

        StringJoiner joiner = new StringJoiner("");
        joiner.add("本次调整");
        int count = 0;
        for (AdjustDistributionConfirmRequestVo adjustDistributionConfirmRequestVo : requestVo) {
            List<DistributionLineShopVo> distributionLineShopList = distributionLineClient.getDistributionLineShopList(adjustDistributionConfirmRequestVo.getDistributionLineId());
            if(CollUtil.isEmpty(distributionLineShopList)) {
                return ResponseVo.failure("无分货单行数据");
            }

            if(CollUtil.isEmpty(adjustDistributionConfirmRequestVo.getConfirmShopList())) {
                continue;
            }

            DistributionLineVo distributionLineVo = distributionLineClient.getDistributionLineShop(adjustDistributionConfirmRequestVo.getDistributionLineId());
            String msg = String.format("物料为%s", StrUtil.isBlank(distributionLineVo.getMaterialName()) ? "SKU:" + distributionLineVo.getGbCode() : distributionLineVo.getMaterialName());
            StringBuffer msg2 = new StringBuffer();
            boolean flag = false;
            Map<Long, DistributionLineShopVo> distributionLineShopMap = distributionLineShopList.stream().collect(Collectors.toMap(DistributionLineShopVo::getShopId, item -> item));
            // 比较adjustDistributionConfirmRequestVo中的confirmShopList的shopId一致的分货数量是否一致
            for (AdjustDistributionConfirmShopRequestVo adjustDistributionConfirmShopRequestVo : adjustDistributionConfirmRequestVo.getConfirmShopList()) {
                DistributionLineShopVo distributionLineShopVo = distributionLineShopMap.get(adjustDistributionConfirmShopRequestVo.getShopId());
                if(distributionLineShopVo == null) {
                    return ResponseVo.failure("无该分货单行数据");
                }
                if(!adjustDistributionConfirmShopRequestVo.getCurrentDistributionQuantity().equals(distributionLineShopVo.getCurrentDistributionQuantity())) {
                    flag = true;
                    count += 1;
                    msg2.append(String.format("调整店铺为%s，调整数量为%d，", distributionLineShopVo.getShopName(), adjustDistributionConfirmShopRequestVo.getCurrentDistributionQuantity()));
                }
            }
            if(flag) {
                joiner.add(msg + msg2 + ";");
            }
        }
        joiner.add("点击确认即可重新销售任务。");

        if(count == 0) {
            log.info("可能为删除, 数据无变化, 业务继续");
            // 可能为删除, 数据无变化
            return ResponseVo.suc();
        }
        return ResponseVo.suc(joiner.toString());
    }

    @PostMapping(value = "/adjustDistribution")
    @Operation(summary = "调整确认分货, 需要记录日志")
    public ResponseVo<String> adjustDistribution(@RequestBody List<AdjustDistributionConfirmRequestVo> requestVo) {
        // 参数校验
        ResponseVo<String> stringResponseVo = this.adjustDistributionConfirm(requestVo);
        if(stringResponseVo.getCode() != ResponseVo.SUCCESS_CODE) {
            return stringResponseVo;
        }

        DistributionLineVo distributionLineShop = distributionLineClient.getDistributionLineShop(requestVo.get(0).getDistributionLineId());
        ResponseVo<DistributionCreateRequestVo> resultVo = distributionClient.getDistributionCreateRequestById(distributionLineShop.getDistributionId());
        if(resultVo.getCode() != ResponseVo.SUCCESS_CODE) {
            throw new CommonException(resultVo.getMsg());
        }

        // 可用库存校验
        handleAvailableQuantity(false, resultVo.getData());

        // 入库
        Long userId = LoginUser.getCurrentUser().getUserId();
        if(Objects.isNull(userId)) {
            userId = 1L;
        }
        distributionClient.adjustDistributionAndAddLog(userId, requestVo);
        return ResponseVo.suc();
    }

    @PostMapping(value = "/createDistribution")
    @Operation(summary = "保存/确认分货")
    public ResponseVo createDistribution(@RequestBody DistributionCreateRequestVo requestVo) {
        // 参数校验
        ResponseVo<String> checkParams = checkParams(requestVo);
        if(checkParams.getCode() != ResponseVo.SUCCESS_CODE) {
            return checkParams;
        }

        // 设置创建人
        requestVo.setCreateUserId(LoginUser.getCurrentUser().getUserId());

        // 可用库存校验
        handleAvailableQuantity(true, requestVo);


        return distributionClient.createDistribution(requestVo);
    }

    @GetMapping(value = "/confirmDistribution/{distributionId}")
    @Operation(summary = "确认分货(详情页)")
    public ResponseVo confirmDistribution(@PathVariable("distributionId") Long distributionId) {
        // 参数校验
        if(Objects.isNull(distributionId)) {
            return ResponseVo.failure("参数错误");
        }

        ResponseVo<DistributionCreateRequestVo> resultVo = distributionClient.getDistributionCreateRequestById(distributionId);

        if(resultVo.getCode() != ResponseVo.SUCCESS_CODE) {
            return resultVo;
        }

        // 可用库存校验
        handleAvailableQuantity(false, resultVo.getData());

        // 设置创建人
        DistributionCreateRequestVo requestVo = resultVo.getData();
        requestVo.setCreateUserId(LoginUser.getCurrentUser().getUserId());

        return distributionClient.createDistribution(requestVo);
    }

    /**
     * 创建确认分货前提示
     * @param isQueryDb
     * @param requestVo
     */
    private void handleAvailableQuantity(boolean isQueryDb, DistributionCreateRequestVo requestVo) {
        List<DistributionLineCreateRequestVo> distributionLineList = requestVo.getDistributionLineList();
        List<String> codes = new ArrayList<>();

        // 需要查库并且存在分货单id
        if (null != requestVo.getDistributionId() && isQueryDb) {
            ResponseVo<DistributionCreateRequestVo> resultVo = distributionClient.getDistributionCreateRequestById(requestVo.getDistributionId());
            if(resultVo.getCode() == ResponseVo.SUCCESS_CODE) {
                distributionLineList.addAll(resultVo.getData().getDistributionLineList());
            }
        }

        // 物料
        if (CollUtil.isNotEmpty(distributionLineList) && DistributionTypeEnum.MATERIAL.getValue().equals(requestVo.getDistributionType())) {
            // distributionLineList根据物料编码去重
            distributionLineList = new ArrayList<>(distributionLineList.stream()
                    .collect(Collectors.toMap(
                            DistributionLineCreateRequestVo::getMaterialCode,
                            item -> item,
                            (existing, replacement) -> existing
                    ))
                    .values());
            codes = distributionLineList.stream().map(DistributionLineCreateRequestVo::getMaterialCode).collect(Collectors.toList());
        }

        // sku
        if (CollUtil.isNotEmpty(distributionLineList) && DistributionTypeEnum.SKU.getValue().equals(requestVo.getDistributionType())) {
            // distributionLineList根据国标码去重
            distributionLineList = new ArrayList<>(distributionLineList.stream()
                    .collect(Collectors.toMap(
                            DistributionLineCreateRequestVo::getGbCode,
                            item -> item,
                            (existing, replacement) -> existing
                    ))
                    .values());
            codes = distributionLineList.stream().map(DistributionLineCreateRequestVo::getGbCode).collect(Collectors.toList());
        }

        // codes去重
        codes = codes.stream().distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(codes)) {
            log.error("处理校验可用库存, 待校验物料/sku为空");
            throw new CommonException("处理校验可用库存, 待校验物料/sku为空");
        }

        // 获取可用库存
        List<StockAvailableQuantityVo> quantityVos;
        if (DistributionTypeEnum.MATERIAL.getValue().equals(requestVo.getDistributionType())) {
            quantityVos = stockClient.listAvailableQuantityByMaterialCode(codes);
        } else {
            quantityVos = stockClient.listAvailableQuantityByGbCode(codes);
        }

        if (CollUtil.isEmpty(quantityVos)) {
            log.error("处理校验可用库存, 获取可用库存数量为空");
            throw new CommonException("处理校验可用库存, 获取可用库存数量为空");
        }

        String msg = DistributionTypeEnum.MATERIAL.getValue().equals(requestVo.getDistributionType()) ? "物料编码:" : "国标码:";
        Map<String, StockAvailableQuantityVo> quantityVoMap = quantityVos.stream().filter(v -> v.getAvailableQuantity() != null).collect(Collectors.toMap(StockAvailableQuantityVo::getCode, v -> v));
        StringBuilder msg2 = new StringBuilder();

        // 处理分货单行可用库存数量不存在的情况
        for (DistributionLineCreateRequestVo distributionLineCreateRequestVo : distributionLineList) {
            if (DistributionTypeEnum.SKU.getValue().equals(requestVo.getDistributionType())
                    && quantityVoMap.containsKey(distributionLineCreateRequestVo.getGbCode())) {
                StockAvailableQuantityVo stockAvailableQuantityVo = quantityVoMap.get(distributionLineCreateRequestVo.getGbCode());
                distributionLineCreateRequestVo.setAvailableQuantity(stockAvailableQuantityVo.getAvailableQuantity());
                // 仓库名称
                distributionLineCreateRequestVo.setWarehouseNames(stockAvailableQuantityVo.getWarehouseNames());
            } else if (DistributionTypeEnum.MATERIAL.getValue().equals(requestVo.getDistributionType())
                    && quantityVoMap.containsKey(distributionLineCreateRequestVo.getMaterialCode())) {
                StockAvailableQuantityVo stockAvailableQuantityVo = quantityVoMap.get(distributionLineCreateRequestVo.getMaterialCode());
                distributionLineCreateRequestVo.setAvailableQuantity(stockAvailableQuantityVo.getAvailableQuantity());
                // 仓库名称
                distributionLineCreateRequestVo.setWarehouseNames(stockAvailableQuantityVo.getWarehouseNames());
            } else {
                msg2.append(DistributionTypeEnum.MATERIAL.getValue().equals(requestVo.getDistributionType())
                        ? distributionLineCreateRequestVo.getMaterialCode() : distributionLineCreateRequestVo.getGbCode()).append(",");
            }
        }

        if(StrUtil.isNotBlank(msg2.toString())) {
            msg2 = msg2.deleteCharAt(msg2.length() - 1);
            throw new CommonException(msg + msg2 + " ,处理校验可用库存, 获取可用库存数量为空");
        }

        // 处理 各店铺分货量之和<可用库存情况
        // 物料/国标码: 44532323,674646355 ... 各店铺分货量之和大于可用库存数量, 请核实修改后重新提交！
        // 页面提交的分货单行商店数据
        for (DistributionLineCreateRequestVo distributionLineCreateRequestVo : distributionLineList) {
            Integer availableQuantity = distributionLineCreateRequestVo.getAvailableQuantity();
            List<DistributionLineShopCreateRequestVo> distributionLineShopList = distributionLineCreateRequestVo.getDistributionLineShopList();
            String code = DistributionTypeEnum.MATERIAL.getValue().equals(requestVo.getDistributionType()) ? distributionLineCreateRequestVo.getMaterialCode() : distributionLineCreateRequestVo.getGbCode();
            if (CollUtil.isEmpty(distributionLineShopList)) {
                log.error("code: {}, 处理校验可用库存, 获取分货单行商店为空", code);
                continue;
            }

            // 商店行分货量相加
            Integer currentDistributionQuantity = distributionLineShopList.stream()
                    .map(distributionLineShopCreateRequestVo ->
                            distributionLineShopCreateRequestVo.getCurrentDistributionQuantity() == null ? 0 : distributionLineShopCreateRequestVo.getCurrentDistributionQuantity()).reduce(0, Integer::sum);

            if (currentDistributionQuantity > availableQuantity) {
                msg2.append(code).append(",");
            }
        }

        if(StrUtil.isNotBlank(msg2.toString())) {
            msg2 = msg2.deleteCharAt(msg2.length() - 1);
            throw new CommonException(msg + msg2 + " ,各店铺分货量之和大于可用库存数量, 请核实修改后重新提交！");
        }
    }

    private ResponseVo<String> checkParams(DistributionCreateRequestVo requestVo) {
        if(requestVo == null) {
            throw new CommonException("参数错误");
        }

        if(requestVo.getSaveType() == null || DistributionPageSaveTypeEnum.getLabelByValue(requestVo.getSaveType()) == null) {
            throw new CommonException("保存类型错误");
        }

        if(requestVo.getIndustryId() == null) {
            throw new CommonException("产业未选择");
        }

        Long userId = LoginUser.getCurrentUser().getUserId();
        if(userId == null) {
            userId = 1L;
        }
        List<EnumInfoVo> industryList = industryCategoryClient.getIndustryByPersonInChargeId(userId);
        if(industryList == null || industryList.isEmpty()) {
            throw new CommonException("无分货可用产业");
        }
        Map<String, EnumInfoVo> industryMap = industryList.stream().collect(Collectors.toMap(EnumInfoVo::getValue, item -> item));
        if(!industryMap.containsKey(requestVo.getIndustryId())) {
            throw new CommonException("非当前用户关联产业");
        }
        requestVo.setIndustryName(industryMap.get(requestVo.getIndustryId()).getLabel());

        // 单独只是保存，则不需要其他参数校验
        /*if(DistributionPageSaveTypeEnum.SAVE.getLabel().equals(requestVo.getSaveType())) {
            return ResponseVo.suc();
        }*/

        if(requestVo.getDistributionType() == null || DistributionTypeEnum.getLabelByValue(requestVo.getDistributionType()) == null) {
            throw new CommonException("分货类型错误");
        }

        if(CollUtil.isEmpty(requestVo.getDistributionLineList())) {
            return ResponseVo.suc();
        }

        // 处理商店信息
        List<ShopVo> list = shopClient.list(Constants.PUBLIC_YES);
        if(CollUtil.isEmpty(list)) {
            throw new CommonException("无分货可用商店");
        }
        Map<String, ShopVo> shopMap = list.stream().collect(Collectors.toMap(ShopVo::getChannelCode, item -> item));

        if (CollUtil.isNotEmpty(requestVo.getDistributionLineList())) {
            List<DistributionLineCreateRequestVo> distributionLineList = requestVo.getDistributionLineList();
            // distributionLineList 增加校验是否有重复物料
            if (distributionLineList.stream().map(item -> item.getGbCode() + item.getMaterialCode()).distinct().count() != distributionLineList.size()) {
                throw new CommonException("分货行存在重复物料");
            }

            distributionLineList.forEach(item -> {
                if(StrUtil.isBlank(item.getGbCode()) && StrUtil.isBlank(item.getMaterialCode())) {
                    throw new CommonException("分货行国标码/物料编码不能为空");
                }

                if(Objects.isNull(item.getReservedQuantity())) {
                    throw new CommonException("“请填写预留库存”，无法保存和确认");
                }

                // 如果为新增行, 则必须有分货商店
                if (Objects.isNull(item.getDistributionLineId()) && CollUtil.isEmpty(item.getDistributionLineShopList())) {
                    throw new CommonException("新增的分货行, 必须有分货商店");
                }

                if (CollUtil.isNotEmpty(item.getDistributionLineShopList())) {
                    item.getDistributionLineShopList().forEach(shopItem -> {
                        if(StrUtil.isBlank(shopItem.getShopCode())) {
                            throw new CommonException("分货行商店编码为空");
                        }
                        if(shopItem.getCurrentDistributionQuantity() == null) {
                            throw new CommonException("“请填写店铺分货数量”，无法保存和确认");
                        }
                        // 处理商店信息
                        if(shopMap.get(shopItem.getShopCode()) == null) {
                            throw new CommonException("分货行商店不在已有数据中存在");
                        }
                        shopItem.setShopName(shopMap.get(shopItem.getShopCode()).getChannelName());
                        shopItem.setShopId(shopMap.get(shopItem.getShopCode()).getId());
                    });
                }

            });
        }

        return ResponseVo.suc();
    }

    @Operation(summary = "下载模板")
    @GetMapping(value = "/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response, @RequestParam(value = "industryId") String industryId) {
        if(StrUtil.isBlank(industryId)) {
            throw new CommonException("参数错误");
        }

        // 获取分货模版物料数据
        List<DistributionSkuImportDataVo> businessObjectivesImportData = listMaterial4GbCode(industryId);

        try {
            ExcelExportUtil.export(response, DistributionSkuImportDataVo.EXPORT_EXCEL_NAME, "机型", DistributionSkuImportDataVo.class, businessObjectivesImportData);
        } catch (IOException e) {
            throw new CommonException("IO异常");
        }
    }

    /**
     * 获取分货模版物料数据
     * @param industryId
     * @return
     */
    private List<DistributionSkuImportDataVo> listMaterial4GbCode(String industryId) {
        Material4GbCodeQueryReqVo materialQueryReqVo = new Material4GbCodeQueryReqVo();
        materialQueryReqVo.setIndustryId(industryId);
        List<MaterialVo> materialSelect4GbCode = materialClient.getMaterialSelect4GbCode(materialQueryReqVo);
        if(CollUtil.isEmpty(materialSelect4GbCode)) {
            throw new CommonException("无该产业分货可用物料");
        }
        List<ShopVo> list = shopClient.list(Constants.PUBLIC_YES);
        if(CollUtil.isEmpty(list)) {
            throw new CommonException("无分货可用商店");
        }

        List<DistributionSkuImportDataVo> distributionSkuImportDataVoList = new ArrayList<>();

        for (MaterialVo materialVo : materialSelect4GbCode) {
            // 填充商店
            for (ShopVo shopVo : list) {
                DistributionSkuImportDataVo distributionSkuImportDataVo = new DistributionSkuImportDataVo();
                distributionSkuImportDataVo.setGbCode(materialVo.getGbCode());
                distributionSkuImportDataVo.setCategoryName(materialVo.getCategoryName());
//                distributionSkuImportDataVo.setProjectName(materialVo.getProjectName());
                distributionSkuImportDataVo.setProductSeriesName(materialVo.getProductSeriesName());
                distributionSkuImportDataVo.setModelName(materialVo.getModelName());
                distributionSkuImportDataVo.setColor(materialVo.getColor());
                distributionSkuImportDataVo.setShopName(shopVo.getChannelName());

                // 获取上一期分货量
                DistributionLineShopVo  distributionLineShopVo = distributionClient.getPreviousDistributionByIndustryIdAndGbCodeAndShopCode(industryId, materialVo.getGbCode(), shopVo.getChannelCode());
                if(distributionLineShopVo != null) {
                    distributionSkuImportDataVo.setLastDistributionQuantity(distributionLineShopVo.getCurrentDistributionQuantity());
                    // 上期销量/近7天销量/近14天销量
                    distributionSkuImportDataVo.setLastSalesQuantity(distributionLineShopVo.getCurrentSalesQuantity());
                    distributionSkuImportDataVo.setLast7DaySalesQuantity(distributionLineShopVo.getLast7DaySalesQuantity());
                    distributionSkuImportDataVo.setLast14DaySalesQuantity(distributionLineShopVo.getLast14DaySalesQuantity());
                }

                distributionSkuImportDataVoList.add(distributionSkuImportDataVo);
            }
        }

        // 处理可用库存数量
        handleAvailableQuantity(distributionSkuImportDataVoList);

        return distributionSkuImportDataVoList;
    }

    /**
     * 处理可用库存数量
     * @param distributionSkuImportDataVoList
     */
    private void handleAvailableQuantity(List<DistributionSkuImportDataVo> distributionSkuImportDataVoList) {
        if (CollUtil.isEmpty(distributionSkuImportDataVoList)) {
            return;
        }
        List<String> materialCodeList = distributionSkuImportDataVoList.stream().map(DistributionSkuImportDataVo::getGbCode).collect(Collectors.toList());
        List<StockAvailableQuantityVo> stockAvailableQuantityVos = stockClient.listAvailableQuantityByMaterialCode(materialCodeList);
        if (CollUtil.isEmpty(stockAvailableQuantityVos)) {
            return;
        }
        Map<String, StockAvailableQuantityVo> stockAvailableQuantityVoMap = stockAvailableQuantityVos.stream().collect(Collectors.toMap(StockAvailableQuantityVo::getCode, v -> v));
        for (DistributionSkuImportDataVo materialVo : distributionSkuImportDataVoList) {
            materialVo.setAvailableQuantity(stockAvailableQuantityVoMap.get(materialVo.getGbCode()).getAvailableQuantity().toString());
        }
    }

    @Operation(summary = "导入分货")
    @PostMapping(value = "/importDistribution")
    public ResponseVo importDistribution(@RequestParam(value = "distributionId", required = false) Long distributionId,
                                         @RequestPart("file") MultipartFile file) {
        if (file.isEmpty()) {
            return ResponseVo.failure("文件不能为空");
        }

        try {
            HashMap<String, ShopVo> shopMap = shopClient.getShopMap();

            DistributionSkuImportDataListener distributionSkuImportDataListener = new DistributionSkuImportDataListener(
                    this, shopMap, materialClient, distributionClient, distributionId);
            EasyExcel.read(file.getInputStream(), DistributionSkuImportDataVo.class,
                    distributionSkuImportDataListener).sheet().doRead();

            return ResponseVo.suc(distributionSkuImportDataListener.getDistributionId());
        } catch (IOException e) {
            throw new CommonException("IO异常");
        } catch (ExcelDataConvertException e) {
            throw new CommonException("请检查数据类型");
        }
    }
}