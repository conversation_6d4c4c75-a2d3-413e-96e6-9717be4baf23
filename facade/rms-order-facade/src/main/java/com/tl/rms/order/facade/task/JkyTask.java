package com.tl.rms.order.facade.task;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.tl.rms.lib.beans.web.restful.ResponseMessage;
import com.tl.rms.lib.redis.util.RedisUtil;
import com.tl.rms.lib.wsclient.client.jky.InvokeJkyService;
import com.tl.rms.lib.wsclient.client.jky.request.JkyOnlineOrderRequest;
import com.tl.rms.lib.wsclient.client.jky.request.JkyOrderRequest;
import com.tl.rms.lib.wsclient.client.jky.response.JackyunResponse;
import com.tl.rms.order.api.JkyOrderClient;
import com.tl.rms.order.domain.constant.JkyConstant;
import com.tl.rms.order.domain.response.JkyReturnResponse;
import com.tl.rms.order.domain.vo.JkyOnlineOrderDataVo;
import com.tl.rms.order.facade.controller.JkyFacade;
import com.tl.rms.order.facade.controller.MaterialFacade;
import com.tl.rms.util.DateUtil;
import com.tl.rms.util.json.JsonProcessUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class JkyTask {

    @Autowired
    private MaterialFacade materialFacade;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private InvokeJkyService invokeJkyService;

    @Autowired
    private JkyOrderClient jkyOrderClient;

    @Autowired
    private JkyFacade jkyFacade;

    /**
     * 吉客云销售单 上次同步的截止时间
     */
    private static final String JKY_ORDER_LAST_AUDIT_END_TIME = "jky:order:lastAuditEndTime";

    /**
     * 吉客云网店订单 上次同步的截止时间
     */
    private static final String JKY_INTERNET_SHOP_ORDER_LAST_END_TIME = "jky:order:lastInternetShopSyncEndTime";

    private DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    /**
     * 物料信息同步吉客云
     *
     * <AUTHOR>
     * @date 2025/5/19
     */
    @XxlJob(value = "syncMaterialTask")
    private void syncMaterialTask() {
        log.info("syncMaterialTask start");
        materialFacade.syncMaterial(new ArrayList<>());
        log.info("syncMaterialTask end");
    }

    /**
     * 同步吉客云销售单
     * 每15分钟执行一次，每次查询前15分钟之内的
     * 因核对库存差异，TW和EBS的库存快照是7点，中台会有半小时的数据延迟
     * 特于此前增加一次同步，以减少此类差异数据
     *
     * <AUTHOR>
     * @date 2023/5/22
     */
//    @Async
//    @Scheduled(cron = "0 53 6 * * ?")
//    @Scheduled(cron = "5 0/15 * * * ?")
//    @XxlJob(value = "syncJkyOrderTask")
    public void syncJkyOrderTask() {
        try {
            JkyOrderRequest orderRequest = new JkyOrderRequest();
            orderRequest.setPageSize(200);
            orderRequest.setFields(JkyConstant.FIELDS_ORDER);

            // 获取上次同步的审核截止时间
            String lastSyncTime = redisUtil.get(JKY_ORDER_LAST_AUDIT_END_TIME);
            if (StringUtils.isEmpty(lastSyncTime)) {
                ResponseMessage<String> responseMessage = jkyOrderClient.queryJkyOrderMaxAuditTime(invokeJkyService.isProdEnv() ? "TLJY" : "TLJYJY");
                if (responseMessage == null || !responseMessage.isOk() || StringUtils.isEmpty(responseMessage.getBody())) {
                    log.info("同步吉客云销售单定时任务，获取已同步销售单最大审核时间失败");
                    return;
                }
                lastSyncTime = responseMessage.getBody();
            }

            // 本次同步的起始时间：上次同步的截止时间 [startModified,endModified)
            orderRequest.setStartModified(lastSyncTime);
            // 本次同步的截止时间：当前时间-3m
            orderRequest.setEndModified(DateUtil.dateToStr(DateUtils.addMinutes(new Date(), -3)));

            long time = System.currentTimeMillis();

            jkyFacade.handleSyncOrder(orderRequest);

            // 更新上次同步的审核截止时间
            redisUtil.set(JKY_ORDER_LAST_AUDIT_END_TIME, orderRequest.getEndModified(), null);
            log.info("syncJkyOrderTask total cost: {}ms", System.currentTimeMillis() - time);
        } catch (Exception e) {
            log.error("同步吉客云销售单，异常：", e);
        }

    }

    /**
     * 重新同步特定状态销售单
     *
     * <AUTHOR>
     * @date 2023/11/17 16:28
     */
//    @Async
//    @Scheduled(cron = "30 3,33 * * * ?")
    public void syncJkyOrderStatusTask() {

        long time = System.currentTimeMillis();
        try {
            JkyOrderRequest orderRequest = new JkyOrderRequest();
            orderRequest.setPageSize(50);
            orderRequest.setFields(JkyConstant.FIELDS_ORDER);

            ResponseMessage<List<String>> responseMessage = jkyOrderClient.listTradeNo2Update();
            if (responseMessage == null || !responseMessage.isOk()) {
                log.info("重新同步特定状态销售单，查询销售单号失败");
                return;
            }

            List<String> tradeNoList = responseMessage.getBody();
            if (CollectionUtils.isNotEmpty(tradeNoList)) {
                List<List<String>> tradeNoLists = Lists.partition(tradeNoList, orderRequest.getPageSize());
                for (List<String> tradeNos : tradeNoLists) {
                    orderRequest.setTradeNo(StringUtils.join(tradeNos, ","));
                    jkyFacade.handleSyncOrder(orderRequest);
                }
            } else {
                log.info("重新同步特定状态销售单，未查到需要相关数据");
            }

            log.info("syncJkyOrderStatusTask total cost: {}ms", System.currentTimeMillis() - time);
        } catch (Exception e) {
            log.error("重新同步特定状态销售单，异常：", e);
        }

    }

    /**
     * 同步吉客云退换补货单
     * 每30分钟执行一次，每次查询半个小时前的审核
     *
     * <AUTHOR>
     * @date 2023/5/31
     */
//    @Async
//    @Scheduled(cron = "12 6,36 * * * ?")
//    @XxlJob(value = "syncJkyReturnTask")
    public void syncJkyReturnTask() {
        try {
            JkyOrderRequest orderRequest = new JkyOrderRequest();
            orderRequest.setPageSize(200);

            ResponseMessage<String> responseMessage = jkyOrderClient.queryJkyReturnMaxAuditTime();
            if (responseMessage == null || !responseMessage.isOk()) {
                log.info("同步吉客云退换补货单定时任务，获取已同步退换补货单最大审核时间失败");
                return;
            }

            // 计算处理开始审核时间距当前时间不能超过7天
            orderRequest.setStartAuditDate(DateUtil.dateToStr(DateUtils.addSeconds(DateUtil.strToDate(responseMessage.getBody()), 1)));
            orderRequest.setEndAuditDate(DateUtil.dateToStr(DateUtils.addMinutes(new Date(), -7)));

            long time = System.currentTimeMillis();

            handleSyncReturn(orderRequest);

            log.info("syncJkyReturnTask total cost: {}ms", System.currentTimeMillis() - time);
        } catch (Exception e) {
            log.error("同步吉客云退换补货单，异常：", e);
        }

    }

    /**
     * 分页查询退换补货单
     */
    private void handleSyncReturn(JkyOrderRequest orderRequest) {

        // 总页数
        int pageTotal = 1;
        for (int pageIndex = 0; pageIndex < pageTotal; pageIndex++) {
            log.info("handleSyncReturn pageIndex: {}, total: {}", pageIndex, pageTotal);
            orderRequest.setPageIndex(pageIndex);
            if (pageIndex == 0) {
                orderRequest.setHasTotal("1");
            } else {
                orderRequest.setHasTotal(null);
            }

            JackyunResponse response = invokeJkyService.syncReturn(orderRequest);
            if (response.getCode() != 200) {
                log.info("同步吉客云退换补货单失败，msg: {}", response.getMsg());
                break;
            }

            JkyReturnResponse jkyReturnResponse = JsonProcessUtil.jsonToBean(JsonProcessUtil.beanToJson(response.getResult().getData()), JkyReturnResponse.class);
            if (pageIndex == 0) {
                // 向上取整，获取总页数
                pageTotal = jkyReturnResponse.getTotalResults() / orderRequest.getPageSize() + (jkyReturnResponse.getTotalResults() % orderRequest.getPageSize() != 0 ? 1 : 0);
            }

            if (CollectionUtils.isNotEmpty(jkyReturnResponse.getReturnChangeList())) {
                jkyOrderClient.syncJkyReturn(jkyReturnResponse.getReturnChangeList(), "task");
            }
        }

    }

    /**
     * 同步吉客云网店订单
     * 每5分钟执行一次，每次查询5分钟前的
     *
     * <AUTHOR>
     * @date 2025/8/13
     */
    //@XxlJob(value = "syncJkyOnlineOrderTask")
    public void syncJkyOnlineOrderTask() {
        try {
            long startTime = System.currentTimeMillis();

            // 获取上次同步的结束时间
            String lastSyncTime = redisUtil.get(JKY_INTERNET_SHOP_ORDER_LAST_END_TIME);
            if (StringUtils.isEmpty(lastSyncTime)) {
                log.info("未获取到同步吉客云网店订单上次执行结束时间，暂不处理");
                return;
            }

            LocalDateTime lastTime;
            try {
                lastTime = LocalDateTime.parse(lastSyncTime, dateTimeFormatter);
            } catch (DateTimeParseException e) {
                log.error("上次同步时间解析失败: {}", lastSyncTime, e);
                return;
            }
            // 构建请求（单次同步，不循环分页）
            JkyOnlineOrderRequest request = new JkyOnlineOrderRequest();
            request.setPageIndex(0);
            request.setPageSize(200);
            request.setCreateTimeBegin(lastTime.format(dateTimeFormatter));
            LocalDateTime newTime = lastTime.plusMinutes(5);
            request.setCreateTimeEnd(newTime.format(dateTimeFormatter));

            // 接口调用
            JackyunResponse response = invokeWithRetry(request, 3);
            if (response == null || response.getCode() != 200) {
                log.warn("同步吉客云网店订单失败, code={}, msg={}",
                        response != null ? response.getCode() : "null",
                        response != null ? response.getMsg() : "null");
                return;
            }

            List<JkyOnlineOrderDataVo> jkyOnlineOrderDatumVos = handleResponse(
                    response.getResult() != null ? response.getResult().getData() : null
            );

            if (!jkyOnlineOrderDatumVos.isEmpty()) {
                jkyOrderClient.batchSyncInternetShopOrderData(jkyOnlineOrderDatumVos);
                log.info("同步吉客云网店订单完成，总数量: {}", jkyOnlineOrderDatumVos.size());
            } else {
                log.info("同步吉客云网店订单，无新数据");
            }

            // 更新 Redis 上次同步时间
            redisUtil.set(JKY_INTERNET_SHOP_ORDER_LAST_END_TIME, newTime.format(dateTimeFormatter), null);

            long cost = System.currentTimeMillis() - startTime;
            log.info("同步吉客云网店订单耗时 {} ms", cost);

        } catch (Exception e) {
            log.error("同步吉客云网店订单，异常：", e);
        }

    }

    private JackyunResponse invokeWithRetry(JkyOnlineOrderRequest request, int retryCount) {
        for (int i = 0; i < retryCount; i++) {
            try {
                JackyunResponse response = invokeJkyService.syncJkyOnlineOrder(request);
                if (response != null && response.getCode() == 200) {
                    return response;
                }
            } catch (Exception e) {
                log.error("第 {} 次调用吉客云接口异常", i + 1, e);
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException ignored) {
            }
        }
        return null;
    }

    /**
     * 处理网店订单返回结果
     *
     * @param data Object
     * <AUTHOR>
     * @date 2025/8/14
     */
    private List<JkyOnlineOrderDataVo> handleResponse(Object data) {
        if (data == null) {
            // 处理空数据场景（返回空列表而非null，避免后续NPE）
            return List.of();
        }
        // 1. 将原始数据转为JSON字符串
        String jsonStr = JsonProcessUtil.beanToJson(data);
        // 2. 使用TypeReference指定泛型类型，将JSON数组转为List<JkyOnlineOrderDataVo>
        return JsonProcessUtil.jsonToBean(jsonStr, new TypeReference<List<JkyOnlineOrderDataVo>>() {
        });
    }

}
