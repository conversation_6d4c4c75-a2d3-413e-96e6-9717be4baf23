package com.tl.rms.order.facade.controller;

import com.google.common.collect.Lists;
import com.tl.rms.lib.beans.web.restful.ResponseMessage;
import com.tl.rms.lib.wsclient.client.jky.InvokeJkyService;
import com.tl.rms.lib.wsclient.client.jky.request.JkyMaterialCreateRequest;
import com.tl.rms.lib.wsclient.client.jky.request.JkyOrderRequest;
import com.tl.rms.lib.wsclient.client.jky.request.JkyPriceCreateRequest;
import com.tl.rms.lib.wsclient.client.jky.response.JackyunResponse;
import com.tl.rms.order.api.JkyOrderFeignClient;
import com.tl.rms.order.domain.constant.JkyConstant;
import com.tl.rms.order.domain.response.JkyOrderResponse;
import com.tl.rms.util.JsonKeysToLowercaseUtil;
import com.tl.rms.util.json.JsonProcessUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 吉客云接口对接
 *
 * <AUTHOR>
 * @date 2025/4/15
 */
@Slf4j
@RestController
@RequestMapping("/jky")
public class JkyFacade {

    @Autowired
    private JkyOrderFeignClient jkyOrderFeignClient;

    @Autowired
    private InvokeJkyService invokeJkyService;


    /**
     * 批量同步物料到吉客云
     * 批量创建货品
     *
     * @param request request
     * @return ResponseMessage
     * <AUTHOR>
     * @date 2025/4/15
     */
    @PostMapping("/material/sync")
    public ResponseMessage<?> syncMaterial(@RequestBody List<JkyMaterialCreateRequest> request) {

        log.info("/material/sync: {}", JsonProcessUtil.beanToJson(request));

        JackyunResponse response = invokeJkyService.createMaterial(request);
        if (response == null) {
            return ResponseMessage.error("调用吉客云批量创建货品接口，返回结果为空");
        }
        if (response.getCode().equals(200)) {
            return ResponseMessage.ok();
        } else {
            return ResponseMessage.error(response.getMsg());
        }
    }

    /**
     * 同步物料价格到吉客云
     * 创建货品价目（已经存在则修改，根据规格、单位、币种定位）
     *
     * @param request request
     * @return ResponseMessage
     * <AUTHOR>
     * @date 2025/4/15
     */
    @PostMapping("/price/sync")
    public ResponseMessage<?> syncPrice(@RequestBody JkyPriceCreateRequest request) {

        log.info("/price/sync: {}", JsonProcessUtil.beanToJson(request));

        JackyunResponse response = invokeJkyService.createPrice(request);
        if (response.getCode() != 200) {
            log.info("新增销售渠道失败，msg: {}", response.getMsg());
        }
        log.info("response: {}", JsonProcessUtil.beanToJson(response));

        return ResponseMessage.ok();
    }

    /**
     * 根据日期查询更新销售单状态
     * 根据创建时间范围查询，并推送大数据
     *
     * @return ResponseMessage
     * <AUTHOR>
     * @date 2024/6/21
     */
    @PostMapping("syncStatusByDate")
    public ResponseMessage syncStatusByDate(@RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate) {

        log.info("syncStatusByDate, startDate: {}, endDate: {}", startDate, endDate);
        long start = System.currentTimeMillis();
        ResponseMessage<List<String>> res = jkyOrderFeignClient.listTradeNoByDate(startDate, endDate);
        if (res == null || !res.isOk()) {
            return ResponseMessage.error("查询销售订单失败");
        }
        if (CollectionUtils.isEmpty(res.getBody())) {
            return ResponseMessage.error("查询销售订单为空");
        }

        List<String> tradeNoList = res.getBody();
        log.info("syncStatusByDate, tradeNoList.size: {}", tradeNoList.size());
        List<List<String>> tradeNoListList = Lists.partition(tradeNoList, 200);
        for (List<String> tradeNos : tradeNoListList) {
            JkyOrderRequest orderRequest = new JkyOrderRequest();
            orderRequest.setPageIndex(0);
            orderRequest.setPageSize(200);
            orderRequest.setFields("tradeStatus,completeTime,signingTime");
            orderRequest.setTradeNo(StringUtils.join(tradeNos, ","));

            JackyunResponse response = invokeJkyService.syncOrder(orderRequest);
            if (response.getCode() != 200) {
                log.info("同步吉客云销售单失败，msg: {}", response.getMsg());
                break;
            }

            JkyOrderResponse jkyOrderResponse = handleResponse(response.getResult().getData());
            if (CollectionUtils.isNotEmpty(jkyOrderResponse.getTrades())) {
                jkyOrderFeignClient.syncJkyOrderStatus(jkyOrderResponse.getTrades(), "task");
            }

        }

        log.info("syncStatusByDate cost: {}ms", System.currentTimeMillis() - start);
        return ResponseMessage.ok();
    }

    /**
     * 通过销售单号重新获取数据
     *
     * @param tradeNo 销售单号，多个用半角逗号分隔
     * @return ResponseMessage
     * <AUTHOR>
     * @date 2024/6/21
     */
    @PostMapping("syncByTradeNo")
    public ResponseMessage syncByTradeNo(@RequestParam("tradeNo") String tradeNo) {

        log.info("syncByTradeNo: {}", tradeNo);

        List<String> tradeNoList = Arrays.asList(tradeNo.split(","));
        List<List<String>> tradeNoListList = Lists.partition(tradeNoList, 200);
        for (List<String> tradeNos : tradeNoListList) {
            JkyOrderRequest orderRequest = new JkyOrderRequest();
            orderRequest.setPageSize(200);
            orderRequest.setFields(JkyConstant.FIELDS_ORDER);
            orderRequest.setTradeNo(StringUtils.join(tradeNos, ","));

            handleSyncOrder(orderRequest);

        }
        return ResponseMessage.ok();
    }

    /**
     * 分页查询销售单
     * 生产环境使用定制接口，非生产环境使用旧查询接口
     */
    public void handleSyncOrder(JkyOrderRequest orderRequest) {

        // 总页数
        int pageTotal = 1;
        for (int pageIndex = 0; pageIndex < pageTotal; pageIndex++) {
            log.info("handleSyncOrder pageIndex: {}, total: {}", pageIndex, pageTotal);
            orderRequest.setPageIndex(pageIndex);
            if (pageIndex == 0) {
                orderRequest.setHasTotal("1");
            } else {
                orderRequest.setHasTotal(null);
            }

            JackyunResponse response = invokeJkyService.syncOrder(orderRequest);
            if (response.getCode() != 200) {
                log.info("同步吉客云销售单失败，msg: {}", response.getMsg());
                break;
            }

            JkyOrderResponse jkyOrderResponse = handleResponse(response.getResult().getData());
            if (pageIndex == 0) {
                // 向上取整，获取总页数
                pageTotal = jkyOrderResponse.getTotalResults() / orderRequest.getPageSize() + (jkyOrderResponse.getTotalResults() % orderRequest.getPageSize() != 0 ? 1 : 0);
            }

            if (CollectionUtils.isNotEmpty(jkyOrderResponse.getTrades())) {
                jkyOrderFeignClient.syncJkyOrder(jkyOrderResponse.getTrades(), "task");
            }
        }

    }

    /**
     * 处理销售单返回结果
     *
     * @param data Object
     * <AUTHOR>
     * @date 2023/11/17 15:50
     */
    private JkyOrderResponse handleResponse(Object data) {

        String jsonStr;
        if (invokeJkyService.isProdEnv()) {
            jsonStr = JsonKeysToLowercaseUtil.convertKeysToLowercase(data.toString()).toJSONString();
        } else {
            jsonStr = JsonProcessUtil.beanToJson(data);
        }
        return JsonProcessUtil.jsonToBean(jsonStr, JkyOrderResponse.class);
    }

}
