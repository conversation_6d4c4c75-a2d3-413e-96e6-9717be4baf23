<?xml version="1.0" encoding="UTF-8" ?>
<validator xmlns="https://github.com/srchen1987/dawdler-series"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="https://github.com/srchen1987/dawdler-series https://cdn.jsdelivr.net/gh/srchen1987/dawdler-series-xsd@main/controller-validator.xsd">
    <validator-fields>
        <validator-field name="orderId" explain="订单编码">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="contractId" explain="合同编码">
            <![CDATA[maxSize:255]]>
        </validator-field>
        <validator-field name="source" explain="操作来源">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="custId" explain="客户编码">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="customerId" explain="客户编码">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="timestamp" explain="时间戳">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="signature" explain="签名">
            <![CDATA[maxSize:100]]>
        </validator-field>
        <validator-field name="cartId" explain="购物车编码">
            <![CDATA[maxSize:200]]>
        </validator-field>
        <validator-field name="userId" explain="用户编码">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="agentId" explain="代理人编码">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="agentLogin" explain="代理人登录名">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="remark" explain="备注">
            <![CDATA[maxSize:255]]>
        </validator-field>
        <validator-field name="remarkHw" explain="备注">
            <![CDATA[maxSize:50]]>
        </validator-field>
        <validator-field name="addressId" explain="地址编码">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="skuId" explain="sku编码">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="trackingNumber" explain="物流单号">
            <![CDATA[maxSize:700]]>
        </validator-field>
        <validator-field name="createDate" explain="创建日期">
            <![CDATA[maxSize:10]]>
        </validator-field>
        <validator-field name="completedDate" explain="完成日期">
            <![CDATA[maxSize:10]]>
        </validator-field>
        <validator-field name="orderStatus" explain="订单状态">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="branchId" explain="分公司编码">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="flag" explain="标识">
            <![CDATA[maxSize:10]]>
        </validator-field>
        <validator-field name="branchName" explain="分公司名称">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="paymentType" explain="支付类型">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="shippingGroupId" explain="支付类型">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="id" explain="订单编码">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="customerName" explain="客户名称">
            <![CDATA[maxSize:100]]>
        </validator-field>
        <validator-field name="projectId" explain="项目名称">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="projectName" explain="支付类型">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="status" explain="状态">
            <![CDATA[maxSize:40]]>
        </validator-field>
        <validator-field name="startDateStr" explain="开始时间">
            <![CDATA[maxSize:10]]>
        </validator-field>
        <validator-field name="endDateStr" explain="截止时间">
            <![CDATA[maxSize:10]]>
        </validator-field>
        <validator-field name="shippingMethod" explain="配送方式">
            <![CDATA[maxSize:40]]>
        </validator-field>
    </validator-fields>

    <validator-fields-groups>
        <validator-fields-group id="orderAndContractId">
            <validator ref="orderId"/>
            <validator ref="contractId"/>
        </validator-fields-group>

        <validator-fields-group id="submitOrderRequest">
            <validator ref="cartId"/>
            <validator ref="source"/>
            <validator ref="custId"/>
            <validator ref="timestamp"/>
            <validator ref="signature"/>
            <validator ref="userId"/>
            <validator ref="agentId"/>
            <validator ref="agentLogin"/>
            <validator ref="remark"/>
            <validator ref="addressId"/>
        </validator-fields-group>

        <validator-fields-group id="orderExportRequest">
            <validator ref="createDate"/>
            <validator ref="completedDate"/>
            <validator ref="orderStatus"/>
            <validator ref="orderId"/>
            <validator ref="branchId"/>
            <validator ref="flag"/>
        </validator-fields-group>

        <validator-fields-group id="querySubmittedOrdersRequest">
            <validator ref="customerId"/>
            <validator ref="branchId"/>
            <validator ref="branchName"/>
            <validator ref="paymentType"/>
        </validator-fields-group>

        <validator-fields-group id="queryShippingGroupRequest">
            <validator ref="customerId"/>
            <validator ref="shippingGroupId"/>
        </validator-fields-group>

        <validator-fields-group id="queryOrderDetailRequest">
            <validator ref="customerId"/>
            <validator ref="orderId"/>
        </validator-fields-group>

        <validator-fields-group id="queryOrderRequest">
            <validator ref="id"/>
            <validator ref="customerId"/>
            <validator ref="customerName"/>
            <validator ref="branchId"/>
            <validator ref="branchName"/>
            <validator ref="projectId"/>
            <validator ref="projectName"/>
            <validator ref="status"/>
            <validator ref="startDateStr"/>
            <validator ref="endDateStr"/>
            <validator ref="paymentType"/>
            <validator ref="shippingMethod"/>
        </validator-fields-group>

        <validator-fields-group id="cartRequest">
            <validator ref="cartId"/>
            <validator ref="source"/>
            <validator ref="custId"/>
            <validator ref="userId"/>
            <validator ref="agentId"/>
            <validator ref="timestamp"/>
            <validator ref="signature"/>
            <validator ref="remarkHw"/>
        </validator-fields-group>
    </validator-fields-groups>

    <validator-mappings>
        <validator-mapping name="/order/handleConfirmRebateAndImprestPayment">
            <validator refgid="orderAndContractId"/>
        </validator-mapping>
        <validator-mapping name="/order/confirmRebateAndImprestPaymentPage">
            <validator ref="orderId"/>
        </validator-mapping>
        <validator-mapping name="/order/submitOrder">
            <validator refgid="submitOrderRequest"/>
        </validator-mapping>
        <validator-mapping name="/order/checkout">
            <validator ref="orderId"/>
        </validator-mapping>
        <validator-mapping name="/order/buyAgainByOrderId">
            <validator ref="orderId"/>
        </validator-mapping>
        <validator-mapping name="/order/buyAgainBySkuId">
            <validator ref="skuId"/>
        </validator-mapping>
        <validator-mapping name="/order/queryLogisticsInfoOne">
            <validator ref="orderId"/>
        </validator-mapping>
        <validator-mapping name="/order/queryLogisticsInfoTwo">
            <validator ref="trackingNumber"/>
        </validator-mapping>
        <validator-mapping name="/order/confirmReceipt">
            <validator ref="orderId"/>
        </validator-mapping>
        <validator-mapping name="/order/orderDetailExport">
            <validator refgid="orderExportRequest"/>
        </validator-mapping>
        <validator-mapping name="/order/orderPaymentExport">
            <validator refgid="orderExportRequest"/>
        </validator-mapping>
        <validator-mapping name="/order/cancelOrder">
            <validator ref="orderId"/>
        </validator-mapping>
        <validator-mapping name="/order/querySubmittedOrders">
            <validator refgid="querySubmittedOrdersRequest"/>
        </validator-mapping>
        <validator-mapping name="/order/queryShippingGroupById">
            <validator refgid="queryShippingGroupRequest"/>
        </validator-mapping>
        <validator-mapping name="/order/queryProductsOfShippingGroup">
            <validator refgid="queryShippingGroupRequest"/>
        </validator-mapping>
        <validator-mapping name="/order/queryOrderDetailById">
            <validator refgid="queryOrderDetailRequest"/>
        </validator-mapping>
        <validator-mapping name="/order/searchOrder">
            <validator refgid="queryOrderRequest"/>
        </validator-mapping>
        <validator-mapping name="/order/censusOrder">
            <validator ref="branchId"/>
        </validator-mapping>
        <validator-mapping name="/order/censusDownload">
            <validator ref="branchId"/>
        </validator-mapping>
        <validator-mapping name="/order/pushOrder">
            <validator refgid="cartRequest"/>
        </validator-mapping>
    </validator-mappings>
</validator>