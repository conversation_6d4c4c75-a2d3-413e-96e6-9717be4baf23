<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>facade</artifactId>
        <groupId>com.tl.rms.facade</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>rms-fulfillment</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-fulfillment-basic</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-fulfillment-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-mq-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-wsclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.api</groupId>
            <artifactId>rms-order-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.api</groupId>
            <artifactId>rms-user-api</artifactId>
        </dependency>

        <!-- BMC -->
        <dependency>
            <groupId>com.pttl</groupId>
            <artifactId>pttlmp-common</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>
</project>
