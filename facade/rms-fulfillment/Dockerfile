FROM openjdk:17.0.2-jdk

ENV LANG C.UTF-8
ENV	APP_NAME=rms-fulfillment
ENV	VERSION=1.0
ENV PORT=8213
ENV APP_SOURCE=/application
ENV JAVA_OPTS="-Dspring.profiles.active=sitk8s"
RUN ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo Asia/Shanghai > /etc/timezone

RUN mkdir $APP_SOURCE
COPY target/$APP_NAME.jar $APP_SOURCE/$APP_NAME.jar 

COPY ./apollo-dev.pttl.com.pem /opt/apollo-dev.pttl.com.pem
COPY ./apollo-fat.pttl.com.pem /opt/apollo-fat.pttl.com.pem
COPY ./apollo-uat.pttl.com.pem /opt/apollo-uat.pttl.com.pem
COPY ./apollo-prod.pttl.com.pem /opt/apollo-prod.pttl.com.pem
RUN $JAVA_HOME/bin/keytool -importcert -noprompt -cacerts  -alias apollo-dev -file /opt/apollo-dev.pttl.com.pem -storepass changeit
RUN $JAVA_HOME/bin/keytool -importcert -noprompt -cacerts  -alias apollo-fat -file /opt/apollo-fat.pttl.com.pem -storepass changeit
RUN $JAVA_HOME/bin/keytool -importcert -noprompt -cacerts  -alias apollo-uat -file /opt/apollo-uat.pttl.com.pem -storepass changeit
RUN $JAVA_HOME/bin/keytool -importcert -noprompt -cacerts  -alias apollo-prod -file /opt/apollo-prod.pttl.com.pem -storepass changeit

RUN sleep 3
RUN $JAVA_HOME/bin/keytool -list -cacerts -storepass changeit | grep -i apollo
WORKDIR $APP_SOURCE

CMD java -Xms1600M -Xmx1600M -XX:MetaspaceSize=320M -XX:MaxMetaspaceSize=320M -XX:MaxDirectMemorySize=64M -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/home/<USER>/logs/prod-rms/rms-order-facade_dump.hprof -Dfile.encoding=UTF-8 $JAVA_OPTS --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.math=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED  --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.text=ALL-UNNAMED -jar $APP_SOURCE/$APP_NAME.jar

EXPOSE $PORT