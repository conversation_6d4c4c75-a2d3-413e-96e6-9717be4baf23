package com.tl.rms.fulfillment.listener;


import com.rabbitmq.client.Channel;
import com.tl.rms.fulfillment.service.order.EbsService;
import com.tl.rms.lib.fulfillment.client.config.FulfillmentClientConfig;
import com.tl.rms.lib.logging.LogFormat;
import com.tl.rms.lib.logging.LogTypeEnum;
import com.tl.rms.lib.mq.config.RabbitContainerFactory;
import com.tl.rms.lib.redis.key.RedisKey;
import com.tl.rms.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 吉客云相关MQ
 *
 * <AUTHOR>
 * @date 2023/5/22
 */
@Slf4j
@Component
public class OrderListener {

    @Autowired
    private EbsService ebsService;

    @Autowired
    private FulfillmentClientConfig fulfillmentClientConfig;

    @Autowired
    private RedissonClient redissonClient;

    private static final String MSG_ID = "message_id";


    /**
     * 吉客云销售单对接EBS
     */
    @RabbitListener(queues = "${mq.queue.jkyOrder2EBS}", containerFactory = RabbitContainerFactory.PARALLEL_CONTAINER_FACTORY)
    public void jkyOrder2EBS(Channel channel, Message message) throws Exception {

        byte[] data = message.getBody();
        String content = new String(data);
        Long tradeId = JsonUtil.fromJson(content, Long.class);

        RLock lock = null;
        boolean isLock = false;
        try {
            // 分布式锁
            lock = redissonClient.getLock(RedisKey.LOCK_JKY_SEND_ORDER_EBS + ":" + tradeId);
            isLock = lock.tryLock(0, 30, TimeUnit.SECONDS);
            if (isLock) {
                log.info("jkyOrder2EBS: {} {}", message.getMessageProperties().getHeaders().get(MSG_ID), tradeId);
                ebsService.handleJkyOrder2Ebs(tradeId);
                Thread.sleep(1200);
            } else {
                log.warn("jkyOrder2EBS failed to get lock: {}", tradeId);
            }
        } catch (InterruptedException e) {
            LogFormat.error(log, fulfillmentClientConfig.getJkyOrder2EBS(), LogTypeEnum.RMS_MQ.getValue(), e, tradeId);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            LogFormat.error(log, fulfillmentClientConfig.getJkyOrder2EBS(), LogTypeEnum.RMS_MQ.getValue(), e, tradeId);
            // MQ是定时任务发送的，不抛出异常
        } finally {
            if (lock != null && lock.isLocked() && isLock) {
                lock.unlock();
            }
        }

    }

    /**
     * 吉客云销售单出库对接EBS
     */
    @RabbitListener(queues = "${mq.queue.jkyOrderOutbound2Ebs}", containerFactory = RabbitContainerFactory.PARALLEL_CONTAINER_FACTORY)
    public void jkyOrderOutbound2Ebs(Channel channel, Message message) throws Exception {

        byte[] data = message.getBody();
        String orderId = new String(data);
        try {
            log.info("jkyOrderOutbound2Ebs: {} {}", message.getMessageProperties().getHeaders().get(MSG_ID), orderId);
            ebsService.handleJkyOrderOutbound2Ebs(orderId);
        } catch (Exception e) {
            LogFormat.error(log, fulfillmentClientConfig.getJkyOrderOutbound2Ebs(), LogTypeEnum.RMS_MQ.getValue(), e, orderId);
            // MQ是定时任务发送的，不抛出异常
        }

    }

    /**
     * 吉客云销售单签收对接EBS
     */
    @RabbitListener(queues = "${mq.queue.jkyOrderSign2Ebs}", containerFactory = RabbitContainerFactory.PARALLEL_CONTAINER_FACTORY)
    public void jkyOrderSign2Ebs(Channel channel, Message message) throws Exception {

        byte[] data = message.getBody();
        String orderId = new String(data);
        try {
            log.info("jkyOrderSign2Ebs: {} {}", message.getMessageProperties().getHeaders().get(MSG_ID), orderId);
            ebsService.handleJkyOrderSign2Ebs(orderId);
        } catch (Exception e) {
            LogFormat.error(log, fulfillmentClientConfig.getJkyOrderSign2Ebs(), LogTypeEnum.RMS_MQ.getValue(), e, orderId);
            // MQ是定时任务发送的，不抛出异常
        }

    }

    /**
     * 吉客云销售单入库对接EBS
     */
    @RabbitListener(queues = "${mq.queue.jkyOrderInbound2Ebs}", containerFactory = RabbitContainerFactory.PARALLEL_CONTAINER_FACTORY)
    public void jkyOrderInbound2Ebs(Channel channel, Message message) throws Exception {

        byte[] data = message.getBody();
        String orderId = new String(data);
        try {
            log.info("jkyOrderInbound2Ebs: {} {}", message.getMessageProperties().getHeaders().get(MSG_ID), orderId);
            ebsService.handleJkyOrderInbound2Ebs(orderId);
        } catch (Exception e) {
            LogFormat.error(log, fulfillmentClientConfig.getJkyOrderInbound2Ebs(), LogTypeEnum.RMS_MQ.getValue(), e, orderId);
            // MQ是定时任务发送的，不抛出异常
        }

    }

    /**
     * 吉客云销售单行取消对接EBS
     */
    @RabbitListener(queues = "${mq.queue.jkyCancelLine2Ebs}", containerFactory = RabbitContainerFactory.PARALLEL_CONTAINER_FACTORY)
    public void jkyCancelLine2Ebs(Channel channel, Message message) throws Exception {

        byte[] data = message.getBody();
        String orderId = new String(data);
        try {
            log.info("jkyCancelLine2Ebs: {} {}", message.getMessageProperties().getHeaders().get(MSG_ID), orderId);
            ebsService.handleJkyCancelLine2Ebs(orderId);
        } catch (Exception e) {
            LogFormat.error(log, fulfillmentClientConfig.getJkyCancelLine2Ebs(), LogTypeEnum.RMS_MQ.getValue(), e, orderId);
            // MQ是定时任务发送的，不抛出异常
        }

    }

}
