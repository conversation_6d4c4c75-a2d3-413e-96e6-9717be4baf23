package com.tl.rms.fulfillment.config;

import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.SentinelServersConfig;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Zheng
 * @describtion redisson 配置
 * @date 2019/9/23
 */
@Configuration
public class RedissionConfig {

    @Autowired
    private RedisProperties redisProperties;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();

        if (redisProperties.getCluster() != null) {
            // 集群模式
            ClusterServersConfig clusterServersConfig = config.useClusterServers();
            List<String> nodes = redisProperties.getCluster().getNodes();
            for (String node : nodes) {
                clusterServersConfig.addNodeAddress("redis://" + node);
            }
            clusterServersConfig.setTimeout((int) redisProperties.getTimeout().toMillis())
                    .setMasterConnectionPoolSize(1024)
                    .setSlaveConnectionPoolSize(1024)
                    .setConnectTimeout(10000)
                    .setRetryInterval(3000);
            if (StringUtils.isNotBlank(redisProperties.getPassword())) {
                clusterServersConfig.setPassword(redisProperties.getPassword());
            }

            return Redisson.create(config);
        } else if (redisProperties.getSentinel() != null) {
            // 哨兵模式
            List<String> newNodes = new ArrayList<>();
            redisProperties.getSentinel().getNodes().stream().forEach((index) -> newNodes.add(
                    index.startsWith("redis://") ? index : "redis://" + index));

            SentinelServersConfig serverConfig = config.useSentinelServers()
                    .addSentinelAddress(newNodes.toArray(new String[0]))
                    .setMasterName(redisProperties.getSentinel().getMaster())
                    .setSentinelPassword(redisProperties.getSentinel().getPassword())
//                    .setReadMode(ReadMode.MASTER)
                    .setTimeout((int) redisProperties.getTimeout().toMillis())
                    .setMasterConnectionPoolSize(1024)
                    .setSlaveConnectionPoolSize(1024)
                    .setConnectTimeout(10000)
                    .setRetryInterval(3000);

            if (StringUtils.isNotBlank(redisProperties.getPassword())) {
                serverConfig.setPassword(redisProperties.getPassword());
            }
        } else {
            // 单机模式
            var maxActive = redisProperties.getJedis().getPool().getMaxActive();
            SingleServerConfig singleServerConfig = config.useSingleServer();
            singleServerConfig.setAddress("redis://" + redisProperties.getHost() + ":" + redisProperties.getPort())
                    .setDatabase(redisProperties.getDatabase())
                    .setTimeout((int) redisProperties.getTimeout().toMillis())
                    .setConnectionPoolSize(maxActive > singleServerConfig.getConnectionMinimumIdleSize() ? maxActive : singleServerConfig.getConnectionMinimumIdleSize());

            if (StringUtils.isNotBlank(redisProperties.getPassword())) {
                singleServerConfig.setPassword(redisProperties.getPassword());
            }
        }

        return Redisson.create(config);
    }
}