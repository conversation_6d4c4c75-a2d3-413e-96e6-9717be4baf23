package com.tl.rms.fulfillment.listener;

import java.util.List;

import com.tl.rms.lib.wsclient.client.tw.InvokeTwService;
import com.tl.rms.lib.wsclient.client.tw.request.TwCommonCallbackRequest;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import com.qimen.api.request.DeliveryorderConfirmRequest;
import com.qimen.api.response.DeliveryorderConfirmResponse;
import com.rabbitmq.client.Channel;
import com.tl.rms.fulfillment.service.order.TwService;
import com.tl.rms.lib.fulfillment.client.config.FulfillmentClientConfig;
import com.tl.rms.lib.fulfillment.message.TWMessage;
import com.tl.rms.lib.fulfillment.message.TwAdjustMessage;
import com.tl.rms.lib.logging.LogFormat;
import com.tl.rms.lib.logging.LogTypeEnum;
import com.tl.rms.lib.mq.config.RabbitContainerFactory;
import com.tl.rms.lib.wsclient.client.qimen.InvokeQiMenService;
import com.tl.rms.util.JsonUtil;
import com.tl.rms.util.json.JsonProcessUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class TwListener {

    private final TwService twService;
    private final FulfillmentClientConfig fulfillmentClientConfig;
    private final RedissonClient redissonClient;
    private final InvokeQiMenService invokeQiMenService;
    private final InvokeTwService invokeTwService;

    private static final String MSG_ID = "message_id";

    /**
     * 吉客云销售单出库
     */
    @RabbitListener(queues = "${mq.queue.jkyOrderOutboundRms}",
        containerFactory = RabbitContainerFactory.PARALLEL_CONTAINER_FACTORY)
    public void jkyOrderOutbound(Channel channel, Message message) throws Exception {

        byte[] data = message.getBody();
        String content = new String(data);
        TWMessage request = JsonProcessUtil.jsonToBean(content, TWMessage.class);
        try {
            log.info("jkyOrderOutbound: {} {}", message.getMessageProperties().getHeaders().get(MSG_ID), request);
            twService.handleJkyOrderOutbound(request);
        } catch (Exception e) {
            LogFormat.error(log, fulfillmentClientConfig.getJkyOrderOutbound(), LogTypeEnum.RMS_MQ.getValue(), e,
                request);
            throw e;
        }

    }

    @RabbitListener(queues = "${mq.queue.rmsTwInbound:rmsTwInbound.uat}",
        containerFactory = RabbitContainerFactory.PARALLEL_CONTAINER_FACTORY)
    public void twInbound(Channel channel, Message message) throws Exception {

        byte[] data = message.getBody();
        String content = new String(data);
        TWMessage request = JsonUtil.fromJson(content, TWMessage.class);
        try {
            log.info("twInbound: {} {}", message.getMessageProperties().getHeaders().get(MSG_ID), request);
            twService.handleTwInbound(request);
        } catch (Exception e) {
            LogFormat.error(log, fulfillmentClientConfig.getRmsTwInbound(), LogTypeEnum.RMS_MQ.getValue(), e, request);
            throw e;
        }
    }

    @RabbitListener(queues = "${mq.queue.rmsTwAdjust:rmsTwAdjust.uat}",
        containerFactory = RabbitContainerFactory.PARALLEL_CONTAINER_FACTORY)
    public void twAdjust(Channel channel, Message message) throws Exception {

        byte[] data = message.getBody();
        String content = new String(data);
        List<TwAdjustMessage> list = JsonUtil.parseArray(content, TwAdjustMessage.class);
        try {
            log.info("twAdjust: {} {}", message.getMessageProperties().getHeaders().get(MSG_ID), list);
//            twService.handleTwAdjust(list);
        } catch (Exception e) {
            LogFormat.error(log, fulfillmentClientConfig.getRmsTwAdjust(), LogTypeEnum.RMS_MQ.getValue(), e, list);
            throw e;
        }
    }

    @RabbitListener(queues = "${mq.queue.rms.twAdjust2Qimen:twAdjust2Qimen.uat}",
            containerFactory = RabbitContainerFactory.PARALLEL_CONTAINER_FACTORY)
    public void twAdjust2Qimen(Channel channel, Message message) throws Exception {
        byte[] data = message.getBody();
        String content = new String(data);
        List<TwAdjustMessage> list = JsonUtil.parseArray(content, TwAdjustMessage.class);
        try {
            log.info("twAdjust2Qimen: {} {}", message.getMessageProperties().getHeaders().get(MSG_ID), list);
            twService.handleTw2QimenAdjust(list);
        } catch (Exception e) {
            LogFormat.error(log, fulfillmentClientConfig.getTwAdjust2Qimen(), LogTypeEnum.RMS_MQ.getValue(), e, list);
            throw e;
        }
    }

    @RabbitListener(queues = "${mq.queue.twRmsConfirmOrder:twRmsConfirmOrder.uat}",
        containerFactory = RabbitContainerFactory.PARALLEL_CONTAINER_FACTORY)
    public void onDeliveryOrderConfirm(Channel channel, Message message) throws Exception {
        byte[] data = message.getBody();
        String content = new String(data);
        try {
            log.info("twRmsConfirmOrder: {} {}", message.getMessageProperties().getHeaders().get(MSG_ID), content);
            DeliveryorderConfirmRequest request = JsonUtil.fromJson(content, DeliveryorderConfirmRequest.class);
            DeliveryorderConfirmResponse deliveryorderConfirmResponse =
                invokeQiMenService.confirmDeliveryOrder(request);

            invokeTwService.callback(content, deliveryorderConfirmResponse,
                TwCommonCallbackRequest.BizType.WMS_DELIVERY_ORDER_CONFIRM_ASYNC);
        } catch (Exception e) {
            LogFormat.error(log, fulfillmentClientConfig.getTwRmsConfirmOrder(), LogTypeEnum.RMS_MQ.getValue(), e,
                content);
            throw e;
        }
    }
}
