package com.tl.rms.fulfillment.controller;

import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.tl.rms.fulfillment.consumer.PttlConsumer;
import com.tl.rms.lib.mq.producer.MqProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
//import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.concurrent.TimeoutException;

/**
 * rabbitMQ接口
 *
 * @version 1.0
 * @date 2021/1/2811:51 PM
 */
@Slf4j
@RestController
@RequestMapping("/rabbitMQ")
public class MQController {

    @Value("${spring.rabbitmq.host}")
    private String ip;
    @Value("${spring.rabbitmq.port}")
    private int port;
    @Value("${spring.rabbitmq.username}")
    private String username;
    @Value("${spring.rabbitmq.password}")
    private String password;
    @Value("${spring.rabbitmq.virtual-host}")
    private String virtualHost;

    @Value("${enablePushDeadLetter:false}")
    private boolean enablePushDeadLetter;

    // default exchange
    private static String exchange = "";
    // 队列名
    private final static String queueName = "rabbit_fail_queue";

    @Autowired
    private MqProducer mqProducer;

    /**
     * 开启自动消费异常队列接口
     * <p>
     * 经能效平台分析 ,未被调用的方法. 1d2ed98215073b4ec6866caaab21c25d
     * 扫描时间: 2024-10-30 16:19:28
     *
     * @version 1.0
     */
    @GetMapping("/rabbitMQSend")
    public String rabbitMQSend() throws IOException, TimeoutException {
        log.info("是否启用消费异常队列值为:{}", enablePushDeadLetter);
        if (!enablePushDeadLetter) {
            return "请先开启自动消费异常队列";
        }

        ConnectionFactory connectionFactory = new ConnectionFactory();
        connectionFactory.setHost(ip);
        connectionFactory.setPort(port);
        connectionFactory.setVirtualHost(virtualHost);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);

        // 2 创建Connection
        Connection connection = null;
        Channel channel = null;
        try {
            connection = connectionFactory.newConnection();

            // 3 创建Channel
            channel = connection.createChannel();

            // 4 创建Queue 已经存在，不需要创建
            // channel.queueDeclare(queueName, true, false, false, null);

            // 5 消费端开始消费信息
            channel.basicConsume(queueName, true, new PttlConsumer(channel, mqProducer));

        } catch (Exception e) {
            log.error("", e);
            return "error";
        } finally {
            try {
                Thread.sleep(30000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            if (channel != null) {
                channel.close();
            }
            if (connection != null) {
                connection.close();
            }
        }

        return "ok";
    }

    /**
     * 经能效平台分析 ,未被调用的方法. c125ab196c36d1643e5e36ade2d173b6
     * 扫描时间: 2024-10-30 16:19:28
     */
//    @Scheduled(cron = "0 0/5 * * * ?")
//    public void pushDeadLetter() throws IOException, TimeoutException {
//        rabbitMQSend();
//    }

}
