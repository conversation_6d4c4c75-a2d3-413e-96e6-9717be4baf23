package com.tl.rms.fulfillment;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;


@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.tl.rms","com.pttl.mp"})
@SpringBootApplication
public class RmsFulfillmentApplication {

    public static void main(String[] args) {
        SpringApplication.run(RmsFulfillmentApplication.class, args);
    }

}