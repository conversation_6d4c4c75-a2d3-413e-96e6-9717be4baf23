package com.tl.rms.fulfillment.service.order.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.qimen.api.request.ReturnorderConfirmRequest;
import com.qimen.api.request.StockchangeReportRequest;
import com.qimen.api.response.StockchangeReportResponse;
import com.tl.rms.fulfillment.service.order.TwService;
import com.tl.rms.lib.fulfillment.message.*;
import com.tl.rms.lib.wsclient.client.qimen.InvokeQiMenService;
import com.tl.rms.lib.wsclient.client.tw.InvokeTwService;
import com.tl.rms.order.api.JkyOrderClient;
import com.tl.rms.order.api.StockClient;
import com.tl.rms.order.domain.enums.JkyOrderShipStatusEnum;
import com.tl.rms.order.domain.enums.StockChangeLogGoodsTypeEnum;
import com.tl.rms.order.domain.enums.StockChangeLogInventoryTypeEnums;
import com.tl.rms.order.domain.po.JkyOrderShip;
import com.tl.rms.order.domain.vo.StockChangeLogSaveReqVo;
import com.tl.rms.user.api.WarehouseClient;
import com.tl.rms.user.domain.vo.WarehouseVo;
import com.tl.rms.util.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class TwServiceImpl implements TwService {

    private final StockClient stockClient;
    private final JkyOrderClient jkyOrderClient;
    private final WarehouseClient warehouseClient;
    private final InvokeQiMenService invokeQiMenService;
    private final InvokeTwService invokeTwService;

    @Override
    public void handleJkyOrderOutbound(TWMessage twMessage) {

        String orderId = twMessage.getOrderId();
        String address = twMessage.getAddress();
        String deliveryId = twMessage.getDeliveryId();
        LocalDateTime transactionDate = LocalDateTimeUtil.parse(twMessage.getTransactionDate(), DateUtil.FORMAT_DEFAULT);
        List<TWCommerceItem> twItemList = twMessage.getTwCommerceItemList();
        List<JkyOrderShip> jkyOrderShips = new ArrayList<>(twItemList.size());
        for (TWCommerceItem twItem : twItemList) {
            JkyOrderShip jkyOrderShip = new JkyOrderShip();
            jkyOrderShip.setOrderId(orderId);
            jkyOrderShip.setMaterialCode(twItem.getMaterialCode());
            jkyOrderShip.setQty(twItem.getOrderQty());
            jkyOrderShip.setOutQty(twItem.getQty());
            jkyOrderShip.setOutTime(transactionDate);
            jkyOrderShip.setOutInv(twItem.getOutInv());
            jkyOrderShip.setAddress(address);
            jkyOrderShip.setDeliveryId(deliveryId);
            jkyOrderShip.setStatus(JkyOrderShipStatusEnum.OUTBOUND.getValue());
            jkyOrderShip.setOut2EbsStatus(2);
            jkyOrderShips.add(jkyOrderShip);
        }
        jkyOrderClient.saveJkyOrderShip(jkyOrderShips);

        List<StockChangeLogSaveReqVo> items = twMessage.getTwCommerceItemList()
                .stream()
                .map(item -> convertOutbound(item, twMessage))
                .collect(Collectors.toList());
        updateWarehouseCodesByName(items);
        stockClient.handleOutbound(items);
    }

    @Override
    public void handleJkyOrderSign(TWMessage twMessage) {

        String orderId = twMessage.getOrderId();
        LocalDateTime transactionDate = LocalDateTimeUtil.parse(twMessage.getTransactionDate(), DateUtil.FORMAT_DEFAULT);
        LocalDateTime actualTime = LocalDateTimeUtil.parse(twMessage.getActualTime(), DateUtil.FORMAT_DEFAULT);
        List<TWCommerceItem> twItemList = twMessage.getTwCommerceItemList();
        List<JkyOrderShip> jkyOrderShips = new ArrayList<>(twItemList.size());
        for (TWCommerceItem twItem : twItemList) {
            JkyOrderShip jkyOrderShip = new JkyOrderShip();
            jkyOrderShip.setOrderId(orderId);
            jkyOrderShip.setMaterialCode(twItem.getMaterialCode());
            jkyOrderShip.setSignQty(twItem.getQty());
            jkyOrderShip.setSignTime(transactionDate);
            jkyOrderShip.setSignActualTime(actualTime);
            jkyOrderShip.setStatus(JkyOrderShipStatusEnum.SIGN.getValue());
            jkyOrderShip.setSign2EbsStatus(2);
            jkyOrderShips.add(jkyOrderShip);
        }

        jkyOrderClient.saveJkyOrderShip(jkyOrderShips);

    }

    @SneakyThrows
    @Override
    public void handleJkyCancelLine(CancelLineResponse message) {

        String orderId = message.getOrderId();
        LocalDateTime cancelDate = LocalDateTimeUtil.parse(message.getCancelDate(), DateUtil.FORMAT_DEFAULT);
        List<CancelLineResponse.Line> lines = message.getLines();
        List<JkyOrderShip> jkyOrderShips = new ArrayList<>(lines.size());
        for (CancelLineResponse.Line line : lines) {
            JkyOrderShip jkyOrderShip = new JkyOrderShip();
            jkyOrderShip.setOrderId(orderId);
            jkyOrderShip.setMaterialCode(line.getMaterialCode());
            jkyOrderShip.setCancelTime(cancelDate);
            jkyOrderShip.setCancelDetail(line.getMessage());
            jkyOrderShip.setStatus(JkyOrderShipStatusEnum.REMOVED.getValue());
            jkyOrderShip.setCancel2EbsStatus(2);
            jkyOrderShips.add(jkyOrderShip);
        }

        jkyOrderClient.saveJkyOrderShip(jkyOrderShips);

    }

    @Override
    public void handleTwInbound(TWMessage twMessage) {
        log.info("MQ接收TW入库消息:{}", twMessage);

        String orderId = twMessage.getOrderId();
        String inCode = twMessage.getDeliveryId();
        LocalDateTime transactionDate = LocalDateTimeUtil.parse(twMessage.getTransactionDate(), DateUtil.FORMAT_DEFAULT);
        List<TWCommerceItem> twItemList = twMessage.getTwCommerceItemList();
        List<JkyOrderShip> jkyOrderShips = new ArrayList<>(twItemList.size());
        for (TWCommerceItem twItem : twItemList) {
            JkyOrderShip jkyOrderShip = new JkyOrderShip();
            jkyOrderShip.setOrderId(orderId);
            jkyOrderShip.setMaterialCode(twItem.getMaterialCode());
            jkyOrderShip.setInQty(twItem.getQty());
            jkyOrderShip.setInTime(transactionDate);
            jkyOrderShip.setInInv(twItem.getInInv());
            jkyOrderShip.setInLocator(twItem.getInLocator());
            jkyOrderShip.setInCode(inCode);
            jkyOrderShip.setStatus(JkyOrderShipStatusEnum.INBOUND.getValue());
            // 需定时任务查找已发送EBS的退货单，新增JkyOrderShip入库数据
            jkyOrderShip.setIn2EbsStatus(3);
            jkyOrderShips.add(jkyOrderShip);
        }

        jkyOrderClient.saveJkyOrderShip(jkyOrderShips);

        List<StockChangeLogSaveReqVo> items = twMessage.getTwCommerceItemList()
                .stream()
                .map(item -> convertTwInbound(item, twMessage))
                .toList();
        updateWarehouseCodesByName(items);
        stockClient.handleInbound(items);
    }

    @Override
    public void handleTwAdjust(List<TwAdjustMessage> list) {
        log.info("MQ接收TW调整消息: {}", list);
        List<StockChangeLogSaveReqVo> items = list
                .stream()
                .map(this::convertTwAdjust)
                .toList();
        updateWarehouseCodesById(items);
        stockClient.handleAdjust(items);
    }

    @Override
    public void handleTw2QimenAdjust(List<TwAdjustMessage> list) {
        log.info("MQ接收TW调整消息: {}", list);
        StockchangeReportRequest request = new StockchangeReportRequest();
        List<StockchangeReportRequest.Item> items = list.stream()
                .map(this::convertToStockChangeItem)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        request.setItems(items);
        StockchangeReportResponse response = invokeQiMenService.reportStockChange(request);
        if (response != null) {
            if (response.isSuccess()) {
                log.info("Successfully reported {} stock change items to Qimen", items.size());
            } else {
                log.error("Failed to report stock change items to Qimen: {}", response.getMessage());
                throw new RuntimeException("奇门库存异动接口调用失败：" + response.getMessage());
            }
        } else {
            log.error("Received null response from Qimen stock change report API");
            throw new RuntimeException("奇门库存异动接口调用失败：收到空响应");
        }
        log.info("Successfully reported {} stock change items to Qimen", items.size());

    }

    private StockchangeReportRequest.Item convertToStockChangeItem(TwAdjustMessage adjustMessage) {
        if (adjustMessage == null) {
            return null;
        }

        StockchangeReportRequest.Item item = new StockchangeReportRequest.Item();
        // 货主编码, string (50) , 必填
        item.setOwnerCode(adjustMessage.getOwnerCode());
        // 仓库编码, string (50)，必填
        item.setWarehouseCode(adjustMessage.getWarehouseCode());
        // 引起异动的单据编码，string（50），必填
        item.setOrderCode(adjustMessage.getOrderCode());
        // 单据类型 ，string（50），JYCK= 一般交易出库单，HHCK= 换货出库 ，BFCK= 补发出库
        // PTCK=普通出库单，DBCK=调拨出库 ，QTCK=其他出库，SCRK=生产入库，LYRK=领用入库，
        // CCRK=残次品入库，CGRK=采购入库 ，DBRK= 调拨入库 ，QTRK= 其他入库 ，XTRK= 销退入库
        // HHRK= 换货入库 CNJG= 仓内加工单 ZTTZ=状态调整单
        item.setOrderType(adjustMessage.getOrderType());
        // 外部业务编码, 消息 ID, 用于去重，用来保证因为网络等原因导致重复传输，请求不会被重复处理，必填
        item.setOutBizCode(adjustMessage.getOutBizCode());
        // 商品编码, string (50) , 必填
        item.setItemCode(adjustMessage.getItemCode());
        // 仓储系统商品 ID, string (50)，条件必填
        item.setItemId(adjustMessage.getItemCode());
        // 库存类型，string (50) , ZP=正品, CC=残次,JS=机损, XS= 箱损, ZT=在途库存，DJ=冻结
        item.setInventoryType(adjustMessage.getInventoryType());
        // 商品变化量，int，必填，可为正为负
        item.setQuantity(NumberUtils.toLong(adjustMessage.getQuantity(), 0L));
        // 异动时间, string (19) , YYYY-MM-DD HH:MM:SS
        item.setChangeTime(adjustMessage.getChangeTime());

        return item;
    }

    private StockChangeLogSaveReqVo convertOutbound(TWCommerceItem item, TWMessage twMessage) {
        StockChangeLogSaveReqVo reqVo = new StockChangeLogSaveReqVo();
        // 物料编码
        reqVo.setMaterialCode(item.getMaterialCode());
        // 仓库编码
        reqVo.setWarehouseCode(item.getInvCode());
        // 仓库名称
        reqVo.setWarehouseName(item.getOutInv());
        // 出入库单号
        reqVo.setCode(twMessage.getDeliveryId());
        // 出入库类型
        reqVo.setType(StockChangeLogInventoryTypeEnums.XSCK.getValue());
        // 出入库时间
        reqVo.setHandleTime(LocalDateTimeUtil.parse(twMessage.getTransactionDate(), DateUtil.FORMAT_DEFAULT));
        // 变更数量
        reqVo.setQty(-item.getQty());
        // 货品属性
        reqVo.setGoodsType(item.getInLocator());
        // 订单号
        reqVo.setOrderId(twMessage.getOrderId());
        // 制单人
        reqVo.setOperator("TW");
        return reqVo;
    }

    private StockChangeLogSaveReqVo convertTwInbound(TWCommerceItem item, TWMessage twMessage) {
        StockChangeLogSaveReqVo reqVo = new StockChangeLogSaveReqVo();
        // 物料编码
        reqVo.setMaterialCode(item.getMaterialCode());
        // 仓库编码
        reqVo.setWarehouseCode(item.getInvCode());
        // 仓库名称
        reqVo.setWarehouseName(item.getInInv());
        // 出入库单号
        reqVo.setCode(twMessage.getDeliveryId());
        // 出入库类型
        reqVo.setType(StockChangeLogInventoryTypeEnums.XTRK.getValue());
        // 出入库时间
        reqVo.setHandleTime(LocalDateTimeUtil.parse(twMessage.getTransactionDate(), DateUtil.FORMAT_DEFAULT));

        // 变更数量
        reqVo.setQty(item.getQty());
        // 货品属性
        reqVo.setGoodsType(item.getInLocator());
        // 订单号
        reqVo.setOrderId(twMessage.getOrderId());
        // 制单人
        reqVo.setOperator("TW");
        return reqVo;
    }

    private StockChangeLogSaveReqVo convertTwAdjust(TwAdjustMessage item) {
        StockChangeLogSaveReqVo reqVo = new StockChangeLogSaveReqVo();
        //物料编码
        reqVo.setMaterialCode(item.getItemCode());
        //仓库编码
        reqVo.setWarehouseCode(item.getWarehouseCode());
        //出入库单号
        reqVo.setCode(item.getOrderCode());
        //出入库类型
        reqVo.setType(item.getOrderType());
        //出入库时间
        reqVo.setHandleTime(LocalDateTimeUtil.parse(item.getChangeTime(), DateUtil.FORMAT_DEFAULT));
        //变更数量
        reqVo.setQty(NumberUtils.toInt(item.getQuantity(), 0));
        //库存类型
        reqVo.setInventoryType(item.getInventoryType());
        //货品属性
        StockChangeLogGoodsTypeEnum goodsTypeEnum = StockChangeLogGoodsTypeEnum.getByValue(item.getWmsInventoryType());
        reqVo.setGoodsType(goodsTypeEnum != null ? goodsTypeEnum.getValue() : item.getWmsInventoryType());
        //制单人
        reqVo.setOperator("TW");
        return reqVo;
    }

    private void updateWarehouseCodesByName(List<StockChangeLogSaveReqVo> items) {
        List<String> warehouseNames = items
                .stream()
                .map(StockChangeLogSaveReqVo::getWarehouseName)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        List<WarehouseVo> warehouseVos = warehouseClient.queryByNames(warehouseNames);
        Map<String, String> warehouseNameMap = warehouseVos.stream().collect(Collectors.toMap(WarehouseVo::getName, WarehouseVo::getId));
        items.forEach(item -> item.setWarehouseCode(warehouseNameMap.get(item.getWarehouseName())));
    }

    private void updateWarehouseCodesById(List<StockChangeLogSaveReqVo> items) {
        List<String> warehouseIds = items
                .stream()
                .map(StockChangeLogSaveReqVo::getWarehouseCode)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        List<WarehouseVo> warehouseVos = warehouseClient.queryByIds(warehouseIds);
        Map<String, String> warehouseNameMap = warehouseVos.stream().collect(Collectors.toMap(WarehouseVo::getId, WarehouseVo::getName));
        items.forEach(item -> item.setWarehouseName(warehouseNameMap.get(item.getWarehouseCode())));
    }

    /**
     * 将TW退货入库确认转换为奇门退货入库确认
     */
    @Override
    public ReturnorderConfirmRequest convertTwReturnOrderConfirmToQiMen(TwReturnOrderConfirmMessage twRequest) {
        if (twRequest == null || twRequest.getReturnOrder() == null) {
            throw new IllegalArgumentException("TW退货入库确认请求不能为空");
        }

        TwReturnOrderConfirmMessage.ReturnOrder twReturnOrder = twRequest.getReturnOrder();
        ReturnorderConfirmRequest qimenRequest = new ReturnorderConfirmRequest();

        // 设置退货入库单主信息
        ReturnorderConfirmRequest.ReturnOrder qimenReturnOrder = new ReturnorderConfirmRequest.ReturnOrder();
        // 退货单编码
        qimenReturnOrder.setReturnOrderCode(twReturnOrder.getReturnOrderCode());
        // 仓库系统订单编码
        qimenReturnOrder.setReturnOrderId(twReturnOrder.getReturnOrderId());
        // 仓库编码
        qimenReturnOrder.setWarehouseCode(twReturnOrder.getWarehouseCode());
        // 外部业务编码
        qimenReturnOrder.setOutBizCode(twReturnOrder.getOutBizCode());
        // 单据类型
        qimenReturnOrder.setOrderType(twReturnOrder.getOrderType());
        // 确认入库时间
        qimenReturnOrder.setOrderConfirmTime(twReturnOrder.getOrderConfirmTime());
        // 退货原因
        qimenReturnOrder.setReturnReason(twReturnOrder.getReturnReason());
        // 物流公司编码
        qimenReturnOrder.setLogisticsCode(twReturnOrder.getLogisticsCode());
        // 物流公司名称
        qimenReturnOrder.setLogisticsName(twReturnOrder.getLogisticsName());
        // 运单号
        qimenReturnOrder.setExpressCode(twReturnOrder.getExpressCode());
        // 备注
        qimenReturnOrder.setRemark(twReturnOrder.getRemark());

        // 设置发件人信息（从TW请求中无法获取，使用默认值或从其他地方获取）
        ReturnorderConfirmRequest.SenderInfo senderInfo = new ReturnorderConfirmRequest.SenderInfo();
        // 公司名称
        senderInfo.setCompany("");
        // 姓名
        senderInfo.setName("");
        // 邮编
        senderInfo.setZipCode("");
        // 固定电话
        senderInfo.setTel("");
        // 移动电话
        senderInfo.setMobile("");
        // 电子邮箱
        senderInfo.setEmail("");
        // 国家二字码
        senderInfo.setCountryCode("");
        // 省份
        senderInfo.setProvince("");
        // 城市
        senderInfo.setCity("");
        // 区域
        senderInfo.setArea("");
        // 村镇
        senderInfo.setTown("");
        // 详细地址
        senderInfo.setDetailAddress("");
        qimenReturnOrder.setSenderInfo(senderInfo);

        qimenRequest.setReturnOrder(qimenReturnOrder);

        // 设置订单行信息
        if (!CollectionUtils.isEmpty(twRequest.getOrderLines())) {
            List<ReturnorderConfirmRequest.OrderLine> qimenOrderLines = new ArrayList<>();

            for (TwReturnOrderConfirmMessage.OrderLine twOrderLine : twRequest.getOrderLines()) {
                ReturnorderConfirmRequest.OrderLine qimenOrderLine = new ReturnorderConfirmRequest.OrderLine();
                // 单据行号
                qimenOrderLine.setOrderLineNo(twOrderLine.getOrderLineNo());
                // 交易平台订单（TW请求中无此字段）
                qimenOrderLine.setSourceOrderCode("");
                // 交易平台子订单编码（TW请求中无此字段）
                qimenOrderLine.setSubSourceOrderCode("");
                // 商品编码
                qimenOrderLine.setItemCode(twOrderLine.getItemCode());
                // 仓储系统商品编码
                qimenOrderLine.setItemId(twOrderLine.getItemId());

                // 设置SN列表
                if (!CollectionUtils.isEmpty(twOrderLine.getSnList())) {
                    if (qimenOrderLine.getSnList() == null) {
                        ReturnorderConfirmRequest.SnList snList = new ReturnorderConfirmRequest.SnList();
                        snList.setSn(twOrderLine.getSnList());
                        qimenOrderLine.setSnList(snList);
                    }
                }

                // 库存类型
                qimenOrderLine.setInventoryType(twOrderLine.getInventoryType());
                // 应收商品数量
                if (Objects.nonNull(twOrderLine.getPlanQty())) {
                    qimenOrderLine.setPlanQty(Long.valueOf(twOrderLine.getPlanQty()));
                }
                // 实收商品数量
                if (Objects.nonNull(twOrderLine.getActualQty())) {
                    qimenOrderLine.setActualQty(twOrderLine.getActualQty().toString());
                }
                // 批次编码（TW请求中无此字段）
                qimenOrderLine.setBatchCode("");
                // 生产日期（TW请求中无此字段）
                qimenOrderLine.setProductDate("");
                // 过期日期（TW请求中无此字段）
                qimenOrderLine.setExpireDate("");
                // 生产批号（TW请求中无此字段）
                qimenOrderLine.setProduceCode("");
                // 商品的二维码（TW请求中无此字段）
                qimenOrderLine.setQrCode("");

                qimenOrderLines.add(qimenOrderLine);
            }

            qimenRequest.setOrderLines(qimenOrderLines);
        }

        return qimenRequest;
    }
}
