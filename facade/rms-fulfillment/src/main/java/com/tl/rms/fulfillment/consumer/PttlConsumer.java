package com.tl.rms.fulfillment.consumer;

import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.DefaultConsumer;
import com.rabbitmq.client.Envelope;
import com.tl.rms.lib.mq.producer.MqProducer;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * <AUTHOR> @Title: ${file_name}
 * @Package ${package_name}
 * : ${todo}
 * @date 2021/1/2912:04 AM
 * @Version 0.1
 */
@Slf4j
public class PttlConsumer extends DefaultConsumer {

    private MqProducer mqProducer;

    public PttlConsumer(Channel channel, MqProducer mqProducer) {
        super(channel);
        this.mqProducer = mqProducer;
    }

    @Override
    public void handleDelivery(String consumerTag, Envelope envelope,
                               AMQP.BasicProperties properties, byte[] body) throws IOException {
        log.info("consumer message, consumerTag: {}, envelope: {}, properties: {}", consumerTag, envelope, properties);
        String message = new String(body);
        Object messageIdObj = properties.getHeaders().get("message_id");
        String messageId = null;
        if (messageIdObj != null) {
            messageId = messageIdObj.toString();
        }
        String routingKey = envelope.getRoutingKey();

        mqProducer.sendMessage(routingKey, message, messageId);
    }
}
