package com.tl.rms.fulfillment.service.order;


public interface EbsService {

    /**
     * 发送吉客云销售单到EBS
     *
     * <AUTHOR>
     * @date 2023/5/22
     */
    void handleJkyOrder2Ebs(Long tradeId);

    /**
     * 处理吉客云订单出库发送EBS
     */
    void handleJkyOrderOutbound2Ebs(String orderId);

    /**
     * 处理吉客云订单签收发送EBS
     */
    void handleJkyOrderSign2Ebs(String orderId);

    /**
     * 处理吉客云订单入库发送EBS
     */
    void handleJkyOrderInbound2Ebs(String orderId);

    /**
     * 处理吉客云订单行取消发送EBS
     */
    void handleJkyCancelLine2Ebs(String orderId);

}