package com.tl.rms.fulfillment.tool;

import com.pttl.mp.common.tlmall.common.reponse.ResponseMessage;
import com.pttl.mp.material.domain.PTMPMaterial;
import com.pttl.mp.tlmall.search.service.CSIPTMPFeignProductService;
import com.tl.rms.fulfillment.exception.FulfillmentException;
import com.tl.rms.fulfillment.util.FulfillmentUtils;
import com.tl.rms.lib.wsclient.client.ebs.InvokeEBSService;
import com.tl.rms.lib.wsclient.client.ebs.inboundsync.InboundResponseInfo;
import com.tl.rms.lib.wsclient.client.ebs.ordersync.MessageRequestType;
import com.tl.rms.lib.wsclient.client.ebs.ordersync.OrderLineType;
import com.tl.rms.lib.wsclient.client.ebs.ordersync.OrderLinesType;
import com.tl.rms.lib.wsclient.client.ebs.outboundsync.OutboundResponseInfoType;
import com.tl.rms.lib.wsclient.client.ebs.outboundsync.OutboundResponseItemType;
import com.tl.rms.lib.wsclient.client.ebs.signsync.SignInfo;
import com.tl.rms.lib.wsclient.client.ebs.signsync.SignInfoItem;
import com.tl.rms.order.api.JkyOrderFeignClient;
import com.tl.rms.order.domain.constant.JkyConstant;
import com.tl.rms.order.domain.po.extend.JkyOrderShipExtend;
import com.tl.rms.order.domain.vo.JkyOrderEbsVO;
import com.tl.rms.order.domain.vo.JkyOrderGoodsEbsVO;
import com.tl.rms.user.api.BusinessUnitClient;
import com.tl.rms.user.api.WarehouseClient;
import com.tl.rms.user.domain.po.Warehouse;
import com.tl.rms.user.domain.vo.WarehouseVo;
import com.tl.rms.util.DateUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 吉客云销售单对接EBS
 *
 * <AUTHOR>
 * @date 2023/5/25
 */
@Slf4j
@Component
public class EbsJkyTools {

    @Autowired
    private InvokeEBSService invokeEBSService;

    @Autowired
    private WarehouseClient warehouseClient;

    @Autowired
    private BusinessUnitClient businessUnitClient;

    @Autowired
    private CSIPTMPFeignProductService productService;

    @Autowired
    private JkyOrderFeignClient jkyOrderFeignClient;

    /**
     * 店铺配置 店铺编码以及对应的客户编码、OU、收款方法
     */
    @Value("#{${jackyun.shop}}")
    private Map<String, Map<String, String>> shopConfig;

    /**
     * 国补订单 一店多主体模式 店铺、省份和分公司对应关系
     */
    @Value("#{${jackyun.multipleOrg:null}}")
    private Map<String, Map<String, String>> multipleOrg;

    /**
     * 国补订单 寄售模式一 店铺名称 包含字样
     */
    private static final String CONSIGNMENT_SHOP_NAME = "-供销平台";

    /**
     * 国补订单 寄售模式二 店铺和各省份寄售客户配置
     */
    @Value("#{${jackyun.consignment}}")
    private Map<String, Map<String, String>> consignment;


    public void sendJkyOrder2EBS(JkyOrderEbsVO order) {

        log.info("吉客云销售单发送EBS：{}", order.getTradeNo());

        Map<String, String> shopMap = shopConfig.get(order.getShopCode());
        if (MapUtils.isEmpty(shopMap)) {
            log.info("{} 未查到店铺配置信息", order.getTradeNo());
            throw new FulfillmentException("未查到店铺配置信息");
        }

        MessageRequestType request = new MessageRequestType();

        // 组装订单数据
        setEbsJkyOrder(request, order, shopMap);

        // 组装订单行信息
        setEbsJkyOrderLine(request, order, shopMap);

        invokeEBSService.invokeEBSOrderSyncService(request);
    }


    /**
     * 组装订单数据
     */
    private void setEbsJkyOrder(MessageRequestType request, JkyOrderEbsVO order, Map<String, String> shopMap) {

        request.setSourceHeaderId(order.getTradeNo());
        request.setSourceCode(JkyConstant.SOURCE_JKY);

        // 业务实体名称 PTTL_OU_01_太力总部
        if (StringUtils.isEmpty(order.getBranchName())) {
            order.setBranchName(shopMap.get("branch"));
            // 国补订单，一店多主体模式，根据收货地址中的省份，来确认分公司；
            if (isGovSubsidy(order.getFlagNames()) && MapUtils.isNotEmpty(multipleOrg.get(order.getShopCode()))) {
                Map<String, String> provinceMap = multipleOrg.get(order.getShopCode());
                if (StringUtils.isNotEmpty(provinceMap.get(order.getState()))) {
                    order.setBranchName(provinceMap.get(order.getState()));
                }
            }
        }
        request.setOrgName(order.getBranchName());

        // 客户编号
        if (StringUtils.isEmpty(order.getCustomerId())) {
            order.setCustomerId(shopMap.get("cust"));
            // 国补订单寄售模式二
            if (StringUtils.isNotEmpty(order.getConsignCode()) && isGovSubsidy(order.getFlagNames())
                    && consignment.get(order.getShopCode()) != null) {
                String custId = consignment.get(order.getShopCode()).get(order.getConsignCode());
                if (StringUtils.isEmpty(custId)) {
                    log.info("未配置寄售客户 {} {}", order.getTradeNo(), order.getConsignCode());
                    throw new FulfillmentException("未配置寄售客户");
                }
                order.setCustomerId(custId);
            }
        }
        request.setCustomerNumber(order.getCustomerId());

        // 国补标记
        if (isGovSubsidy(order.getFlagNames())) {
            request.setGsType("Y");
        } else {
            request.setGsType("N");
        }

        // 订单类型 tradeType: 正向 1 零售业务 2 代发货(来自分销商) 7 售后发货 / 逆向 8 销售退货
        Integer tradeType = order.getTradeType();
        if (tradeType == 1 || tradeType == 2 || tradeType == 7) {
            request.setOrderType(FulfillmentUtils.convert2EbsOrderType(request.getOrgName()));
        } else if (tradeType == 8) {
            request.setOrderType(request.getOrgName().split("_")[2] + "_22退货订单");
            // EBS退货原单号
            request.setReturnOrderNumber(order.getReturnSourceTradeNo());
        } else {
            log.info("{} 交易类型错误 {}", order.getTradeNo(), tradeType);
            throw new FulfillmentException("交易类型错误");
        }

        // 审核时间
        request.setOrderedDate(DateUtil.dateToStr(order.getAuditTime()));
        // 付款条件 默认
        request.setTermName("IMMEDIATE");
        // 退货申请单号
        request.setReturnNum(order.getSourceAfterNo());
        // 网店订单号，为方便EBS统计店铺销量，需拼接店铺对应项目编码
        request.setSamsungLevel(order.getOnlineTradeNo() + "||" + shopMap.get("pro"));
        // 备注
        request.setComments(order.getTradeNo());

    }

    /**
     * 是否国补
     *
     * @param flagNames 标记名称
     * @return boolean
     * <AUTHOR>
     * @date 2025/3/31
     */
    private boolean isGovSubsidy(String flagNames) {
        return StringUtils.isNotEmpty(flagNames) && flagNames.contains("国补订单.");
    }

    /**
     * 是否寄售模式
     *
     * @param order 销售单
     * @return boolean
     * <AUTHOR>
     * @date 2025/5/21
     */
    private boolean isConsignment(JkyOrderEbsVO order) {
        return order.getShopName().contains(CONSIGNMENT_SHOP_NAME) ||
                (consignment.get(order.getShopCode()) != null && StringUtils.isNotEmpty(order.getConsignCode()));
    }

    /**
     * 组装订单行信息
     */
    @SneakyThrows
    private void setEbsJkyOrderLine(MessageRequestType request, JkyOrderEbsVO order, Map<String, String> shopMap) {

        OrderLinesType ebsOrderLineInfo = new OrderLinesType();
        request.setOrderLines(ebsOrderLineInfo);
        List<OrderLineType> ebsOrderLines = ebsOrderLineInfo.getOrderLine();

        List<JkyOrderGoodsEbsVO> goodsDetails = order.getGoodsDetail();
        if (isGovSubsidy(order.getFlagNames()) && goodsDetails.size() > 1) {
            log.info("{} 国补订单货品明细不能多行", order.getTradeNo());
            throw new FulfillmentException("国补订单货品明细不能多行");
        }
        // TODO 后面改为从RMS的物料表上取，物料需要增加税率字段，且BMC同步接口也加
        List<String> matIds = goodsDetails.stream().map(JkyOrderGoodsEbsVO::getBarcode).toList();
        ResponseMessage<List<PTMPMaterial>> matRes = productService.findMaterialByCodes(matIds);
        if (matRes == null || !matRes.isOk() || CollectionUtils.isEmpty(matRes.getBody())) {
            log.info("{} 查询物料信息失败", order.getTradeNo());
            throw new FulfillmentException("查询物料信息失败");
        }
        Map<String, String> matMap = matRes.getBody().stream().collect(Collectors.toMap(PTMPMaterial::getMaterialCode, PTMPMaterial::getTaxRate));

        // 项目从物料上取
        request.setProjectCode(matRes.getBody().get(0).getProjectId());
        String buName = businessUnitClient.getBuNameByProjectId(request.getProjectCode());
        if (StringUtils.isEmpty(buName)) {
            log.info("查询物料上项目信息失败 {}", order.getTradeNo());
            throw new FulfillmentException("查询物料上项目信息失败");
        }
        // 收单地点 (事业部名称)
        request.setInvoiceToAddress(buName);

        // 商家承担国补金额
        BigDecimal merchantAmount = BigDecimal.ZERO;
        if (StringUtils.isNotEmpty(order.getGovSubsidyAmountMerchant())) {
            merchantAmount = new BigDecimal(order.getGovSubsidyAmountMerchant());
        }
        // 国补寄售业务，采购折扣点位
        BigDecimal rate = order.getPurchaseDiscountRate();
        if (rate == null) {
            rate = BigDecimal.ZERO;
            // 寄售模式二
            if (consignment.get(order.getShopCode()) != null && StringUtils.isNotEmpty(order.getConsignCode())) {
                com.tl.rms.lib.beans.web.restful.ResponseMessage<BigDecimal> rateRes = jkyOrderFeignClient.getRateByShopAndModel(order.getShopCode(), goodsDetails.get(0).getGoodsName());
                if (rateRes != null && rateRes.isOk() && rateRes.getBody() != null) {
                    rate = rateRes.getBody();
                    order.setPurchaseDiscountRate(rate);
                }
            }
        }
        if (!BigDecimal.ZERO.equals(rate)) {
            rate = rate.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
        }
        BigDecimal discountFactor = BigDecimal.ONE.subtract(rate);
        // 净应收金额 = (总金额 - 商家承担国补金额) * (1 - 点位)
        // 净国补金额 = (国补金额 - 商家承担国补金额) * (1 - 点位)

        // 总金额，根据明细行上计算的单价，再乘以数量汇总而来，避免因计算单价时的精度问题，导致跟总金额不一致
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (JkyOrderGoodsEbsVO goodsVO : goodsDetails) {
            OrderLineType line = new OrderLineType();
            // 外围系统订单头ID
            line.setSourceHeaderId(order.getTradeNo());
            // 来源
            line.setSourceCode(JkyConstant.SOURCE_JKY);
            // 订购项目
            line.setOrderedItem(goodsVO.getBarcode());
            // 数量
            line.setOrderedQuantity(String.valueOf(goodsVO.getSellCount()));
            // 单位
            line.setOrderQuantityUom(goodsVO.getUnit());
            // 销售单价
            if (goodsVO.getSellCount() == null || BigDecimal.ZERO.compareTo(goodsVO.getSellCount()) == 0) {
                log.info("{} {} 销售数量为0", order.getTradeNo(), goodsVO.getBarcode());
                throw new FulfillmentException(goodsVO.getBarcode() + " 销售数量为0");
            }
            // 分摊后金额大于零
            if (BigDecimal.ZERO.compareTo(goodsVO.getShareFavourableAfterFee()) < 0) {
                goodsVO.setShareFavourableAfterFee(goodsVO.getShareFavourableAfterFee().subtract(merchantAmount));
            } else if (BigDecimal.ZERO.compareTo(goodsVO.getShareFavourableAfterFee()) > 0) {
                goodsVO.setShareFavourableAfterFee(goodsVO.getShareFavourableAfterFee().add(merchantAmount));
            }
            line.setUnitSellingPrice(goodsVO.getShareFavourableAfterFee().multiply(discountFactor).divide(goodsVO.getSellCount(), 2, RoundingMode.HALF_UP).toString());
            // 价目表价格 等同销售单价
            line.setUnitListPrice(line.getUnitSellingPrice());
            // 税码
            if (StringUtils.isEmpty(matMap.get(goodsVO.getBarcode()))) {
                log.info("{} {} 未查到物料税率", order.getTradeNo(), goodsVO.getBarcode());
                throw new FulfillmentException(goodsVO.getBarcode() + " 未查到物料税率");
            }
            line.setTaxCode(matMap.get(goodsVO.getBarcode()));
            // 发运子库存
            line.setShipFromSubinventory(order.getWarehouseCode());
            // 发运货位 默认：warehouseCode-001，即拼接仓库编码和良品货位
            line.setShipFromLocator(order.getWarehouseCode() + "-001");
            if (order.getTradeType() == 8) {
                log.info("吉客云逆向销售单 {} 发送EBS，关联原单单号：{}", order.getTradeNo(), order.getReturnSourceTradeNo());
                line.setSourceOrderId(order.getReturnSourceTradeNo());
            }

            totalAmount = totalAmount.add(goodsVO.getSellCount().multiply(new BigDecimal(line.getUnitSellingPrice())).setScale(2, RoundingMode.HALF_UP));

            ebsOrderLines.add(line);
        }

        order.setEbsAmount(totalAmount.toString());
        // 国补金额
        if (StringUtils.isNotEmpty(order.getGovSubsidyAmount())) {
            request.setGsAmount(new BigDecimal(order.getGovSubsidyAmount()).subtract(merchantAmount).multiply(discountFactor).setScale(2, RoundingMode.HALF_UP).toString());
        } else {
            request.setGsAmount("0");
        }

        // 寄售模式，按额度支付传，其他的按现金传（包括本地国补）
        if (isGovSubsidy(order.getFlagNames()) && isConsignment(order) && !order.getCustomerId().equals(shopMap.get("cust"))) {
            // 现金付款额
            request.setCashAmount("0");
            // 使用额度金额
            request.setCreditAmount(totalAmount.toString());
            // 使用额度类型
            request.setCreditType("FixedQuota");
            // 账期 EBS接收后按照9999做真实账期的获取
            request.setPaymentDays("9999");
            // 客户PO 终端网店订单号
            request.setCustPoNumber(goodsDetails.get(0).getCustomerTradeNo());
        } else {
            request.setCashAmount(totalAmount.toString());
            request.setCreditAmount("0");
            request.setPaymentDays("0");
        }

    }

    /**
     * 发送吉客云订单出库给EBS
     */
    public void sendJkyOrderOutbound2Ebs(String orderId, List<JkyOrderShipExtend> jkyOrderShips) {

        Date outTime = null;
        String whOrgName = null;
        List<String> warehouseNames = jkyOrderShips.stream().map(JkyOrderShipExtend::getOutInv).collect(Collectors.toList());
        List<WarehouseVo> warehouseVos = warehouseClient.queryByNames(warehouseNames);
        Map<String, WarehouseVo> warehouseMap = warehouseVos.stream().collect(Collectors.toMap(WarehouseVo::getName, item -> item));

        OutboundResponseInfoType.DetailItems detailItems = new OutboundResponseInfoType.DetailItems();
        List<OutboundResponseItemType> ebsItems = detailItems.getOutboundResponseItem();
        for (JkyOrderShipExtend jkyOrderShip : jkyOrderShips) {

            outTime = Date.from(jkyOrderShip.getOutTime().atZone(ZoneId.systemDefault()).toInstant());

            OutboundResponseItemType ebsItem = new OutboundResponseItemType();
            // 物料编码
            ebsItem.setSkuId(jkyOrderShip.getMaterialCode());
            // 订单行数量
            ebsItem.setOrigianlQuantity(jkyOrderShip.getQty());
            // 出库数量
            ebsItem.setShippedQuantity(jkyOrderShip.getOutQty());
            // 订单行号
            ebsItem.setExternalLineId(jkyOrderShip.getId().toString());
            ebsItem.setUdf10(ebsItem.getExternalLineId());
            // 订单来源
            ebsItem.setUdf08(JkyConstant.SOURCE_JKY);

            WarehouseVo warehouse = warehouseMap.get(jkyOrderShip.getOutInv());
            if (warehouse != null) {
                whOrgName = warehouse.getWhOrgName();
                // 仓库
                ebsItem.setLotAttr01(warehouse.getId());
                // 货位
                ebsItem.setLotAttr09(warehouse.getId() + "-001");
            }

            warehouseNames.add(jkyOrderShip.getOutInv());
            ebsItems.add(ebsItem);
        }

        OutboundResponseInfoType request = new OutboundResponseInfoType();
        // 分公司名称
        request.setCustomerId(jkyOrderShips.get(0).getBranchName());
        if (StringUtils.isEmpty(request.getCustomerId())) {
            request.setCustomerId(getBranchByShopCode(jkyOrderShips.get(0).getShopCode()));
        }
        // 订单号
        request.setOrderId(orderId);
        request.setCustomerOrderId(request.getOrderId());
        // 仓库物权
        request.setOwnerId(whOrgName);
        // 订单来源
        request.setUdf08(JkyConstant.SOURCE_JKY);
        request.setDetailItems(detailItems);

        // 出库时间
        if (outTime != null) {
            request.setTransactionDate(DateUtil.handleOverMonthDate(outTime, 13));
        }

        invokeEBSService.invokeEBSOrderOutboundService(request);

    }

    /**
     * 发送吉客云订单签收给EBS
     * 不管TW发的签收数量，都给EBS发送全签（即签收数量是行上的数量）
     */
    public void sendJkyOrderSign2Ebs(String orderId, List<JkyOrderShipExtend> jkyOrderShips) {

        Date signTime = null;
        SignInfo.DetailItems detailItems = new SignInfo.DetailItems();
        List<SignInfoItem> ebsItems = detailItems.getSignInfoItem();
        for (JkyOrderShipExtend jkyOrderShip : jkyOrderShips) {

            signTime = Date.from(jkyOrderShip.getSignTime().atZone(ZoneId.systemDefault()).toInstant());

            SignInfoItem ebsItem = new SignInfoItem();
            // 物料编码
            ebsItem.setItemGid(jkyOrderShip.getMaterialCode());
            // 签收数量
            ebsItem.setTransPackageCount(jkyOrderShip.getQty());
            // 订单行号
            ebsItem.setErpDocLineId(jkyOrderShip.getId().toString());
            ebsItems.add(ebsItem);
        }

        SignInfo ebsSign = new SignInfo();
        // 订单号
        ebsSign.setCustomerOrderId(orderId);
        ebsSign.setErpDeliveryNo(ebsSign.getCustomerOrderId());
        // 分公司名称
        ebsSign.setCustomerId(jkyOrderShips.get(0).getBranchName());
        if (StringUtils.isEmpty(ebsSign.getCustomerId())) {
            ebsSign.setCustomerId(getBranchByShopCode(jkyOrderShips.get(0).getShopCode()));
        }
        // 仓库物权
        Warehouse warehouse = warehouseClient.getByName(jkyOrderShips.get(0).getOutInv());
        if (warehouse != null) {
            ebsSign.setOwnerId(warehouse.getWhOrgName());
        }
        ebsSign.setDetailItems(detailItems);

        // 签收时间
        if (signTime != null) {
            ebsSign.setTransactionDate(DateUtil.handleOverMonthDate(signTime, 14));
        }

        invokeEBSService.invokeEBSOrderSignService(ebsSign);

    }

    /**
     * 发送吉客云订单入库给EBS
     */
    public void sendJkyOrderInbound2Ebs(String orderId, List<JkyOrderShipExtend> jkyOrderShips) {

        List<String> warehouseNames = jkyOrderShips.stream().map(JkyOrderShipExtend::getOutInv).collect(Collectors.toList());
        // 入库仓库，避免上线初期该值为空，还保留出库仓库
        warehouseNames.addAll(jkyOrderShips.stream().map(JkyOrderShipExtend::getInInv).toList());

        List<WarehouseVo> warehouseVos = warehouseClient.queryByNames(warehouseNames);
        Map<String, WarehouseVo> warehouseMap = warehouseVos.stream().collect(Collectors.toMap(WarehouseVo::getName, item -> item));

        for (JkyOrderShipExtend jkyOrderShip : jkyOrderShips) {
            // 封装吉客云订单入库给EBS参数
            InboundResponseInfo inboundResponseInfo = new InboundResponseInfo();
            // 分公司名称
            inboundResponseInfo.setBranchName(jkyOrderShip.getBranchName());
            if (StringUtils.isEmpty(inboundResponseInfo.getBranchName())) {
                inboundResponseInfo.setBranchName(getBranchByShopCode(jkyOrderShip.getShopCode()));
            }
            // 订单id
            inboundResponseInfo.setOrderId(orderId);
            // TODO 为啥要固定为2？
            inboundResponseInfo.setLineId(2);
            // 物料id
            inboundResponseInfo.setMatCode(jkyOrderShip.getMaterialCode());
            // 入库数量
            inboundResponseInfo.setInQty(jkyOrderShip.getInQty());
            // TW退货入库编号
            inboundResponseInfo.setJkyToNumber(jkyOrderShip.getInCode());

            // 入库时间
            Date inTime = Date.from(jkyOrderShip.getInTime().atZone(ZoneId.systemDefault()).toInstant());
            if (inTime != null) {
                inboundResponseInfo.setInTime(DateUtil.handleOverMonthDate(inTime, 15));
            }

            String inv = StringUtils.isNotEmpty(jkyOrderShip.getInInv()) ? jkyOrderShip.getInInv() : jkyOrderShip.getOutInv();
            WarehouseVo warehouse = warehouseMap.get(inv);
            if (warehouse != null) {
                // 仓库物权
                inboundResponseInfo.setOwnerId(warehouse.getWhOrgName());
                // 仓库id
                inboundResponseInfo.setWarehouseId(warehouse.getId());
                // 转成“-001”格式
                String inLocator = FulfillmentUtils.IN_LOCATOR_MAP.get(jkyOrderShip.getInLocator());
                // 目的字库货位
                inboundResponseInfo.setInLocator(warehouse.getId() + inLocator);
            }

            // 调用ebs服务
            invokeEBSService.invokeEBSOrderInboundServiceByRest(inboundResponseInfo);
        }
    }

    /**
     * 发送吉客云订单行取消给EBS
     */
    public void sendJkyCancelLine2Ebs(String orderId, List<JkyOrderShipExtend> jkyOrderShips) {

        String shopCode = jkyOrderShips.get(0).getShopCode();
        String branchName = getBranchByShopCode(shopCode);

        for (JkyOrderShipExtend jkyOrderShip : jkyOrderShips) {

            com.tl.rms.lib.wsclient.client.ebs.linecancelsync.MessageRequestType messageRequestType = new com.tl.rms.lib.wsclient.client.ebs.linecancelsync.MessageRequestType();
            // 分公司名称
            messageRequestType.setOrgName(jkyOrderShip.getBranchName());
            if (StringUtils.isEmpty(messageRequestType.getOrgName())) {
                messageRequestType.setOrgName(branchName);
            }
            messageRequestType.setSourceHeaderId(orderId);
            // 订单行Id
            messageRequestType.setSourceLineId(jkyOrderShip.getId().toString());
            // 物料编码
            messageRequestType.setItemNumber(jkyOrderShip.getMaterialCode());
            messageRequestType.setReturnAmount("0");
            invokeEBSService.invokeEBSLineCancelService(messageRequestType);

        }

    }

    private String getBranchByShopCode(String shopCode) {
        Map<String, String> shopMap = shopConfig.get(shopCode);
        return shopMap.get("branch");
    }

}
