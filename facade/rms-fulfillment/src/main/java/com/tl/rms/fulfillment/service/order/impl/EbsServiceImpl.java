package com.tl.rms.fulfillment.service.order.impl;

import com.tl.rms.fulfillment.exception.FulfillmentException;
import com.tl.rms.fulfillment.service.order.EbsService;
import com.tl.rms.fulfillment.tool.EbsJkyTools;
import com.tl.rms.fulfillment.util.FulfillmentUtils;
import com.tl.rms.lib.beans.web.restful.ResponseMessage;
import com.tl.rms.lib.redis.key.RedisKey;
import com.tl.rms.lib.redis.util.RedisUtil;
import com.tl.rms.order.api.JkyOrderClient;
import com.tl.rms.order.domain.po.extend.JkyOrderShipExtend;
import com.tl.rms.order.domain.vo.JkyOrderEbsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class EbsServiceImpl implements EbsService {

    private final JkyOrderClient jkyOrderClient;

    private final EbsJkyTools ebsJkyTools;

    private final RedisUtil redisUtil;

    /**
     * 发送吉客云销售单到EBS
     */
    @Override
    public void handleJkyOrder2Ebs(Long tradeId) {

        ResponseMessage<JkyOrderEbsVO> responseMessage = jkyOrderClient.queryJkyOrderEbsVOByTradeId(tradeId);
        if (responseMessage == null || !responseMessage.isOk() || responseMessage.getBody() == null) {
            log.info("未查询到待发送EBS的吉客云销售单: {}", tradeId);
            return;
        }
        JkyOrderEbsVO order = responseMessage.getBody();

        String redisKey = RedisKey.STEP_JKY_ORDER_TO_EBS + tradeId;
        Integer step = FulfillmentUtils.getStep(redisUtil, redisKey);

        if (step < 1) {
            try {
                ebsJkyTools.sendJkyOrder2EBS(order);
                step++;
            } catch (FulfillmentException fulfillmentException) {
                // 更新销售单异常信息及状态3
                jkyOrderClient.updateJkyOrderSendEbsStatusError(tradeId, fulfillmentException.getMessage());
                // step设置为2,避免再次去更新当前销售单发送ebs状态
                step = 2;
                redisUtil.set(redisKey, step, FulfillmentUtils.STEP_EXPIRE_TIME);
            } catch (Exception e) {
                redisUtil.set(redisKey, step, FulfillmentUtils.STEP_EXPIRE_TIME);
                throw e;
            }
        }

        if (step < 2) {
            try {
                jkyOrderClient.updateJkyOrderSendEbsTimeCustBranch(order);
                step++;
            } catch (Exception e) {
                redisUtil.set(redisKey, step, FulfillmentUtils.STEP_EXPIRE_TIME);
                throw e;
            }
        }

    }

    @Override
    public void handleJkyOrderOutbound2Ebs(String orderId) {

        List<JkyOrderShipExtend> jkyOrderShips = jkyOrderClient.queryByOrderId(orderId);
        List<JkyOrderShipExtend> needRemoveShips = new ArrayList<>();
        List<Long> needUpdateShipIds = new ArrayList<>();
        for (JkyOrderShipExtend jkyOrderShip : jkyOrderShips) {
            if (jkyOrderShip.getOut2EbsStatus() == 2) {
                needUpdateShipIds.add(jkyOrderShip.getId());
            } else {
                needRemoveShips.add(jkyOrderShip);
            }
        }

        if (CollectionUtils.isNotEmpty(needRemoveShips)) {
            jkyOrderShips.removeAll(needRemoveShips);
        }

        if (CollectionUtils.isEmpty(jkyOrderShips)) {
            return;
        }

        ebsJkyTools.sendJkyOrderOutbound2Ebs(orderId, jkyOrderShips);

        jkyOrderClient.updateOut2EbsStatus(needUpdateShipIds);

    }

    @Override
    public void handleJkyOrderSign2Ebs(String orderId) {

        List<JkyOrderShipExtend> jkyOrderShips = jkyOrderClient.queryByOrderId(orderId);
        List<JkyOrderShipExtend> needRemoveShips = new ArrayList<>();
        List<Long> needUpdateShipIds = new ArrayList<>();
        for (JkyOrderShipExtend jkyOrderShip : jkyOrderShips) {
            if (jkyOrderShip.getSign2EbsStatus() == 2) {
                needUpdateShipIds.add(jkyOrderShip.getId());
            } else {
                needRemoveShips.add(jkyOrderShip);
            }
        }

        if (CollectionUtils.isNotEmpty(needRemoveShips)) {
            jkyOrderShips.removeAll(needRemoveShips);
        }

        if (CollectionUtils.isEmpty(jkyOrderShips)) {
            return;
        }

        ebsJkyTools.sendJkyOrderSign2Ebs(orderId, jkyOrderShips);

        jkyOrderClient.updateSign2EbsStatus(needUpdateShipIds);

    }

    @Override
    public void handleJkyOrderInbound2Ebs(String orderId) {

        List<JkyOrderShipExtend> jkyOrderShips = jkyOrderClient.queryByOrderId(orderId);
        List<JkyOrderShipExtend> needRemoveShips = new ArrayList<>();
        List<Long> needUpdateShipIds = new ArrayList<>();
        for (JkyOrderShipExtend jkyOrderShip : jkyOrderShips) {
            if (jkyOrderShip.getIn2EbsStatus() == 2) {
                needUpdateShipIds.add(jkyOrderShip.getId());
            } else {
                needRemoveShips.add(jkyOrderShip);
            }
        }

        if (CollectionUtils.isNotEmpty(needRemoveShips)) {
            jkyOrderShips.removeAll(needRemoveShips);
        }

        if (CollectionUtils.isEmpty(jkyOrderShips)) {
            return;
        }

        ebsJkyTools.sendJkyOrderInbound2Ebs(orderId, jkyOrderShips);

        jkyOrderClient.updateIn2EbsStatus(needUpdateShipIds);

    }

    @Override
    public void handleJkyCancelLine2Ebs(String orderId) {

        List<JkyOrderShipExtend> jkyOrderShips = jkyOrderClient.queryByOrderId(orderId);
        List<JkyOrderShipExtend> needRemoveShips = new ArrayList<>();
        List<Long> needUpdateShipIds = new ArrayList<>();
        for (JkyOrderShipExtend jkyOrderShip : jkyOrderShips) {
            if (jkyOrderShip.getCancel2EbsStatus() == 2) {
                needUpdateShipIds.add(jkyOrderShip.getId());
            } else {
                needRemoveShips.add(jkyOrderShip);
            }
        }

        if (CollectionUtils.isNotEmpty(needRemoveShips)) {
            jkyOrderShips.removeAll(needRemoveShips);
        }

        if (CollectionUtils.isEmpty(jkyOrderShips)) {
            return;
        }

        ebsJkyTools.sendJkyCancelLine2Ebs(orderId, jkyOrderShips);

        jkyOrderClient.updateCancelLine2EbsStatus(needUpdateShipIds);

    }

}
