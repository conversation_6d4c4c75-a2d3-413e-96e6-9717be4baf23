package com.tl.rms.fulfillment.service.order;


import com.tl.rms.lib.fulfillment.message.CancelLineResponse;
import com.qimen.api.request.ReturnorderConfirmRequest;
import com.tl.rms.lib.fulfillment.message.TWMessage;
import com.tl.rms.lib.fulfillment.message.TwAdjustMessage;
import com.tl.rms.lib.fulfillment.message.TwReturnOrderConfirmMessage;

import java.util.List;


public interface TwService {

    /**
     * 处理吉客云订单出库
     */
    void handleJkyOrderOutbound(TWMessage twMessage);

    /**
     * 处理吉客云订单签收
     */
    void handleJkyOrderSign(TWMessage twMessage);

    void handleTwInbound(TWMessage twMessage);

    void handleTwAdjust(List<TwAdjustMessage> list);

    /**
     * 处理吉客云行取消
     */
    void handleJkyCancelLine(CancelLineResponse message);

    void handleTw2QimenAdjust(List<TwAdjustMessage> list);

    ReturnorderConfirmRequest convertTwReturnOrderConfirmToQiMen(TwReturnOrderConfirmMessage twRequest);
}