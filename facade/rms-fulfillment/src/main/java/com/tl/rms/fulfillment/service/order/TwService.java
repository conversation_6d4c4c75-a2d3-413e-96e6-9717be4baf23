package com.tl.rms.fulfillment.service.order;


import com.qimen.api.QimenResponse;
import com.tl.rms.lib.fulfillment.message.TWMessage;
import com.tl.rms.lib.fulfillment.message.TwAdjustMessage;
import com.tl.rms.lib.wsclient.client.tw.request.TwCommonCallbackRequest;

import java.util.List;


public interface TwService {

    void handleJkyOrderOutbound(TWMessage twMessage);

    void handleTwInbound(TWMessage twMessage);

    void handleTwAdjust(List<TwAdjustMessage> list);

    void handleTw2QimenAdjust(List<TwAdjustMessage> list);
}