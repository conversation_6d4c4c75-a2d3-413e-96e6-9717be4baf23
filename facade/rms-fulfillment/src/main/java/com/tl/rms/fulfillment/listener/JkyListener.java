package com.tl.rms.fulfillment.listener;

import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import com.rabbitmq.client.Channel;
import com.tl.rms.fulfillment.service.order.JkyService;
import com.tl.rms.lib.fulfillment.client.config.FulfillmentClientConfig;
import com.tl.rms.lib.logging.LogFormat;
import com.tl.rms.lib.logging.LogTypeEnum;
import com.tl.rms.lib.mq.config.RabbitContainerFactory;
import com.tl.rms.order.domain.vo.QMDeliveryOrderCreateVo;
import com.tl.rms.util.JsonUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class JkyListener {

    private final JkyService jkyService;
    private final FulfillmentClientConfig fulfillmentClientConfig;
    private final RedissonClient redissonClient;

    private static final String MSG_ID = "message_id";

    @RabbitListener(queues = "${mq.queue.rmsJkyDeliveryOrder:rmsJkyDeliveryOrder.uat}",
        containerFactory = RabbitContainerFactory.PARALLEL_CONTAINER_FACTORY)
    public void jkyDeliveryOrder(Channel channel, Message message) throws Exception {
        byte[] data = message.getBody();
        String content = new String(data);
        QMDeliveryOrderCreateVo request = JsonUtil.fromJson(content, QMDeliveryOrderCreateVo.class);
        try {
            log.info("消费发货单创建消息: {} {}", message.getMessageProperties().getHeaders().get(MSG_ID),
                content);
            jkyService.handleDeliveryOrderCreate(request);
        } catch (Exception e) {
            LogFormat.error(log, fulfillmentClientConfig.getRmsTwInbound(), LogTypeEnum.RMS_MQ.getValue(), e, request);
            throw e;
        }
    }
}
