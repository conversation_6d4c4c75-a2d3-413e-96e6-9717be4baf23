package com.tl.rms.fulfillment.util;

import com.tl.rms.lib.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/9/4
 */
@Slf4j
@Component
public class FulfillmentUtils {

    /**
     * 记录步骤的失效时间，10分钟
     */
    public static final long STEP_EXPIRE_TIME = 10 * 60L;

    /**
     * 入库货位转换
     */
    public static final Map<String, String> IN_LOCATOR_MAP;

    static {
        Map<String, String> inLocatorMap = new HashMap<>();
        inLocatorMap.put("OK", "-001");
        inLocatorMap.put("BOXLOSS", "-002");
        inLocatorMap.put("MISSED", "-003");
        inLocatorMap.put("借机", "-004");
        inLocatorMap.put("采购退货", "-005");
        inLocatorMap.put("DAMAGE", "-006");
        inLocatorMap.put("AFTER SALES", "-008");
        IN_LOCATOR_MAP = Collections.unmodifiableMap(inLocatorMap);
    }

    /**
     * 转换为EBS的订单类型 如：EBS公司简称（PTTL_OU_01_太力总部）中的01 + EBS订单类型
     *
     * @param branchName 分公司名称
     * @return String
     * <AUTHOR>
     * @date 2019/8/26
     */
    public static String convert2EbsOrderType(String branchName) {
        if (StringUtils.isEmpty(branchName)) {
            return null;
        }
        String[] arr = branchName.split("_");
        return arr[2] + "_" + "11销售订单";
    }

    /**
     * 获取已执行流程步数
     *
     * @return Integer
     * <AUTHOR>
     * @date 2022/8/10
     */
    public static Integer getStep(RedisUtil redisUtil, String redisKey) {
        Integer step = redisUtil.get(redisKey);
        return step == null ? 0 : step;
    }

}
