package com.tl.rms.fulfillment.service.order.impl;

import com.tl.rms.fulfillment.service.order.JkyService;
import com.tl.rms.lib.wsclient.client.tw.InvokeTwService;
import com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder.ArrayOfPtOutboundOrderDetailInfo;
import com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder.PtOutboundOrderDetailInfo;
import com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder.PtOutboundOrderInfo;
import com.tl.rms.order.api.DeliveryOrderClient;
import com.tl.rms.order.domain.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class JkyServiceImpl implements JkyService {

    private final DeliveryOrderClient deliveryOrderClient;
    private final InvokeTwService invokeTwService;

    // suixin todo 待确认赋值
    @Override
    public void handleDeliveryOrderCreate(QMDeliveryOrderCreateVo request) {
        deliveryOrderClient.create(request);

        QMDeliveryOrderVo deliveryOrder = request.getDeliveryOrder();
        QMDeliveryOrderReceiverVo receiverInfo = deliveryOrder.getReceiverInfo();
        PtOutboundOrderInfo ptOutboundOrderInfo = new PtOutboundOrderInfo();

        // ptOutboundOrderInfo.setWhID();
        ptOutboundOrderInfo.setCustomerOrderID(deliveryOrder.getDeliveryOrderCode());
        ptOutboundOrderInfo.setExternalOrderID(deliveryOrder.getDeliveryOrderCode());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        ptOutboundOrderInfo.setOrderType(deliveryOrder.getOrderType());
        LocalDateTime createTime = deliveryOrder.getCreateTime();
        if (createTime != null) {
            ptOutboundOrderInfo.setOrderDate(createTime.format(formatter));
        }
        // ptOutboundOrderInfo.setCustomerID();
        // ptOutboundOrderInfo.setCustomerName();

        ptOutboundOrderInfo.setReceiptOfName(receiverInfo.getDetailAddress());
        ptOutboundOrderInfo.setReceiptOfProvince(receiverInfo.getProvince());
        ptOutboundOrderInfo.setReceiptOfCity(receiverInfo.getCity());
        ptOutboundOrderInfo.setReceiptOfCounty(receiverInfo.getArea());
        ptOutboundOrderInfo.setReceiptOfAddress(receiverInfo.getDetailAddress());
        ptOutboundOrderInfo.setReceiptOfContact(receiverInfo.getName());
        ptOutboundOrderInfo.setReceiptOfPhone(receiverInfo.getMobile());

        // ptOutboundOrderInfo.setUdf05();
        // ptOutboundOrderInfo.setUdf07();
        // ptOutboundOrderInfo.setUdf08();
        // ptOutboundOrderInfo.setUdf15();
        // ptOutboundOrderInfo.setUdf19();
        // ptOutboundOrderInfo.setUdf27();
        // ptOutboundOrderInfo.setUdf30();
        // ptOutboundOrderInfo.setRemark();
        // ptOutboundOrderInfo.setEdiSyncFlag();
        // ptOutboundOrderInfo.setPickOfAuthLetter();

        ArrayOfPtOutboundOrderDetailInfo arrayOfPtOutboundOrderDetailInfo = new ArrayOfPtOutboundOrderDetailInfo();
        List<PtOutboundOrderDetailInfo> ptOutboundOrderDetailInfos =
            arrayOfPtOutboundOrderDetailInfo.getPtOutboundOrderDetailInfo();
        ptOutboundOrderInfo.setPtOutboundOrderDetailInfos(arrayOfPtOutboundOrderDetailInfo);

        String ownerId = null;
        QMDeliveryOrderLinesVo deliveryOrderLines = request.getDeliveryOrderLines();
        for (QMDeliveryOrderLineVo deliveryOrderLine : deliveryOrderLines.getDeliveryOrderLines()) {
            if (StringUtils.isNotBlank(deliveryOrderLine.getOwnerCode())) {
                ownerId = deliveryOrderLine.getOwnerCode();
            }
            PtOutboundOrderDetailInfo ptOutboundOrderDetailInfo = new PtOutboundOrderDetailInfo();
            ptOutboundOrderDetailInfo.setSkuID(deliveryOrderLine.getItemCode());
            ptOutboundOrderDetailInfo.setQty(BigDecimal.valueOf(deliveryOrderLine.getPlanQty()));
            ptOutboundOrderDetailInfo.setExternalLineID(deliveryOrderLine.getOrderLineNo());
            // ptOutboundOrderDetailInfo.setLotAttr01();
            // ptOutboundOrderDetailInfo.setLotAttr09();
            // ptOutboundOrderDetailInfo.setUdf02();
            // ptOutboundOrderDetailInfo.setUdf10();

            ptOutboundOrderDetailInfos.add(ptOutboundOrderDetailInfo);
        }
        ptOutboundOrderInfo.setOwnerID(ownerId);
        invokeTwService.invokeTwOrderSyncService(ptOutboundOrderInfo);
    }
}