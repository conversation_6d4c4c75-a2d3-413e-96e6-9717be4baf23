package com.tl.rms.fulfillment.service.order.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

import com.tl.rms.lib.wsclient.client.jky.InvokeJkyService;
import com.tl.rms.lib.wsclient.client.jky.request.JkyDepartmentQueryRequest;
import com.tl.rms.lib.wsclient.client.jky.vo.DepartmentVo;
import com.tl.rms.order.api.MaterialClient;
import com.tl.rms.user.api.ShopClient;
import com.tl.rms.user.domain.vo.ShopVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.tl.rms.common.exception.CommonException;
import com.tl.rms.fulfillment.service.order.JkyService;
import com.tl.rms.lib.wsclient.client.tw.InvokeTwService;
import com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder.ArrayOfPtOutboundOrderDetailInfo;
import com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder.PtOutboundOrderDetailInfo;
import com.tl.rms.lib.wsclient.client.tw.request.deliveryOrder.PtOutboundOrderInfo;
import com.tl.rms.lib.wsclient.client.tw.response.Response;
import com.tl.rms.order.api.DeliveryOrderClient;
import com.tl.rms.order.api.JkyOrderClient;
import com.tl.rms.order.domain.vo.*;
import com.tl.rms.user.api.WarehouseClient;
import com.tl.rms.user.domain.vo.WarehouseVo;
import com.tl.rms.util.JsonUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class JkyServiceImpl implements JkyService {

    private final DeliveryOrderClient deliveryOrderClient;
    private final InvokeTwService invokeTwService;
    private final WarehouseClient warehouseClient;
    private final JkyOrderClient jkyOrderClient;
    private final MaterialClient materialClient;
    private final ShopClient shopClient;
    private final InvokeJkyService invokeJkyService;

    @Override
    public void handleDeliveryOrderCreate(QMDeliveryOrderCreateVo request) {
        QMDeliveryOrderVo deliveryOrder = request.getDeliveryOrder();
        log.info("{} 发货单创建 保存数据开始", deliveryOrder.getDeliveryOrderCode());
        deliveryOrderClient.create(request);
        log.info("{} 发货单创建 保存数据结束", deliveryOrder.getDeliveryOrderCode());

        log.info("{} 发货单创建 同步TW开始", deliveryOrder.getDeliveryOrderCode());
        PtOutboundOrderInfo ptOutboundOrderInfo = buildPtOutboundOrderInfo(request);
        Response response = invokeTwService.createDeliveryOrder(ptOutboundOrderInfo);
        log.info("{} 发货单创建 同步TW结束 响应 {}", deliveryOrder.getDeliveryOrderCode(), JsonUtil.toJson(response));
    }

    // suixin todo待赋值
    private PtOutboundOrderInfo buildPtOutboundOrderInfo(QMDeliveryOrderCreateVo request) {
        QMDeliveryOrderVo deliveryOrder = request.getDeliveryOrder();
        QMDeliveryOrderReceiverVo receiverInfo = deliveryOrder.getReceiverInfo();
        QMDeliveryOrderSenderVo senderInfo = deliveryOrder.getSenderInfo();
        PtOutboundOrderInfo ptOutboundOrderInfo = new PtOutboundOrderInfo();

        // 仓库数据查询
        String warehouseCode = deliveryOrder.getWarehouseCode();
        if (StringUtils.isEmpty(warehouseCode)) {
            throw new CommonException("仓库编码为空");
        }
        WarehouseVo warehouse = warehouseClient.getWarehouseById(warehouseCode);
        if (warehouse == null) {
            throw new CommonException(String.format("%s 不存在此仓库", warehouseCode));
        }

        ptOutboundOrderInfo.setWhID(warehouseCode);
        ptOutboundOrderInfo.setCustomerOrderID(deliveryOrder.getDeliveryOrderCode());
        ptOutboundOrderInfo.setExternalOrderID(deliveryOrder.getDeliveryOrderCode());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        ptOutboundOrderInfo.setOrderType("Sales");
        LocalDateTime createTime = deliveryOrder.getCreateTime();
        if (createTime != null) {
            ptOutboundOrderInfo.setOrderDate(createTime.format(formatter));
        }

        ptOutboundOrderInfo.setOwnerID(warehouse.getWhOrgName());
        ptOutboundOrderInfo.setCustomerID(warehouse.getOrgName());
        ptOutboundOrderInfo.setCustomerName(senderInfo.getCompany() + deliveryOrder.getShopNick() + "-零售客户");

        ptOutboundOrderInfo.setReceiptOfName(receiverInfo.getDetailAddress());
        ptOutboundOrderInfo.setReceiptOfProvince(receiverInfo.getProvince());
        ptOutboundOrderInfo.setReceiptOfCity(receiverInfo.getCity());
        ptOutboundOrderInfo.setReceiptOfCounty(receiverInfo.getArea());
        ptOutboundOrderInfo.setReceiptOfAddress(receiverInfo.getDetailAddress());
        ptOutboundOrderInfo.setReceiptOfContact(receiverInfo.getName());
        ptOutboundOrderInfo.setReceiptOfPhone(receiverInfo.getMobile());
        ptOutboundOrderInfo.setUdf01(receiverInfo.getOaid());

        if (StringUtils.isBlank(deliveryOrder.getShopCode())) {
            throw new CommonException("店铺编号为空");
        }
        ShopVo shop = shopClient.getByCode(deliveryOrder.getShopCode());
        if (shop == null) {
            throw new CommonException(String.format("%s 店铺不存在", deliveryOrder.getShopCode()));
        }
        String departmentName = getDepartmentName(shop);
        ptOutboundOrderInfo.setUdf05(departmentName);
        ptOutboundOrderInfo.setUdf08("RMS");
        ptOutboundOrderInfo.setUdf33("N");
        ptOutboundOrderInfo.setRemark(deliveryOrder.getDeliveryOrderCode());
        ptOutboundOrderInfo.setEdiSyncFlag("N");
        ptOutboundOrderInfo.setIsSNShipped("N");
        // ptOutboundOrderInfo.setSource1("QI_MEN");
        // ptOutboundOrderInfo.setSource2("RMS");
        ptOutboundOrderInfo.setSource3("DIAN_KE_TAI_LI");
        ptOutboundOrderInfo.setSourcePlatformCode(deliveryOrder.getSourcePlatformCode());
        ptOutboundOrderInfo.setShopNick(deliveryOrder.getShopNick());
        ArrayOfPtOutboundOrderDetailInfo arrayOfPtOutboundOrderDetailInfo = new ArrayOfPtOutboundOrderDetailInfo();
        List<PtOutboundOrderDetailInfo> ptOutboundOrderDetailInfos =
            arrayOfPtOutboundOrderDetailInfo.getPtOutboundOrderDetailInfo();
        ptOutboundOrderInfo.setPtOutboundOrderDetailInfos(arrayOfPtOutboundOrderDetailInfo);
        QMDeliveryOrderLinesVo deliveryOrderLines = request.getDeliveryOrderLines();
        for (QMDeliveryOrderLineVo deliveryOrderLine : deliveryOrderLines.getDeliveryOrderLines()) {
            PtOutboundOrderDetailInfo ptOutboundOrderDetailInfo = new PtOutboundOrderDetailInfo();
            // 查询物料信息
            MaterialQueryReqVo materialQueryReqVo = new MaterialQueryReqVo();
            materialQueryReqVo.setMaterialCode(deliveryOrderLine.getItemCode());
            MaterialVo material = materialClient.getMaterialByMaterialCodeOrGbCode(materialQueryReqVo);
            if (material == null) {
                throw new CommonException(String.format("%s 物料不存在", deliveryOrderLine.getItemCode()));
            }
            ptOutboundOrderDetailInfo.setSkuID(deliveryOrderLine.getItemCode());
            ptOutboundOrderDetailInfo.setSkuDescr(material.getMaterialDesc());
            ptOutboundOrderDetailInfo.setQty(BigDecimal.valueOf(deliveryOrderLine.getPlanQty()));
            ptOutboundOrderDetailInfo.setExternalLineID(deliveryOrderLine.getOrderLineNo());
            ptOutboundOrderDetailInfo.setLotAttr01(warehouseCode);
            ptOutboundOrderDetailInfo.setLotAttr09(warehouseCode + "-001");
            ptOutboundOrderDetailInfo.setUdf01(deliveryOrderLine.getSourceOrderCode());
            ptOutboundOrderDetailInfo.setUdf02(warehouse.getWhOrgName());
            ptOutboundOrderDetailInfo.setUdf10(deliveryOrderLine.getOrderLineNo());
            ptOutboundOrderDetailInfos.add(ptOutboundOrderDetailInfo);
        }
        return ptOutboundOrderInfo;
    }

    private String getDepartmentName(ShopVo shop) {
        JkyDepartmentQueryRequest jkyDepartmentQueryRequest = new JkyDepartmentQueryRequest();
        jkyDepartmentQueryRequest.setDepartCode(shop.getProjectId());
        List<DepartmentVo> departmentVos = invokeJkyService.listDepartment(jkyDepartmentQueryRequest);
        if (CollectionUtils.isEmpty(departmentVos)) {
            throw new CommonException(String.format("%s 部门不存在", shop.getProjectId()));
        }
        String departmentName = null;
        for (DepartmentVo departmentVo : departmentVos) {
            if (Objects.equals(departmentVo.getDepartCode(), shop.getProjectId())
                && Objects.equals(departmentVo.getCompanyCode(), shop.getCompanyId())) {
                departmentName = departmentVo.getDepartName();
                break;
            }
        }
        if (departmentName == null) {
            throw new CommonException(String.format("%s 部门不存在", shop.getProjectId()));
        }
        return departmentName;
    }
}