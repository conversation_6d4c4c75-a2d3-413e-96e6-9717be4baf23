server:
  port: 8213

spring:
  application:
    name: rms-fulfillment
  config:
    import: >
      apollo://tech.application-public.yml,
      apollo://tech.redis-tlmall.yml,
      apollo://tech.rabbitmq.yml,
      apollo://tech.mq-redis.yml,
      apollo://tech.FulfillmentClientConfig,
      apollo://tech.WebServiceClientConfig,
      apollo://application.yml

jasypt:
  encryptor:
    password: 123456789
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    algorithm: PBEWithMD5AndDES