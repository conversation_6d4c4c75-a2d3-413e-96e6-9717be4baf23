package com.tl.rms.user.facade.user;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tl.rms.lib.beans.web.restful.ResponseMessage;
import com.tl.rms.lib.redis.util.RedisUtil;
import com.tl.rms.lib.session.user.LoginUser;
import com.tl.rms.lib.validate.util.RegisterValidator;
import com.tl.rms.lib.wsclient.client.hr.InvokeHRService;
import com.tl.rms.lib.wsclient.client.hr.login.InterfaceRequest;
import com.tl.rms.lib.wsclient.client.sms.InvokeSmsService;
import com.tl.rms.lib.wsclient.client.sms.SmsSendRequest;
import com.tl.rms.lib.wsclient.client.tw.response.Response;
import com.tl.rms.user.api.RolePermissionClient;
import com.tl.rms.user.api.UserClient;
import com.tl.rms.user.domain.converter.UserConverter;
import com.tl.rms.user.domain.po.User;
import com.tl.rms.user.domain.request.LoginRequest;
import com.tl.rms.user.domain.request.ResetPasswordRequest;
import com.tl.rms.user.domain.vo.*;
import com.tl.rms.user.facade.util.BusinessKmsUtil;
import com.tl.rms.user.facade.util.ExcelExportUtil;
import com.tl.rms.util.SM2Utils;
import com.tl.rms.util.json.JsonProcessUtil;
import com.tl.rms.util.pwd.PasswordEncryptUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户管理
 *
 * <AUTHOR>
 * @date 2025/3/26
 */
@Slf4j
@RestController
@RequestMapping("/user")
public class UserFacade {

    @Autowired
    private UserClient userClient;

    @Autowired
    private RolePermissionClient rolePermissionClient;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private InvokeHRService invokeHRService;

    @Autowired
    private InvokeSmsService invokeSmsService;

    @Value("${login.password.privateKey}")
    private String privateKey;

    @Value("${login.password.publicKey}")
    private String publicKey;

    @Value("${login.captcha.check:true}")
    private boolean captchaCheck;

    @PostConstruct
    public void initSM2() {
        SM2Utils.initKey(privateKey, publicKey);
    }


    /**
     * 用户登录
     *
     * @param login   login
     * @param request request
     * @return ResponseMessage
     * <AUTHOR>
     * @date 2025/3/26
     */
    @PostMapping("/login")
    public ResponseMessage<?> login(@RequestBody LoginRequest login, HttpServletRequest request) {

        log.info("/login: {}", JsonProcessUtil.beanToJson(login));
        if (StringUtils.isEmpty(login.getUserCode())) {
            return ResponseMessage.error("登录账号不能为空");
        }
        if (StringUtils.isEmpty(login.getPassword())) {
            return ResponseMessage.error("密码不能为空");
        }
        // 校验验证码
        String captcha = login.getCaptcha();
        if (StringUtils.isEmpty(captcha)) {
            return ResponseMessage.error("验证码不能为空");
        }

        UserVo userVo = userClient.getUserByCode(login.getUserCode());
        if (userVo == null) {
            return ResponseMessage.error("用户不存在：" + login);
        }
        if (userVo.getUserStatus() == 0) {
            return ResponseMessage.error("用户已停用");
        }
        if (userVo.getDutyStatus() == 0) {
            return ResponseMessage.error("用户已离职");
        }

        login.setPassword(SM2Utils.decrypt(login.getPassword()));

        userVo.setPhone(BusinessKmsUtil.decrypt(userVo.getPhone()));
        String captchaKey = "rms_captcha_001_" + userVo.getPhone();
        if (captchaCheck) {
            Object redisCaptchaObj = redisUtil.getObj(captchaKey, false);
            if (redisCaptchaObj == null) {
                return ResponseMessage.error("短信验证码超时");
            }

            String errorCountKey = captchaKey + "_error_count";
            String redisCaptcha = StringUtils.defaultString(redisCaptchaObj.toString());
            if (!redisCaptcha.equalsIgnoreCase(captcha)) {
                Object errorCount = redisUtil.getObj(errorCountKey, false);
                if (errorCount == null) {
                    redisUtil.set(errorCountKey, 1, 60 * 60 * 24L);
                } else {
                    if (5 <= Integer.parseInt(errorCount.toString())) {
                        return ResponseMessage.error("短信验证码多次验证错误");
                    }
                    redisUtil.incrLong(errorCountKey, 1);
                }
                return ResponseMessage.error("短信验证码错误");

            }
            redisUtil.del(errorCountKey);
        }

        // 内部用户，调用IHR登录接口
        if (userVo.getUserType() == 1) {
            InterfaceRequest ihrRequest = new InterfaceRequest();
            ihrRequest.setOPRID(login.getUserCode());
            ihrRequest.setPASSWORD(login.getPassword());
            ihrRequest.setFLAG("RMS");
            Response ihrRes = invokeHRService.ihrLogin(ihrRequest);
            if (ihrRes.getReturnCode() > 0) {
                return ResponseMessage.error(ihrRes.getErrorMsg());
            }
            // 外部用户，验证账号密码，验证码
        } else if (userVo.getUserType() == 2) {
            if (!PasswordEncryptUtil.checkPassword(login.getPassword(), userVo.getPassword(), userVo.getSalt())) {
                return ResponseMessage.error("用户名或密码错误");
            }
        } else {
            return ResponseMessage.error("用户类型错误");
        }
        setSessionAfterLoginSuccess(userVo, request);

        return ResponseMessage.ok();
    }

    /**
     * 登录成功后,将用户信息存入session
     *
     * @param user    用户信息
     * @param request request
     * <AUTHOR>
     * @date 2025/3/26
     */
    private void setSessionAfterLoginSuccess(UserVo user, HttpServletRequest request) {

        HttpSession session = request.getSession();
        session.setAttribute(LoginUser.ATTR_USER_ID, user.getId());
        session.setAttribute(LoginUser.ATTR_USER_CODE, user.getUserCode());
        session.setAttribute(LoginUser.ATTR_USER_NAME, user.getUserName());
        // TODO
//        session.setAttribute(LoginUser.ATTR_ROLE, user.getServiceRole());
        session.setAttribute(LoginUser.ATTR_PHONE, user.getPhone());
    }

    /**
     * 登出
     *
     * @param request  request
     * @param response response
     * <AUTHOR>
     * @date 2025/3/31
     */
    @PostMapping("/logout")
    public void logout(HttpServletRequest request, HttpServletResponse response) {

        log.info("logout: {}", JsonProcessUtil.beanToJson(LoginUser.getCurrentUser()));

        request.getSession().setAttribute(LoginUser.ATTR_USER_ID, null);
        request.getSession().setAttribute(LoginUser.ATTR_USER_CODE, null);
        request.getSession().setAttribute(LoginUser.ATTR_USER_NAME, null);
        request.getSession().setAttribute(LoginUser.ATTR_ROLE, null);
        request.getSession().setAttribute(LoginUser.ATTR_PHONE, null);
        request.getSession().invalidate();
        /*try {
            String host = request.getHeader("x-forwarded-host").split(",")[0];
            String basePath = request.getScheme() + "://" + host + "/login";
            response.sendRedirect(basePath);
        } catch (IOException e) {
            log.error("logout exception", e);
        }*/

    }

    /**
     * 发送验证码-登录
     *
     * @param userCode 登录名
     * @return ResponseMessage
     * <AUTHOR>
     * @date 2025/3/25
     */
    @PostMapping("/login/sendCaptcha")
    public ResponseMessage<?> sendCaptchaLogin(@RequestParam("userCode") String userCode) {

        log.info("/login/sendCaptcha {}", userCode);
        UserVo userVo = userClient.getUserByCode(userCode);
        if (userVo == null) {
            return ResponseMessage.error("用户不存在：" + userCode);
        }
        if (userVo.getUserStatus() == 0) {
            return ResponseMessage.error("用户已停用");
        }
        if (userVo.getDutyStatus() == 0) {
            return ResponseMessage.error("用户已离职");
        }
        if (StringUtils.isEmpty(userVo.getPhone())) {
            return ResponseMessage.error("手机号为空");
        }
        userVo.setPhone(BusinessKmsUtil.decrypt(userVo.getPhone()));
        if (!RegisterValidator.validPhone(userVo.getPhone())) {
            return ResponseMessage.error("无效的手机号码");
        }

        // send captcha
        return genCaptchaAndSendSms(userVo.getPhone(), "001");
    }

    /**
     * 分页查询用户列表
     *
     * @param queryVo queryVo
     * @return ResponseMessage
     * <AUTHOR>
     * @date 2025/3/26
     */
    @PostMapping("/page")
    public ResponseMessage<?> page(@RequestBody UserQueryReqVo queryVo) {

        Page<UserVo> page = userClient.page(queryVo);
        return ResponseMessage.ok(page);
    }

    /**
     * 查询所有用户
     *
     * @return ResponseMessage<List < UserVo>>
     * <AUTHOR>
     * @date 2025/4/3
     */
    @GetMapping("/list")
    public ResponseMessage<List<UserVo>> list() {
        List<UserVo> list = userClient.list();
        if (CollectionUtils.isNotEmpty(list)) {
            for (UserVo userVo : list) {
                BusinessKmsUtil.decrypt(userVo);
            }
        }
        return ResponseMessage.ok(list);
    }

    /**
     * 根据ID查询用户信息
     *
     * @param id id
     * @return ResponseMessage
     * <AUTHOR>
     * @date 2025/3/26
     */
    @PostMapping("/queryById")
    public ResponseMessage<?> queryById(@RequestParam("id") Long id) {

        UserVo user = userClient.getUserById(id);
        if (user != null) {
            BusinessKmsUtil.decrypt(user);
            return ResponseMessage.ok(user);
        } else {
            return ResponseMessage.error("未查询到用户信息");
        }
    }

    /**
     * 更新用户信息
     *
     * @param userVo userVo
     * @return ResponseMessage
     * <AUTHOR>
     * @date 2025/3/26
     */
    @PostMapping("/update")
    public ResponseMessage<?> update(@RequestBody UserVo userVo) {

        // 外部用户，可更新密码，密文传输
        if (userVo.getUserType() == 2 && StringUtils.isNotEmpty(userVo.getPassword())) {
            // 解密
            userVo.setPassword(SM2Utils.decrypt(userVo.getPassword()));
        }

        BusinessKmsUtil.encrypt(userVo);
        userVo.setUpdateBy(LoginUser.getCurrentUser().getUserId());
        boolean result = userClient.update(userVo);
        if (result) {
            return ResponseMessage.ok();
        } else {
            return ResponseMessage.error("更新用户失败");
        }
    }

    /**
     * 新增用户信息
     *
     * @param userVo userVo
     * @return ResponseMessage
     * <AUTHOR>
     * @date 2025/3/26
     */
    @PostMapping("/create")
    public ResponseMessage<?> create(@RequestBody UserVo userVo) {

        BusinessKmsUtil.encrypt(userVo);
        userVo.setCreateBy(LoginUser.getCurrentUser().getUserId());
        boolean result = userClient.create(userVo);
        if (result) {
            return ResponseMessage.ok();
        } else {
            return ResponseMessage.error("新增用户失败");
        }
    }

    /**
     * 发送验证码
     *
     * @param phone 手机号
     * @param type  类型
     *              002 找回密码
     *              003 重置密码
     * @return ResponseMessage
     * <AUTHOR>
     * @date 2025/3/26
     */
    @PostMapping("/sendCaptcha")
    public ResponseMessage<?> sendCaptcha(@RequestParam("phone") String phone, @RequestParam("type") String type) {

        if (!"002".equals(type) && !"003".equals(type)) {
            return ResponseMessage.error("参数错误");
        }

        if (!RegisterValidator.validPhone(phone)) {
            return ResponseMessage.error("无效的手机号码");
        }

        User user = userClient.getUserByPhone(BusinessKmsUtil.encrypt(phone));
        if (user == null) {
            return ResponseMessage.error("用户不存在：" + phone);
        }
        if (user.getUserStatus() == 0) {
            return ResponseMessage.error("用户已停用");
        }
        if (user.getDutyStatus() == 0) {
            return ResponseMessage.error("用户已离职");
        }
        if (user.getUserType() == 1) {
            return ResponseMessage.error("内部用户不允许重置密码");
        }
        if ("003".equals(type)) {
            LoginUser loginUser = LoginUser.getCurrentUser();
            if (!user.getId().equals(loginUser.getUserId())) {
                return ResponseMessage.error("手机号码不是当前登录用户的");
            }
        }

        return genCaptchaAndSendSms(phone, type);
    }

    /**
     * 生成验证码并发送短信
     *
     * @param phone 手机号
     * @param type  类型
     *              001 登录
     *              002 找回密码
     *              003 重置密码
     * @return ResponseMessage
     * <AUTHOR>
     * @date 2025/6/4
     */
    private ResponseMessage<?> genCaptchaAndSendSms(String phone, String type) {
        // generate captcha
        int captcha = (int) ((Math.random() * 9 + 1) * 100000);
        String key = "rms_captcha_" + type + "_" + phone;
        Object errorCount = redisUtil.getObj(key + "_error_count", false);
        if (errorCount != null && 5 <= Integer.parseInt(errorCount.toString())) {
            return ResponseMessage.error("验证码错误次数过多，请24小时后重试");
        }

        // send captcha sms
        SmsSendRequest request = new SmsSendRequest();
        request.setPhoneNumbers(phone);
        request.setContent("验证码:" + captcha + ",有效期8小时!");
        Response res = invokeSmsService.sendSms(request);
        if (res.getReturnCode() == 1) {
            return ResponseMessage.error(res.getErrorMsg());
        }

        // save captcha redis
        redisUtil.set(key, captcha, 28800L);

        return ResponseMessage.ok();
    }

    /**
     * 找回密码
     *
     * @param request request
     * @return ResponseMessage
     * <AUTHOR>
     * @date 2025/3/26
     */
    @PostMapping("/findPwd")
    public ResponseMessage<?> findPwd(@RequestBody ResetPasswordRequest request) {

        log.info("/findPwd: {}", JsonProcessUtil.beanToJson(request));
        String phone = request.getPhone();
        if (StringUtils.isEmpty(phone)) {
            return ResponseMessage.error("手机号不能为空");
        }
        if (StringUtils.isEmpty(request.getPassword())) {
            return ResponseMessage.error("密码不能为空");
        }
        // 校验验证码
        String captcha = request.getCaptcha();
        if (StringUtils.isEmpty(captcha)) {
            return ResponseMessage.error("验证码不能为空");
        }

        User user = userClient.getUserByPhone(BusinessKmsUtil.encrypt(phone));
        if (user == null) {
            return ResponseMessage.error("用户不存在：" + phone);
        }
        if (user.getUserType() == 1) {
            return ResponseMessage.error("内部用户不允许重置密码");
        }

        request.setPassword(SM2Utils.decrypt(request.getPassword()));

        String captchaKey = "rms_captcha_002_" + phone;
        Object redisCaptchaObj = redisUtil.getObj(captchaKey, false);
        if (redisCaptchaObj == null) {
            return ResponseMessage.error("短信验证码超时");
        }

        String errorCountKey = captchaKey + "_error_count";
        String redisCaptcha = StringUtils.defaultString(redisCaptchaObj.toString());
        if (!redisCaptcha.equalsIgnoreCase(captcha)) {
            Object errorCount = redisUtil.getObj(errorCountKey, false);
            if (errorCount == null) {
                redisUtil.set(errorCountKey, 1, 60 * 60 * 24L);
            } else {
                if (5 <= Integer.parseInt(errorCount.toString())) {
                    return ResponseMessage.error("短信验证码多次验证错误");
                }
                redisUtil.incrLong(errorCountKey, 1);
            }
            return ResponseMessage.error("短信验证码错误");

        }
        redisUtil.del(errorCountKey);

        boolean result = userClient.resetPwd(user.getId(), request.getPassword());
        if (result) {
            return ResponseMessage.ok();
        } else {
            return ResponseMessage.error("重置密码失败");
        }

    }

    /**
     * 重置密码
     *
     * @param request request
     * @return ResponseMessage
     * <AUTHOR>
     * @date 2025/3/26
     */
    @PostMapping("/resetPwd")
    public ResponseMessage<?> resetPwd(@RequestBody ResetPasswordRequest request) {

        log.info("/resetPwd: {}", JsonProcessUtil.beanToJson(request));
        String phone = request.getPhone();
        if (StringUtils.isEmpty(phone)) {
            return ResponseMessage.error("手机号不能为空");
        }
        if (StringUtils.isEmpty(request.getPassword())) {
            return ResponseMessage.error("密码不能为空");
        }
        // 校验验证码
        String captcha = request.getCaptcha();
        if (StringUtils.isEmpty(captcha)) {
            return ResponseMessage.error("验证码不能为空");
        }

        User user = userClient.getUserByPhone(BusinessKmsUtil.encrypt(phone));
        if (user == null) {
            return ResponseMessage.error("用户不存在：" + phone);
        }
        if (user.getUserType() == 1) {
            return ResponseMessage.error("内部用户不允许重置密码");
        }
        LoginUser loginUser = LoginUser.getCurrentUser();
        if (!user.getId().equals(loginUser.getUserId())) {
            return ResponseMessage.error("手机号码不是当前登录用户的");
        }

        request.setPassword(SM2Utils.decrypt(request.getPassword()));

        String captchaKey = "rms_captcha_003_" + phone;
        Object redisCaptchaObj = redisUtil.getObj(captchaKey, false);
        if (redisCaptchaObj == null) {
            return ResponseMessage.error("短信验证码超时");
        }

        String errorCountKey = captchaKey + "_error_count";
        String redisCaptcha = StringUtils.defaultString(redisCaptchaObj.toString());
        if (!redisCaptcha.equalsIgnoreCase(captcha)) {
            Object errorCount = redisUtil.getObj(errorCountKey, false);
            if (errorCount == null) {
                redisUtil.set(errorCountKey, 1, 60 * 60 * 24L);
            } else {
                if (5 <= Integer.parseInt(errorCount.toString())) {
                    return ResponseMessage.error("短信验证码多次验证错误");
                }
                redisUtil.incrLong(errorCountKey, 1);
            }
            return ResponseMessage.error("短信验证码错误");

        }
        redisUtil.del(errorCountKey);

        boolean result = userClient.resetPwd(user.getId(), request.getPassword());
        if (result) {
            return ResponseMessage.ok();
        } else {
            return ResponseMessage.error("重置密码失败");
        }

    }

    @GetMapping("/getCurrentUser")
    public ResponseMessage<UserWithPermissionVo> getCurrentUser() {
        LoginUser currentUser = LoginUser.getCurrentUser();
        UserWithPermissionVo userWithPermissionVo = new UserWithPermissionVo();
        userWithPermissionVo.setUserId(currentUser.getUserId());
        userWithPermissionVo.setUserCode(currentUser.getUserCode());
        userWithPermissionVo.setUserName(currentUser.getUserName());
        userWithPermissionVo.setPhone(BusinessKmsUtil.decrypt(currentUser.getPhone()));

        UserVo userVo = userClient.getUserByCode(userWithPermissionVo.getUserCode());
        if (userVo == null) {
            return ResponseMessage.error("用户不存在：" + userWithPermissionVo.getUserCode());
        }
        if (userVo.getUserStatus() == 0) {
            return ResponseMessage.error("用户已停用");
        }
        if (userVo.getDutyStatus() == 0) {
            return ResponseMessage.error("用户已离职");
        }

        // 外部用户，未修改默认密码，则需要提示用户修改
        if (userVo.getUserType() == 2 && PasswordEncryptUtil.checkPassword(PasswordEncryptUtil.DEFAULT_PASSWORD, userVo.getPassword(), userVo.getSalt())) {
            userWithPermissionVo.setNeedUpdatePassword(true);
        }

        List<PermissionVo> permissions = rolePermissionClient.getPermissionsByUserId(currentUser.getUserId());
        userWithPermissionVo.setPermissions(permissions);
        return ResponseMessage.ok(userWithPermissionVo);
    }

    @PostMapping("/export")
    @Operation(summary = "导出用户")
    public void export(HttpServletResponse response, @RequestBody UserQueryReqVo queryVo) throws IOException {
        int size = 1000;
        int page = 1;

        List<UserExportVo> excelList = new ArrayList<>();
        Page<UserVo> pageResult;
        do {
            queryVo.setPage(page);
            queryVo.setSize(size);
            pageResult = userClient.page(queryVo);
            List<UserVo> records = pageResult.getRecords();
            if (records.isEmpty()) {
                break;
            }
            excelList.addAll(UserConverter.MAPPER.toExportVoList(records));
            page++;
        } while (page <= pageResult.getPages());

        ExcelExportUtil.export(response, UserExportVo.EXPORT_EXCEL_NAME, "用户", UserExportVo.class, excelList);
    }

}
