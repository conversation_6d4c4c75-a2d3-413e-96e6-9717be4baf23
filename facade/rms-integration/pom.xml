<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>facade</artifactId>
        <groupId>com.tl.rms.facade</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>rms-integration</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web-services</artifactId>
        </dependency>
        <dependency>
            <groupId>wsdl4j</groupId>
            <artifactId>wsdl4j</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.api</groupId>
            <artifactId>rms-user-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.api</groupId>
            <artifactId>rms-order-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-wsclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tl.rms.lib</groupId>
            <artifactId>rms-fulfillment-client</artifactId>
        </dependency>
    </dependencies>
</project>