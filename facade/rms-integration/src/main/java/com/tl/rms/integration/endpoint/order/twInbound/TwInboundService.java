package com.tl.rms.integration.endpoint.order.twInbound;

import com.tl.rms.lib.fulfillment.client.service.FulfillmentMessageSender;
import com.tl.rms.lib.fulfillment.message.TWCommerceItem;
import com.tl.rms.lib.fulfillment.message.TWMessage;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;


@Slf4j
@Service
@RequiredArgsConstructor
public class TwInboundService {

    private final FulfillmentMessageSender fulfillmentMessageSender;

    @SneakyThrows
    public String twInbound(TwInboundRequest twInboundRequest) {
        String orderType = twInboundRequest.getUdf08();
        if (!Set.of("JKY", "RMS").contains(orderType)) {
            log.warn("非JKY、RMS订单，不处理，请求信息: {}", twInboundRequest);
            return null;
        }

        List<DetailItem> inboundItems = twInboundRequest.getDetailItems().getDetailItem();
        List<TWCommerceItem> itemList = new ArrayList<>();
        for (DetailItem inboundItem : inboundItems) {
            TWCommerceItem item = new TWCommerceItem();
            item.setItemId(inboundItem.getExternLineId());
            Integer inQty = Integer.valueOf(inboundItem.getReceivedQuantity());
            item.setQty(inQty);
            // 入库仓库名称
            item.setInInv(inboundItem.getLotAttr01());
            item.setInLocator(inboundItem.getLotAttr09());
            item.setMaterialCode(inboundItem.getSkuId());
            itemList.add(item);
        }

        TWMessage twMessage = new TWMessage();
        twMessage.setOrderId(twInboundRequest.getUdf01());
        // TW退货入库编号
        twMessage.setDeliveryId(twInboundRequest.getCustomerOrderId());
        twMessage.setTwCommerceItemList(itemList);
        twMessage.setTransactionDate(twInboundRequest.getTransactionDate());

        fulfillmentMessageSender.sendTwInbound(twMessage);
        return null;
    }
}
