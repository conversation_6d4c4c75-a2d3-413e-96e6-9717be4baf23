
package com.tl.rms.integration.endpoint.order.outbound;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;
import lombok.ToString;


/**
 * <p>detailItem complex type�� Java �ࡣ
 *
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 *
 * <pre>
 * &lt;complexType name="detailItem">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="attribute2" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="attribute3" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="attribute4" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="attribute5" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="externalLineId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="lotAttr01" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="lotAttr09" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="origianlQuantity" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="shippedQuantity" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="skuId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf01" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf02" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf03" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf04" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf05" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf06" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf07" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf08" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf09" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf10" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "detailItem", namespace = "http://service.rms.pttl.com/integration/endpoint", propOrder = {
        "attribute2",
        "attribute3",
        "attribute4",
        "attribute5",
        "externalLineId",
        "lotAttr01",
        "lotAttr09",
        "origianlQuantity",
        "shippedQuantity",
        "skuId",
        "udf01",
        "udf02",
        "udf03",
        "udf04",
        "udf05",
        "udf06",
        "udf07",
        "udf08",
        "udf09",
        "udf10"
})
@ToString
public class DetailItem {

    protected String attribute2;
    protected String attribute3;
    protected String attribute4;
    protected String attribute5;
    protected String externalLineId;
    protected String lotAttr01;
    protected String lotAttr09;
    protected long origianlQuantity;
    protected long shippedQuantity;
    protected String skuId;
    protected String udf01;
    protected String udf02;
    protected String udf03;
    protected String udf04;
    protected String udf05;
    protected String udf06;
    protected String udf07;
    protected String udf08;
    protected String udf09;
    protected String udf10;

    /**
     * ��ȡattribute2���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getAttribute2() {
        return attribute2;
    }

    /**
     * ����attribute2���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setAttribute2(String value) {
        this.attribute2 = value;
    }

    /**
     * ��ȡattribute3���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getAttribute3() {
        return attribute3;
    }

    /**
     * ����attribute3���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setAttribute3(String value) {
        this.attribute3 = value;
    }

    /**
     * ��ȡattribute4���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getAttribute4() {
        return attribute4;
    }

    /**
     * ����attribute4���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setAttribute4(String value) {
        this.attribute4 = value;
    }

    /**
     * ��ȡattribute5���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getAttribute5() {
        return attribute5;
    }

    /**
     * ����attribute5���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setAttribute5(String value) {
        this.attribute5 = value;
    }

    /**
     * ��ȡexternalLineId���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getExternalLineId() {
        return externalLineId;
    }

    /**
     * ����externalLineId���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setExternalLineId(String value) {
        this.externalLineId = value;
    }

    /**
     * ��ȡlotAttr01���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getLotAttr01() {
        return lotAttr01;
    }

    /**
     * ����lotAttr01���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setLotAttr01(String value) {
        this.lotAttr01 = value;
    }

    /**
     * ��ȡlotAttr09���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getLotAttr09() {
        return lotAttr09;
    }

    /**
     * ����lotAttr09���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setLotAttr09(String value) {
        this.lotAttr09 = value;
    }

    /**
     * ��ȡorigianlQuantity���Ե�ֵ��
     */
    public long getOrigianlQuantity() {
        return origianlQuantity;
    }

    /**
     * ����origianlQuantity���Ե�ֵ��
     */
    public void setOrigianlQuantity(long value) {
        this.origianlQuantity = value;
    }

    /**
     * ��ȡshippedQuantity���Ե�ֵ��
     */
    public long getShippedQuantity() {
        return shippedQuantity;
    }

    /**
     * ����shippedQuantity���Ե�ֵ��
     */
    public void setShippedQuantity(long value) {
        this.shippedQuantity = value;
    }

    /**
     * ��ȡskuId���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getSkuId() {
        return skuId;
    }

    /**
     * ����skuId���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSkuId(String value) {
        this.skuId = value;
    }

    /**
     * ��ȡudf01���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf01() {
        return udf01;
    }

    /**
     * ����udf01���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf01(String value) {
        this.udf01 = value;
    }

    /**
     * ��ȡudf02���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf02() {
        return udf02;
    }

    /**
     * ����udf02���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf02(String value) {
        this.udf02 = value;
    }

    /**
     * ��ȡudf03���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf03() {
        return udf03;
    }

    /**
     * ����udf03���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf03(String value) {
        this.udf03 = value;
    }

    /**
     * ��ȡudf04���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf04() {
        return udf04;
    }

    /**
     * ����udf04���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf04(String value) {
        this.udf04 = value;
    }

    /**
     * ��ȡudf05���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf05() {
        return udf05;
    }

    /**
     * ����udf05���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf05(String value) {
        this.udf05 = value;
    }

    /**
     * ��ȡudf06���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf06() {
        return udf06;
    }

    /**
     * ����udf06���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf06(String value) {
        this.udf06 = value;
    }

    /**
     * ��ȡudf07���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf07() {
        return udf07;
    }

    /**
     * ����udf07���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf07(String value) {
        this.udf07 = value;
    }

    /**
     * ��ȡudf08���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf08() {
        return udf08;
    }

    /**
     * ����udf08���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf08(String value) {
        this.udf08 = value;
    }

    /**
     * ��ȡudf09���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf09() {
        return udf09;
    }

    /**
     * ����udf09���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf09(String value) {
        this.udf09 = value;
    }

    /**
     * ��ȡudf10���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf10() {
        return udf10;
    }

    /**
     * ����udf10���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf10(String value) {
        this.udf10 = value;
    }

}
