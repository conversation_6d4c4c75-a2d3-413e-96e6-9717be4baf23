package com.tl.rms.integration;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;


@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.tl.rms"})
@SpringBootApplication
public class RmsIntegrationApplication {

    public static void main(String[] args) {
        SpringApplication.run(RmsIntegrationApplication.class, args);
    }

}
