package com.tl.rms.integration.controller.order;

import com.tl.rms.common.model.ResponseVo;
import com.tl.rms.order.api.MaterialClient;
import com.tl.rms.order.domain.vo.BmcPushMaterialVo;
import com.tl.rms.order.domain.vo.BmcSyncModelIndustryCategoryVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/order")
@RequiredArgsConstructor
@Tag(name = "物料管理")
public class MaterialController {

    private final MaterialClient materialClient;

    @PostMapping("/material/push")
    @Operation(summary = "BMC推送物料信息")
    public ResponseVo<Boolean> pushMaterial(@RequestBody List<BmcPushMaterialVo> bmcPushMaterialVos) {
        log.info("BMC推送物料信息: {}", bmcPushMaterialVos);
        Boolean result = materialClient.bmcPushMaterial(bmcPushMaterialVos);
        log.info("BMC推送物料信息结果: {}", result);
        return ResponseVo.suc(result);
    }

    @PostMapping("/material/syncModelIndustryCategory")
    @Operation(summary = "BMC同步机型与产业品类关联信息")
    public ResponseVo<Boolean> syncModelIndustryCategory(
            @RequestBody List<BmcSyncModelIndustryCategoryVo> bmcSyncModelIndustryCategoryVos) {
        log.info("BMC同步机型与产业品类关联信息: {}", bmcSyncModelIndustryCategoryVos);
        Boolean result = materialClient.bmcSyncModelIndustryCategory(bmcSyncModelIndustryCategoryVos);
        log.info("BMC同步机型与产业品类关联信息结果: {}", result);
        return ResponseVo.suc(result);
    }
}
