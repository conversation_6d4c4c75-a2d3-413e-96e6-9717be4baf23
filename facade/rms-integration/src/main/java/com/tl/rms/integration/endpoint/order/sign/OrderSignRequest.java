
package com.tl.rms.integration.endpoint.order.sign;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;
import lombok.ToString;


/**
 * <p>anonymous complex type�� Java �ࡣ
 *
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="customerId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="customerOrderId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="destLocationGid" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="detailItems" type="{http://service.rms.pttl.com/integration/endpoint}detailItems" minOccurs="0"/>
 *         &lt;element name="erpDeliveryNo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="externalOrderId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="orderId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="orderReleaseClass" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="orderType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ownerId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="transactionDate" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf08" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "customerId",
        "customerOrderId",
        "destLocationGid",
        "detailItems",
        "erpDeliveryNo",
        "externalOrderId",
        "orderId",
        "orderReleaseClass",
        "orderType",
        "ownerId",
        "transactionDate",
        "udf08"
})
@XmlRootElement(name = "orderSignRequest", namespace = "http://service.rms.pttl.com/integration/endpoint")
@ToString
public class OrderSignRequest {

    protected String customerId;
    protected String customerOrderId;
    protected String destLocationGid;
    protected DetailItems detailItems;
    protected String erpDeliveryNo;
    protected String externalOrderId;
    protected String orderId;
    protected String orderReleaseClass;
    protected String orderType;
    protected String ownerId;
    protected String transactionDate;
    protected String udf08;

    /**
     * ��ȡcustomerId���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getCustomerId() {
        return customerId;
    }

    /**
     * ����customerId���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCustomerId(String value) {
        this.customerId = value;
    }

    /**
     * ��ȡcustomerOrderId���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getCustomerOrderId() {
        return customerOrderId;
    }

    /**
     * ����customerOrderId���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCustomerOrderId(String value) {
        this.customerOrderId = value;
    }

    /**
     * ��ȡdestLocationGid���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getDestLocationGid() {
        return destLocationGid;
    }

    /**
     * ����destLocationGid���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDestLocationGid(String value) {
        this.destLocationGid = value;
    }

    /**
     * ��ȡdetailItems���Ե�ֵ��
     *
     * @return possible object is
     * {@link DetailItems }
     */
    public DetailItems getDetailItems() {
        return detailItems;
    }

    /**
     * ����detailItems���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link DetailItems }
     */
    public void setDetailItems(DetailItems value) {
        this.detailItems = value;
    }

    /**
     * ��ȡerpDeliveryNo���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getErpDeliveryNo() {
        return erpDeliveryNo;
    }

    /**
     * ����erpDeliveryNo���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setErpDeliveryNo(String value) {
        this.erpDeliveryNo = value;
    }

    /**
     * ��ȡexternalOrderId���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getExternalOrderId() {
        return externalOrderId;
    }

    /**
     * ����externalOrderId���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setExternalOrderId(String value) {
        this.externalOrderId = value;
    }

    /**
     * ��ȡorderId���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * ����orderId���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOrderId(String value) {
        this.orderId = value;
    }

    /**
     * ��ȡorderReleaseClass���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getOrderReleaseClass() {
        return orderReleaseClass;
    }

    /**
     * ����orderReleaseClass���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOrderReleaseClass(String value) {
        this.orderReleaseClass = value;
    }

    /**
     * ��ȡorderType���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getOrderType() {
        return orderType;
    }

    /**
     * ����orderType���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOrderType(String value) {
        this.orderType = value;
    }

    /**
     * ��ȡownerId���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getOwnerId() {
        return ownerId;
    }

    /**
     * ����ownerId���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOwnerId(String value) {
        this.ownerId = value;
    }

    /**
     * ��ȡtransactionDate���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getTransactionDate() {
        return transactionDate;
    }

    /**
     * ����transactionDate���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTransactionDate(String value) {
        this.transactionDate = value;
    }

    /**
     * ��ȡudf08���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf08() {
        return udf08;
    }

    /**
     * ����udf08���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf08(String value) {
        this.udf08 = value;
    }

}
