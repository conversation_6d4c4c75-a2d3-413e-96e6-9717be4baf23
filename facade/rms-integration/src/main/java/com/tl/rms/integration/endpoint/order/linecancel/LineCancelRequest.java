
package com.tl.rms.integration.endpoint.order.linecancel;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;
import lombok.ToString;


/**
 * <p>anonymous complex type�� Java �ࡣ
 *
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="cancelDate" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="commandSource" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="customerOrderId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="detailItems" type="{http://service.rms.pttl.com/integration/endpoint}detailItems" minOccurs="0"/>
 *         &lt;element name="externalOrderId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="success" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "cancelDate",
        "commandSource",
        "customerOrderId",
        "detailItems",
        "externalOrderId",
        "success"
})
@XmlRootElement(name = "lineCancelRequest", namespace = "http://service.rms.pttl.com/integration/endpoint")
@ToString
public class LineCancelRequest {

    protected String cancelDate;
    protected String commandSource;
    protected String customerOrderId;
    protected DetailItems detailItems;
    protected String externalOrderId;
    protected String success;

    /**
     * ��ȡcancelDate���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getCancelDate() {
        return cancelDate;
    }

    /**
     * ����cancelDate���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCancelDate(String value) {
        this.cancelDate = value;
    }

    /**
     * ��ȡcommandSource���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getCommandSource() {
        return commandSource;
    }

    /**
     * ����commandSource���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCommandSource(String value) {
        this.commandSource = value;
    }

    /**
     * ��ȡcustomerOrderId���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getCustomerOrderId() {
        return customerOrderId;
    }

    /**
     * ����customerOrderId���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCustomerOrderId(String value) {
        this.customerOrderId = value;
    }

    /**
     * ��ȡdetailItems���Ե�ֵ��
     *
     * @return possible object is
     * {@link DetailItems }
     */
    public DetailItems getDetailItems() {
        return detailItems;
    }

    /**
     * ����detailItems���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link DetailItems }
     */
    public void setDetailItems(DetailItems value) {
        this.detailItems = value;
    }

    /**
     * ��ȡexternalOrderId���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getExternalOrderId() {
        return externalOrderId;
    }

    /**
     * ����externalOrderId���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setExternalOrderId(String value) {
        this.externalOrderId = value;
    }

    /**
     * ��ȡsuccess���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getSuccess() {
        return success;
    }

    /**
     * ����success���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSuccess(String value) {
        this.success = value;
    }

}
