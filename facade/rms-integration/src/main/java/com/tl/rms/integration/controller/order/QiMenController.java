package com.tl.rms.integration.controller.order;

import com.tl.rms.integration.util.QiMenSignUtil;
import com.tl.rms.lib.fulfillment.client.service.FulfillmentMessageSender;
import com.tl.rms.lib.wsclient.client.tw.InvokeTwService;
import com.tl.rms.lib.wsclient.client.tw.ordercancel.Jky2TwOrderCancelRequest;
import com.tl.rms.lib.wsclient.utils.XmlToBeanUtil;
import com.tl.rms.lib.wsclient.vo.QiMenResponse;
import com.tl.rms.order.domain.vo.QMDeliveryOrderCreateVo;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Unmarshaller;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("order/router/qimen/service")
@RequiredArgsConstructor
public class QiMenController {

    private final FulfillmentMessageSender fulfillmentMessageSender;
    private final InvokeTwService invokeTwService;
    private final QiMenSignUtil qiMenSignUtil;

    private static final String SERVICE_NAME = "QIMEN_RMS";
    private static final String MAIN_METHOD = "[handleQiMenRequest]";

    /**
     * 处理奇门标准请求格式
     * 示例URL: http://[wms_webservice_url]?method=[wms_API]&format=xml&app_key=[erp_appkey]&v=2.0&sign=[xxxxxxxxxxxxxxxxxxxxxx]&sign_method=md5&customerId=[QIMENUSR1]
     */
    @RequestMapping(method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_XML_VALUE,
            produces = MediaType.APPLICATION_XML_VALUE + ";charset=utf-8")
    public QiMenResponse handleQiMenRequest(
            @RequestParam Map<String, String> params,
            @RequestBody(required = false) String body,
            HttpServletResponse response) {

        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        String traceId = String.valueOf(System.currentTimeMillis());

        // 记录请求入口日志
        log.info("[{}][{}] traceId={} params={}", SERVICE_NAME, MAIN_METHOD, traceId, params);

        if (StringUtils.hasText(body)) {
            log.info("[{}][{}] traceId={} bodyLength={}", SERVICE_NAME, MAIN_METHOD, traceId, body.length());
        }

        // 1. 验证签名
        if (!qiMenSignUtil.validateSignature(params, body, traceId)) {
            log.warn("[{}][{}] traceId={} invalid signature, appKey={} signMethod={}",
                    SERVICE_NAME, MAIN_METHOD, traceId, params.get("app_key"), params.get("sign_method"));
            return QiMenResponse.failure("Invalid signature");
        }

        // 2. 根据method参数处理不同业务逻辑
        String method = params.get("method");
        if (!StringUtils.hasText(method)) {
            log.warn("[{}][{}] traceId={} missing method parameter", SERVICE_NAME, MAIN_METHOD, traceId);
            return QiMenResponse.failure("Missing method parameter");
        }

        // 3. 根据不同方法分发处理
        QiMenResponse responseResult;
        switch (method) {
            case "deliveryorder.create":
                responseResult = handleDeliveryOrderCreate(body, traceId);
                break;
            case "returnorder.create":
                responseResult = handleReturnOrderCreate(body, traceId);
                break;
            case "order.cancel":
                responseResult = handleOrderCancel(body, traceId);
                break;
            default:
                log.warn("[{}][{}] traceId={} Unsupported method:{}", SERVICE_NAME, MAIN_METHOD, traceId, method);
                return QiMenResponse.failure("Unsupported method: " + method);
        }

        log.info("[{}][{}] traceId={} method={} success={}", SERVICE_NAME, MAIN_METHOD, traceId, method, responseResult);
        return responseResult;
    }

    /**
     * 处理发货单创建
     */
    private QiMenResponse handleDeliveryOrderCreate(String body, String traceId) {
        log.info("[{}][deliveryorder.create] traceId={} start", SERVICE_NAME, traceId);
        try {
            QMDeliveryOrderCreateVo qmDeliveryOrderCreateVo = XmlToBeanUtil.xmlToBean(body, QMDeliveryOrderCreateVo.class);
            fulfillmentMessageSender.sendDeliveryOrderCreateMessage(qmDeliveryOrderCreateVo);
            log.info("[{}][deliveryorder.create] traceId={} success", SERVICE_NAME, traceId);
            return QiMenResponse.success("Delivery order created successfully");
        } catch (Exception e) {
            log.error("[{}][deliveryorder.create] traceId={} error", SERVICE_NAME, traceId, e);
            return QiMenResponse.failure("内部错误");
        }
    }

    /**
     * 退货入库单创建接口
     */
    private QiMenResponse handleReturnOrderCreate(String body, String traceId) {
        log.info("[{}][returnorder.create] traceId={} start", SERVICE_NAME, traceId);
        try {
            // 实际业务逻辑处理
            log.info("[{}][returnorder.create] traceId={} success", SERVICE_NAME, traceId);
            return QiMenResponse.success("Return order create successfully");
        } catch (Exception e) {
            log.error("[{}][returnorder.create] traceId={} error", SERVICE_NAME, traceId, e);
            return QiMenResponse.failure("内部错误");
        }
    }

    /**
     * 单据取消接口
     */
    private QiMenResponse handleOrderCancel(String body, String traceId) {
        log.info("[{}][order.cancel] traceId={} start", SERVICE_NAME, traceId);
        try {
            Jky2TwOrderCancelRequest jky2TwOrderCancelRequest = XmlToBeanUtil.xmlToBean(body, Jky2TwOrderCancelRequest.class);
            QiMenResponse qiMenResponse = invokeTwService.rms2TwOrderCancel(jky2TwOrderCancelRequest);
            if (qiMenResponse != null) {
                log.info("[{}][order.cancel] traceId={} response code={}, message={}",
                        SERVICE_NAME, traceId, qiMenResponse.getCode(), qiMenResponse.getMessage());
                return qiMenResponse;
            } else {
                log.warn("[{}][order.cancel] traceId={} response is null", SERVICE_NAME, traceId);
                return QiMenResponse.failure("Order cancel failed: no response");
            }
        } catch (Exception e) {
            log.error("[{}][order.cancel] traceId={} error", SERVICE_NAME, traceId, e);
            return QiMenResponse.failure("内部错误: " + e.getMessage());
        }
    }

}
