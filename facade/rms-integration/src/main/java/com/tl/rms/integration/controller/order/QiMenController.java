package com.tl.rms.integration.controller.order;

import com.tl.rms.integration.service.QiMenToTwConverter;
import com.tl.rms.integration.util.QiMenSignUtil;
import com.tl.rms.lib.fulfillment.client.service.FulfillmentMessageSender;
import com.tl.rms.lib.wsclient.client.tw.InvokeTwService;
import com.tl.rms.lib.wsclient.client.tw.ordercancel.Jky2TwOrderCancelRequest;
import com.tl.rms.lib.wsclient.client.tw.request.inbound.PtInboundOrderInfo;
import com.tl.rms.lib.wsclient.client.tw.response.Response;
import com.tl.rms.lib.wsclient.utils.XmlToBeanUtil;
import com.tl.rms.lib.wsclient.vo.QiMenResponse;
import com.tl.rms.order.domain.vo.QMDeliveryOrderCreateVo;
import com.tl.rms.order.domain.vo.QMReturnOrderCreateVo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("order/router/qimen/service")
@RequiredArgsConstructor
public class QiMenController {

    private final FulfillmentMessageSender fulfillmentMessageSender;
    private final InvokeTwService invokeTwService;
    private final QiMenSignUtil qiMenSignUtil;
    private final QiMenToTwConverter qiMenToTwConverter;

    private static final String SERVICE_NAME = "QIMEN_RMS";
    private static final String MAIN_METHOD = "[handleQiMenRequest]";

    /**
     * 处理奇门标准请求格式
     * 示例URL: http://[wms_webservice_url]?method=[wms_API]&format=xml&app_key=[erp_appkey]&v=2.0&sign=[xxxxxxxxxxxxxxxxxxxxxx]&sign_method=md5&customerId=[QIMENUSR1]
     */
    @RequestMapping(method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_XML_VALUE,
            produces = MediaType.APPLICATION_XML_VALUE + ";charset=utf-8")
    public QiMenResponse handleQiMenRequest(
            @RequestParam Map<String, String> params,
            @RequestBody(required = false) String body,
            HttpServletResponse response) {

        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        String traceId = String.valueOf(System.currentTimeMillis());

        // 记录请求入口日志
        log.info("[{}][{}] traceId={} params={}", SERVICE_NAME, MAIN_METHOD, traceId, params);

        if (StringUtils.hasText(body)) {
            log.info("[{}][{}] traceId={} bodyLength={}", SERVICE_NAME, MAIN_METHOD, traceId, body.length());
        }

        // 1. 验证签名
        if (!qiMenSignUtil.validateSignature(params, body, traceId)) {
            log.warn("[{}][{}] traceId={} invalid signature, appKey={} signMethod={}",
                    SERVICE_NAME, MAIN_METHOD, traceId, params.get("app_key"), params.get("sign_method"));
            return QiMenResponse.failure("Invalid signature");
        }

        // 2. 根据method参数处理不同业务逻辑
        String method = params.get("method");
        if (!StringUtils.hasText(method)) {
            log.warn("[{}][{}] traceId={} missing method parameter", SERVICE_NAME, MAIN_METHOD, traceId);
            return QiMenResponse.failure("Missing method parameter");
        }

        // 3. 根据不同方法分发处理
        QiMenResponse responseResult;
        switch (method) {
            case "deliveryorder.create":
                responseResult = handleDeliveryOrderCreate(body, traceId);
                break;
            case "returnorder.create":
                responseResult = handleReturnOrderCreate(body, traceId);
                break;
            case "order.cancel":
                responseResult = handleOrderCancel(body, traceId);
                break;
            case "singleitem.synchronize":
                return QiMenResponse.success("singleitem synchronize successfully");
            default:
                log.warn("[{}][{}] traceId={} Unsupported method:{}", SERVICE_NAME, MAIN_METHOD, traceId, method);
                return QiMenResponse.failure("Unsupported method: " + method);
        }

        log.info("[{}][{}] traceId={} method={} success={}", SERVICE_NAME, MAIN_METHOD, traceId, method, responseResult);
        return responseResult;
    }

    /**
     * 处理发货单创建
     */
    private QiMenResponse handleDeliveryOrderCreate(String body, String traceId) {
        log.info("[{}][deliveryorder.create] traceId={} body {} start", SERVICE_NAME, traceId, body);
        try {
            QMDeliveryOrderCreateVo qmDeliveryOrderCreateVo = XmlToBeanUtil.xmlToBean(body, QMDeliveryOrderCreateVo.class);
            fulfillmentMessageSender.sendDeliveryOrderCreateMessage(qmDeliveryOrderCreateVo);
            log.info("[{}][deliveryorder.create] traceId={} success", SERVICE_NAME, traceId);
            return QiMenResponse.success("Delivery order created successfully");
        } catch (Exception e) {
            log.error("[{}][deliveryorder.create] traceId={} error", SERVICE_NAME, traceId, e);
            return QiMenResponse.failure("内部错误");
        }
    }

    /**
     * 退货入库单创建接口
     */
    private QiMenResponse handleReturnOrderCreate(String body, String traceId) {
        log.info("[{}][returnorder.create] traceId={} start", SERVICE_NAME, traceId);
        try {
            // 1. 解析奇门退货入库单XML
            QMReturnOrderCreateVo qmReturnOrder = XmlToBeanUtil.xmlToBean(body, QMReturnOrderCreateVo.class);
            log.info("[{}][returnorder.create] traceId={} parsed qimen return order: {}",
                    SERVICE_NAME, traceId, qmReturnOrder.getReturnOrder().getReturnOrderCode());

            // 2. 转换为TW入库参数
            PtInboundOrderInfo twInboundOrder = qiMenToTwConverter.convertReturnOrderToTwInbound(qmReturnOrder);
            log.info("[{}][returnorder.create] traceId={} converted to tw inbound order: {}",
                    SERVICE_NAME, traceId, twInboundOrder.getCustomerOrderID());

            // 3. 直接调用TW侧入库接口
            Response twResponse = invokeTwService.invokeTwInboundSyncService(twInboundOrder);
            log.info("[{}][returnorder.create] traceId={} tw response: code={}, message={}",
                    SERVICE_NAME, traceId, twResponse.getReturnCode(), twResponse.getErrorMsg());

            // 4. 根据TW响应结果返回奇门响应
            if (twResponse.getReturnCode() == 0) {
                log.info("[{}][returnorder.create] traceId={} success", SERVICE_NAME, traceId);
                return QiMenResponse.success("Return order create successfully");
            } else {
                log.warn("[{}][returnorder.create] traceId={} tw failed: {}",
                        SERVICE_NAME, traceId, twResponse.getErrorMsg());
                return QiMenResponse.failure("TW入库失败: " + twResponse.getErrorMsg());
            }
        } catch (Exception e) {
            log.error("[{}][returnorder.create] traceId={} error", SERVICE_NAME, traceId, e);
            return QiMenResponse.failure("内部错误: " + e.getMessage());
        }
    }

    /**
     * 单据取消接口
     */
    private QiMenResponse handleOrderCancel(String body, String traceId) {
        log.info("[{}][order.cancel] traceId={} start", SERVICE_NAME, traceId);
        try {
            Jky2TwOrderCancelRequest jky2TwOrderCancelRequest = XmlToBeanUtil.xmlToBean(body, Jky2TwOrderCancelRequest.class);
            QiMenResponse qiMenResponse = invokeTwService.rms2TwOrderCancel(jky2TwOrderCancelRequest);
            if (qiMenResponse != null) {
                log.info("[{}][order.cancel] traceId={} response code={}, message={}",
                        SERVICE_NAME, traceId, qiMenResponse.getCode(), qiMenResponse.getMessage());
                return qiMenResponse;
            } else {
                log.warn("[{}][order.cancel] traceId={} response is null", SERVICE_NAME, traceId);
                return QiMenResponse.failure("Order cancel failed: no response");
            }
        } catch (Exception e) {
            log.error("[{}][order.cancel] traceId={} error", SERVICE_NAME, traceId, e);
            return QiMenResponse.failure("内部错误: " + e.getMessage());
        }
    }

}
