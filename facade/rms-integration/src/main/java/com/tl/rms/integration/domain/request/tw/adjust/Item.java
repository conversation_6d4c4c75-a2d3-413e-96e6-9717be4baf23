package com.tl.rms.integration.domain.request.tw.adjust;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import lombok.Data;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class Item {
    @XmlElement(name = "ownerCode")
    private String ownerCode;
    @XmlElement(name = "warehouseCode")
    private String warehouseCode;
    @XmlElement(name = "orderCode")
    private String orderCode;
    @XmlElement(name = "orderType")
    private String orderType;
    @XmlElement(name = "outBizCode")
    private String outBizCode;
    @XmlElement(name = "itemCode")
    private String itemCode;
    @XmlElement(name = "inventoryType")
    private String inventoryType;
    @XmlElement(name = "wmsInventoryType")
    private String wmsInventoryType;
    @XmlElement(name = "quantity")
    private String quantity;
    @XmlElement(name = "changeTime")
    private String changeTime;
}