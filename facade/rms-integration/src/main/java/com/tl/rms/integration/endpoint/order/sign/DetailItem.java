
package com.tl.rms.integration.endpoint.order.sign;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;
import lombok.ToString;


/**
 * <p>detailItem complex type�� Java �ࡣ
 *
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 *
 * <pre>
 * &lt;complexType name="detailItem">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="erpDocLineId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="itemGid" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="lotAttr01" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="transPackageCount" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf7" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "detailItem", namespace = "http://service.rms.pttl.com/integration/endpoint", propOrder = {
        "erpDocLineId",
        "itemGid",
        "lotAttr01",
        "transPackageCount",
        "udf7"
})
@ToString
public class DetailItem {

    protected String erpDocLineId;
    protected String itemGid;
    protected String lotAttr01;
    protected String transPackageCount;
    protected String udf7;

    /**
     * ��ȡerpDocLineId���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getErpDocLineId() {
        return erpDocLineId;
    }

    /**
     * ����erpDocLineId���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setErpDocLineId(String value) {
        this.erpDocLineId = value;
    }

    /**
     * ��ȡitemGid���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getItemGid() {
        return itemGid;
    }

    /**
     * ����itemGid���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setItemGid(String value) {
        this.itemGid = value;
    }

    /**
     * ��ȡlotAttr01���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getLotAttr01() {
        return lotAttr01;
    }

    /**
     * ����lotAttr01���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setLotAttr01(String value) {
        this.lotAttr01 = value;
    }

    /**
     * ��ȡtransPackageCount���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getTransPackageCount() {
        return transPackageCount;
    }

    /**
     * ����transPackageCount���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTransPackageCount(String value) {
        this.transPackageCount = value;
    }

    /**
     * ��ȡudf7���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf7() {
        return udf7;
    }

    /**
     * ����udf7���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf7(String value) {
        this.udf7 = value;
    }

}
