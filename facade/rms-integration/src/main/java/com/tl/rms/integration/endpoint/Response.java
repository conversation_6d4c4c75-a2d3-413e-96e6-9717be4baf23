
package com.tl.rms.integration.endpoint;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * <p>anonymous complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="dateTime" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="errorMsg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="returnCode" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "dateTime",
        "errorMsg",
        "returnCode"
})
@XmlRootElement(name = "response", namespace = "http://service.rms.pttl.com/integration/endpoint")
public class Response {

    public static final int SUCCESS_CODE = 0;
    public static final int FAILURE_CODE = 1;

    protected String dateTime;
    protected String errorMsg;
    protected Integer returnCode;

    public static Response success() {
        return new Response(0, null, String.valueOf(System.currentTimeMillis()));
    }

    public static Response failure(String errorMsg) {
        return new Response(errorMsg);
    }

    public Response(String errorMsg) {
        this.returnCode = (errorMsg == null || errorMsg.isEmpty()) ? 0 : 1;
        this.errorMsg = errorMsg;
        this.dateTime = String.valueOf(System.currentTimeMillis());
    }

    public Response(Integer returnCode, String errorMsg, String dateTime) {
        this.returnCode = returnCode;
        this.errorMsg = errorMsg;
        this.dateTime = dateTime;
    }

    /**
     * 获取dateTime属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDateTime() {
        return dateTime;
    }

    /**
     * 设置dateTime属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDateTime(String value) {
        this.dateTime = value;
    }

    /**
     * 获取errorMsg属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getErrorMsg() {
        return errorMsg;
    }

    /**
     * 设置errorMsg属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setErrorMsg(String value) {
        this.errorMsg = value;
    }

    /**
     * 获取returnCode属性的值。
     *
     * @return possible object is
     * {@link Integer }
     */
    public Integer getReturnCode() {
        return returnCode;
    }

    /**
     * 设置returnCode属性的值。
     *
     * @param value allowed object is
     *              {@link Integer }
     */
    public void setReturnCode(Integer value) {
        this.returnCode = value;
    }

}