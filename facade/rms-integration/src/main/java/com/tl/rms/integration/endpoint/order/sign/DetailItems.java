
package com.tl.rms.integration.endpoint.order.sign;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>detailItems complex type�� Java �ࡣ
 *
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 *
 * <pre>
 * &lt;complexType name="detailItems">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="detailItem" type="{http://service.rms.pttl.com/integration/endpoint}detailItem" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "detailItems", namespace = "http://service.rms.pttl.com/integration/endpoint", propOrder = {
        "detailItem"
})
@ToString
public class DetailItems {

    @XmlElement(nillable = true)
    protected List<DetailItem> detailItem;

    /**
     * Gets the value of the detailItem property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the detailItem property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDetailItem().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link DetailItem }
     */
    public List<DetailItem> getDetailItem() {
        if (detailItem == null) {
            detailItem = new ArrayList<DetailItem>();
        }
        return this.detailItem;
    }

}
