package com.tl.rms.integration.controller.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.qimen.api.request.DeliveryorderConfirmRequest;
import com.tl.rms.common.model.ResponseVo;
import com.tl.rms.integration.domain.request.tw.adjust.TwAdjustRequest;
import com.tl.rms.integration.domain.request.tw.deliveryOrder.DeliveryOrderConfirmRequest;
import com.tl.rms.lib.fulfillment.message.TwReturnOrderConfirmMessage;
import com.tl.rms.lib.fulfillment.client.service.FulfillmentMessageSender;
import com.tl.rms.lib.fulfillment.message.TwAdjustMessage;
import com.tl.rms.lib.wsclient.utils.XmlToBeanUtil;
import com.tl.rms.order.api.StockClient;
import com.tl.rms.order.domain.enums.StockChangeLogGoodsTypeEnum;
import com.tl.rms.order.domain.vo.StockChangeLogSaveReqVo;
import com.tl.rms.user.api.WarehouseClient;
import com.tl.rms.user.domain.vo.WarehouseVo;
import com.tl.rms.util.JsonUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/order/tw")
@RequiredArgsConstructor
@Tag(name = "TW系统对接")
public class TwController {

    private final StockClient stockClient;
    private final WarehouseClient warehouseClient;
    private final FulfillmentMessageSender fulfillmentMessageSender;

//    @Operation(summary = "TW库存异动")
//    @PostMapping(value = "/adjust", consumes = MediaType.APPLICATION_XML_VALUE)
//    public ResponseVo<?> twAdjust(@RequestBody TwAdjustRequest request) {
//        log.info("TW库存异动,请求:{}", request);
//        try {
//            List<TwAdjustMessage> items = request.getItems().getItem().stream().map(item -> {
//                TwAdjustMessage twAdjustMessage = new TwAdjustMessage();
//                BeanUtil.copyProperties(item, twAdjustMessage);
//                return twAdjustMessage;
//            }).toList();
//            fulfillmentMessageSender.sendTwAdjust(items);
//            return ResponseVo.suc();
//        } catch (Exception e) {
//            log.error("TW库存异动,异常:", e);
//            return ResponseVo.failure(e.toString());
//        }
//    }

    @Operation(summary = "TW库存异动")
    @PostMapping(value = "/adjust", consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseVo<?> twAdjust(@RequestBody TwAdjustRequest request) {
        log.info("TW库存异动,请求:{}", request);
        try {
            // 检查请求是否为空
            if (request == null || request.getItems() == null || CollectionUtils.isEmpty(request.getItems().getItem())) {
                log.warn("TW库存异动请求参数为空");
                return ResponseVo.failure("请求参数为空");
            }

            // 将请求中的项目转换为TwAdjustMessage列表
            List<TwAdjustMessage> twAdjustMessages = request.getItems().getItem().stream()
                    .map(item -> {
                        TwAdjustMessage twAdjustMessage = new TwAdjustMessage();
                        BeanUtil.copyProperties(item, twAdjustMessage);
                        return twAdjustMessage;
                    })
                    .toList();
            log.info("TW库存异动,转换后:{}", twAdjustMessages);

            // 转换为StockChangeLogSaveReqVo并更新仓库信息
            List<StockChangeLogSaveReqVo> stockChangeLogs = twAdjustMessages
                    .stream()
                    .map(this::convertTwAdjust)
                    .toList();
            setWarehouseNameById(stockChangeLogs);

            // 发送至RMS系统处理库存调整
            log.info("TW库存异动,发送至RMS:{}", stockChangeLogs);
            stockClient.handleAdjust(stockChangeLogs);
            log.info("TW库存异动,发送至RMS成功");

            // 发送至奇门系统
            log.info("TW库存异动,发送奇门MQ:{}", twAdjustMessages);
            fulfillmentMessageSender.sendTwAdjustToQimen(twAdjustMessages);
            log.info("TW库存异动,发送至奇门成功");

            return ResponseVo.suc();
        } catch (Exception e) {
            log.error("TW库存异动,异常:", e);
            return ResponseVo.failure(e.toString());
        }
    }

    @Operation(summary = "发货单创建结果回调")
    @PostMapping(value = "/confirmDeliveryOrder", consumes = MediaType.APPLICATION_XML_VALUE)
    public ResponseVo<?> confirmDeliveryOrder(@RequestBody DeliveryOrderConfirmRequest request) {
        log.info("发货单创建结果回调,请求:{}", request);
        try {
            if (request == null) {
                return ResponseVo.failure("参数为空");
            }
            DeliveryorderConfirmRequest deliveryorderConfirmRequestForQM = new DeliveryorderConfirmRequest();
            DeliveryorderConfirmRequest.DeliveryOrder deliveryOrderForQM =
                new DeliveryorderConfirmRequest.DeliveryOrder();
            deliveryorderConfirmRequestForQM.setDeliveryOrder(deliveryOrderForQM);

            DeliveryOrderConfirmRequest.DeliveryOrder deliveryOrder = request.getDeliveryOrder();
            BeanUtils.copyProperties(deliveryOrder, deliveryOrderForQM);

            List<DeliveryOrderConfirmRequest.OrderLine> orderLines = request.getOrderLines();
            if (CollectionUtils.isNotEmpty(orderLines)) {
                List<DeliveryorderConfirmRequest.OrderLine> orderLinesForQM = new ArrayList<>(orderLines.size());
                deliveryorderConfirmRequestForQM.setOrderLines(orderLinesForQM);
                for (DeliveryOrderConfirmRequest.OrderLine orderLine : orderLines) {
                    DeliveryorderConfirmRequest.OrderLine orderLineForQM = new DeliveryorderConfirmRequest.OrderLine();
                    orderLinesForQM.add(orderLineForQM);
                    BeanUtils.copyProperties(orderLine, orderLineForQM);
                    List<String> snList = orderLine.getSnList();
                    DeliveryorderConfirmRequest.SnList snListForQM = new DeliveryorderConfirmRequest.SnList();
                    snListForQM.setSn(snList);
                    orderLineForQM.setSnList(snListForQM);
                }
            }

            List<DeliveryOrderConfirmRequest.Package> packages = request.getPackages();
            if (CollectionUtils.isNotEmpty(packages)) {
                List<DeliveryorderConfirmRequest.Package> packagesForQM = new ArrayList<>(packages.size());
                deliveryorderConfirmRequestForQM.setPackages(packagesForQM);
                for (DeliveryOrderConfirmRequest.Package aPackage : packages) {
                    DeliveryorderConfirmRequest.Package packageForQM = new DeliveryorderConfirmRequest.Package();
                    BeanUtils.copyProperties(aPackage, packageForQM);
                    packagesForQM.add(packageForQM);
                    List<DeliveryOrderConfirmRequest.Item> items = aPackage.getItems();
                    if (CollectionUtils.isEmpty(items)) {
                        continue;
                    }
                    List<DeliveryorderConfirmRequest.Item> itemsForQM = new ArrayList<>(items.size());
                    packageForQM.setItems(itemsForQM);
                    for (DeliveryOrderConfirmRequest.Item item : items) {
                        DeliveryorderConfirmRequest.Item itemForQM = new DeliveryorderConfirmRequest.Item();
                        BeanUtils.copyProperties(item, itemForQM);
                        itemsForQM.add(itemForQM);
                    }
                }
            }

            fulfillmentMessageSender.confirmDeliveryOrder(JsonUtil.toJson(deliveryorderConfirmRequestForQM));
            return ResponseVo.suc();
        } catch (Exception e) {
            log.error("TW库存异动,异常:", e);
            return ResponseVo.failure(e.toString());
        }
    }

    /**
     * 将TW库存异动消息转换为StockChangeLogSaveReqVo
     *
     * @param item TW库存异动消息
     * @return StockChangeLogSaveReqVo
     */
    private StockChangeLogSaveReqVo convertTwAdjust(TwAdjustMessage item) {
        StockChangeLogSaveReqVo reqVo = new StockChangeLogSaveReqVo();
        //物料编码
        reqVo.setMaterialCode(item.getItemCode());
        //仓库编码
        reqVo.setWarehouseCode(item.getWarehouseCode());
        //出入库单号
        reqVo.setCode(item.getOrderCode());
        //出入库类型
        reqVo.setType(item.getOrderType());
        //出入库时间
        reqVo.setHandleTime(LocalDateTimeUtil.parse(item.getChangeTime(), "yyyy-MM-dd HH:mm:ss"));
        //变更数量
        reqVo.setQty(NumberUtils.toInt(item.getQuantity(), 0));
        //库存类型
        reqVo.setInventoryType(item.getInventoryType());
        //货品属性
        StockChangeLogGoodsTypeEnum goodsTypeEnum = StockChangeLogGoodsTypeEnum.getByValue(item.getWmsInventoryType());
        reqVo.setGoodsType(goodsTypeEnum != null ? goodsTypeEnum.getValue() : item.getWmsInventoryType());
        //制单人
        reqVo.setOperator("TW");
        return reqVo;
    }

    /**
     * TW退货入库确认接口
     */
    @PostMapping(value = "/returnorder/confirm", consumes = MediaType.APPLICATION_XML_VALUE)
    @Operation(summary = "TW退货入库确认接口", description = "接收TW的退货入库确认请求，放入消息队列处理")
    public ResponseVo<?> confirmReturnOrder(@RequestBody String body) {
        log.info("TW退货入库确认 start, body={}" , body);

        try {
            // 1. 解析TW退货入库确认XML
            TwReturnOrderConfirmMessage request = XmlToBeanUtil.xmlToBean(body, TwReturnOrderConfirmMessage.class);
            log.info("TW退货入库确认 parsed request: returnOrderCode={}",  request.getReturnOrder().getReturnOrderCode());

            // 2. 放入Fulfillment消息队列
            fulfillmentMessageSender.confirmReturnOrder(body);
            log.info("TW退货入库确认 sent to queue successfully");

            return ResponseVo.suc();
        } catch (Exception e) {
            log.error("TW退货入库确认 error" , e);
            return ResponseVo.failure("Internal error: " + e.getMessage());
        }
    }

    /**
     * 更新库存异动的仓库名称
     *
     * @param items 库存异动列表
     */
    private void setWarehouseNameById(List<StockChangeLogSaveReqVo> items) {
        List<String> warehouseIds = items
                .stream()
                .map(StockChangeLogSaveReqVo::getWarehouseCode)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
        List<WarehouseVo> warehouseVos = warehouseClient.queryByIds(warehouseIds);
        Map<String, String> warehouseNameMap = warehouseVos.stream().collect(Collectors.toMap(WarehouseVo::getId, WarehouseVo::getName));
        items.forEach(item -> item.setWarehouseName(warehouseNameMap.get(item.getWarehouseCode())));
    }
}
