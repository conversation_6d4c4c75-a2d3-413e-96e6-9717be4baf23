package com.tl.rms.integration.endpoint.order;

import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@XmlRootElement(name = "request")
@XmlAccessorType(jakarta.xml.bind.annotation.XmlAccessType.FIELD)
public class QMDeliveryOrderCreateResponse {
    public static final String SUCCESS_FLAG = "success";
    public static final String FAIL_FLAG = "failure";

    public static final String ERROR_CODE = "500";

    private String flag;
    private String code;
    private String msg;

    public static QMDeliveryOrderCreateResponse success() {
        QMDeliveryOrderCreateResponse qmDeliveryOrderCreateResponse = new QMDeliveryOrderCreateResponse();
        qmDeliveryOrderCreateResponse.setFlag(SUCCESS_FLAG);
        return qmDeliveryOrderCreateResponse;
    }

    public static QMDeliveryOrderCreateResponse fail(String code, String msg) {
        QMDeliveryOrderCreateResponse qmDeliveryOrderCreateResponse = new QMDeliveryOrderCreateResponse();
        qmDeliveryOrderCreateResponse.setFlag(FAIL_FLAG);
        qmDeliveryOrderCreateResponse.setCode(code);
        qmDeliveryOrderCreateResponse.setMsg(msg);
        return qmDeliveryOrderCreateResponse;
    }
}
