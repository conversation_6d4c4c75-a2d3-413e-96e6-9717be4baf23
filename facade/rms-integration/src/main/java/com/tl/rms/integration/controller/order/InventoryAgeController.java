package com.tl.rms.integration.controller.order;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tl.rms.common.model.ResponseVo;
import com.tl.rms.lib.wsclient.client.bigdata.InvokeBigDataService;
import com.tl.rms.lib.wsclient.client.bigdata.response.InventoryAgeAsyncResponse;
import com.tl.rms.order.api.InventoryAgeClient;
import com.tl.rms.order.domain.vo.InventoryAgeQueryReqVo;
import com.tl.rms.order.domain.vo.InventoryAgeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

@Slf4j
@RestController
@RequestMapping("/order/inventoryAge")
@RequiredArgsConstructor
@Tag(name = "库龄管理")
public class InventoryAgeController {

    private static final int BATCH_SIZE = 1000;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final InventoryAgeClient inventoryAgeClient;
    private final InvokeBigDataService invokeBigDataService;
    private final ObjectMapper objectMapper;

    @PostConstruct
    public void init() {
        // 配置ObjectMapper忽略未知属性，避免因JSON中存在VO中没有的字段而报错
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    @PostMapping("/bigdata/async/response")
    @Operation(summary = "库龄大数据回调")
    public ResponseVo<Boolean> inventoryAgeAsyncResponse(@RequestBody InventoryAgeAsyncResponse inventoryAgeAppResponse) {
        try {
            // 参数校验
            ResponseVo<Boolean> validateResult = validateRequest(inventoryAgeAppResponse);
            if (validateResult != null) {
                return validateResult;
            }

            // 解析账龄日期
            String expiryDateStr = inventoryAgeAppResponse.getSearchParams().getExpiryDate();
            LocalDate expiryDate = parseExpiryDate(expiryDateStr);
            if (expiryDate == null) {
                return ResponseVo.failure("日期格式解析失败: " + expiryDateStr);
            }

            // 清理历史数据
            cleanupHistoricalData(expiryDate);

            // 获取并处理文件数据
            String downloadUrl = inventoryAgeAppResponse.getData().getZipFile();
            InputStream inputStream = invokeBigDataService.inventoryAgeGetFile(downloadUrl);
            if (inputStream == null) {
                log.error("获取文件流失败,下载链接: {}", downloadUrl);
                return ResponseVo.failure("获取文件流失败");
            }

            processZipContent(inputStream);

            log.info("库龄数据全部处理完成");
            return ResponseVo.suc(true);
        } catch (Exception e) {
            log.error("处理库龄大数据回调失败,响应数据: {}", inventoryAgeAppResponse, e);
            return ResponseVo.failure("处理库龄大数据回调失败: " + e.getMessage());
        }
    }

    /**
     * 验证请求参数
     *
     * @param response 回调响应
     * @return 验证结果，如果验证通过返回null
     */
    private ResponseVo<Boolean> validateRequest(InventoryAgeAsyncResponse response) {
        if (response == null) {
            log.warn("库龄大数据回调失败,响应为空");
            return ResponseVo.failure("响应为空");
        }

        if (!response.isOk()) {
            log.warn("库龄大数据回调失败,响应:{}", response);
            return ResponseVo.failure(response.getMsg());
        }

        if (response.getData() == null || StringUtils.isBlank(response.getData().getZipFile())) {
            log.error("库龄大数据回调失败,获取文件下载链接为空, 响应数据: {}", response);
            return ResponseVo.failure("获取文件下载链接失败");
        }

        if (response.getSearchParams() == null || StringUtils.isBlank(response.getSearchParams().getExpiryDate())) {
            log.error("库龄大数据回调失败,缺少必需的账龄日期参数, 响应数据: {}", response);
            return ResponseVo.failure("缺少必需的账龄日期参数");
        }

        return null;
    }

    /**
     * 解析账龄日期
     *
     * @param expiryDateStr 日期字符串
     * @return 解析后的LocalDate对象，解析失败返回null
     */
    private LocalDate parseExpiryDate(String expiryDateStr) {
        try {
            return LocalDate.parse(expiryDateStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            log.error("日期格式解析失败,日期字符串: {}", expiryDateStr, e);
            return null;
        }
    }

    /**
     * 清理历史数据
     *
     * @param expiryDate 账龄日期
     */
    private void cleanupHistoricalData(LocalDate expiryDate) {
        try {
            InventoryAgeQueryReqVo queryVo = new InventoryAgeQueryReqVo();
            queryVo.setExpiryDate(expiryDate);
            Long count = inventoryAgeClient.count(queryVo);
            if (count > 0) {
                inventoryAgeClient.deleteByExpiryDate(expiryDate);
            }
        } catch (Exception e) {
            log.warn("清理历史数据时发生异常,日期: {}", expiryDate, e);
        }
    }

    /**
     * 逐行读取ZIP文件内容并按批次处理
     *
     * @param inputStream ZIP文件输入流
     */
    private void processZipContent(InputStream inputStream) throws IOException {
        try (ZipInputStream zipInputStream = new ZipInputStream(inputStream)) {
            ZipEntry entry;
            int totalCount = 0;
            List<InventoryAgeVo> batchList = new ArrayList<>();

            while ((entry = zipInputStream.getNextEntry()) != null) {
                // 只处理txt文件
                if (!entry.isDirectory() && entry.getName().endsWith(".txt")) {
                    log.info("正在处理文件: {}", entry.getName());

                    // 逐行读取文件内容
                    BufferedReader reader = new BufferedReader(new InputStreamReader(zipInputStream, StandardCharsets.UTF_8));
                    String line;
                    while ((line = reader.readLine()) != null) {
                        // 解析每一行的JSON对象并直接转换为InventoryAgeVo
                        InventoryAgeVo data = parseJsonLineToVo(line);
                        if (data != null) {
                            batchList.add(data);
                            totalCount++;

                            // 达到批次大小时处理数据
                            if (batchList.size() >= BATCH_SIZE) {
                                processBatch(batchList, totalCount);
                                batchList.clear();
                            }
                        }
                    }
                }
                zipInputStream.closeEntry();
            }

            // 处理剩余的数据
            if (!batchList.isEmpty()) {
                processBatch(batchList, totalCount);
            }
        }
    }

    /**
     * 解析单行JSON字符串为InventoryAgeVo对象
     *
     * @param line 单行JSON字符串
     * @return InventoryAgeVo对象
     */
    private InventoryAgeVo parseJsonLineToVo(String line) {
        try {
            line = line.trim();
            if (line.startsWith("{") && (line.endsWith("}") || line.endsWith("},"))) {
                // 移除结尾的逗号（如果有的话）
                if (line.endsWith("},")) {
                    line = line.substring(0, line.length() - 1);
                }

                log.debug("正在处理行: {}", line);

                // 直接将JSON转换为InventoryAgeVo对象
                InventoryAgeVo vo = objectMapper.readValue(line, InventoryAgeVo.class);
                return vo;
            }
        } catch (Exception e) {
            log.warn("解析单行JSON失败: {}", line, e);
        }
        return null;
    }

    /**
     * 处理一批数据
     *
     * @param batchList  批次数据
     * @param totalCount 当前总处理数量
     */
    private void processBatch(List<InventoryAgeVo> batchList, int totalCount) {
        try {
            log.info("处理批次数据,数量: {}, 累计总数: {}", batchList.size(), totalCount);
            if (!batchList.isEmpty()) {
                inventoryAgeClient.saveBatch(batchList);
            }

            log.debug("成功处理{}条数据", batchList.size());
        } catch (Exception e) {
            log.error("处理批次数据失败,数量: {}", batchList.size(), e);
            throw new RuntimeException("处理批次数据失败: " + e.getMessage(), e);
        }
    }
}
