package com.tl.rms.integration.util;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.TreeMap;

/**
 * 奇门接口签名验证工具类
 */
@Slf4j
@Getter
@Component
public class QiMenSignUtil {

    @Value("${qimen.app.key:35061094}")
    private String appKey;

    @Value("${qimen.app.secret:b087286f07581d112ea0176bb6746f8e}")
    private String appSecret;

    private static final String SERVICE_NAME = "QIMEN_RMS";
    private static final String SIGN_METHOD = "[validateSignature]";

    /**
     * 验证请求签名
     *
     * @param params  请求参数
     * @param body    请求体
     * @param traceId 追踪ID
     * @return 签名是否有效
     */
    public boolean validateSignature(Map<String, String> params, String body, String traceId) {
        String sign = params.get("sign");
        String signMethod = params.get("sign_method");
        String requestAppKey = params.get("app_key");

        log.info("[{}][{}] traceId={} sign={} signMethod={} appKey={}",
                SERVICE_NAME, SIGN_METHOD, traceId, sign, signMethod, requestAppKey);

        if (!StringUtils.hasText(sign) || !StringUtils.hasText(signMethod) || !StringUtils.hasText(requestAppKey)) {
            log.warn("[{}][{}] traceId={} missing required params: sign={} signMethod={} appKey={}",
                    SERVICE_NAME, SIGN_METHOD, traceId, sign, signMethod, requestAppKey);
            return false;
        }

        // 获取用于签名的参数（除了sign本身）
        Map<String, String> signParams = new TreeMap<>();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (!"sign".equals(entry.getKey())) {
                signParams.put(entry.getKey(), entry.getValue());
            }
        }

        // 构造签名字符串: 按参数名排序后拼接 keyvalue 形式
        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String, String> entry : signParams.entrySet()) {
            signStr.append(entry.getKey()).append(entry.getValue());
        }

        log.debug("[{}][{}] traceId={} constructed signString length={}",
                SERVICE_NAME, SIGN_METHOD, traceId, signStr.toString().length());

        // 根据签名方法进行签名验证
        try {
            String calculatedSign = "";
            switch (signMethod.toLowerCase()) {
                case "md5":
                    // 奇门MD5签名算法：secret + 排序后的参数字符串 + body内容 + secret
                    StringBuilder signContent = new StringBuilder();
                    signContent.append(appSecret).append(signStr.toString());
                    if (StringUtils.hasText(body)) {
                        signContent.append(body);
                    }
                    signContent.append(appSecret);
                    calculatedSign = md5(signContent.toString());
                    log.debug("[{}][{}] traceId={} md5 signContent length={}",
                            SERVICE_NAME, SIGN_METHOD, traceId, signContent.toString().length());
                    break;
                default:
                    log.warn("[{}][{}] traceId={} unsupported sign method: {}",
                            SERVICE_NAME, SIGN_METHOD, traceId, signMethod);
                    return false;
            }

            boolean isValid = sign.equalsIgnoreCase(calculatedSign);
            if (!isValid) {
                log.warn("[{}][{}] traceId={} sign mismatch: provided={} calculated={}",
                        SERVICE_NAME, SIGN_METHOD, traceId, sign, calculatedSign);
            } else {
                log.info("[{}][{}] traceId={} sign validation success", SERVICE_NAME, SIGN_METHOD, traceId);
            }
            return isValid;
        } catch (Exception e) {
            log.error("[{}][{}] traceId={} sign validation error", SERVICE_NAME, SIGN_METHOD, traceId, e);
            return false;
        }
    }

    /**
     * MD5加密
     */
    private String md5(String input) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
        StringBuilder sb = new StringBuilder();
        for (byte b : hashBytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
}
