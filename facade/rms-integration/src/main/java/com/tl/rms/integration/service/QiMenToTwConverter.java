package com.tl.rms.integration.service;

import com.tl.rms.common.exception.CommonException;
import com.tl.rms.lib.wsclient.client.tw.request.inbound.ArrayOfPtInboundOrderDetailInfo;
import com.tl.rms.lib.wsclient.client.tw.request.inbound.PtInboundOrderDetailInfo;
import com.tl.rms.lib.wsclient.client.tw.request.inbound.PtInboundOrderInfo;
import com.tl.rms.order.api.MaterialClient;
import com.tl.rms.order.domain.vo.*;
import com.tl.rms.user.api.ProjectClient;
import com.tl.rms.user.api.WarehouseClient;
import com.tl.rms.user.domain.vo.ProjectVo;
import com.tl.rms.user.domain.vo.WarehouseVo;
import com.tl.rms.util.StringUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 奇门到TW参数转换服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class QiMenToTwConverter {

    @Resource
    private WarehouseClient warehouseClient;

    @Resource
    private MaterialClient materialClient;

    @Resource
    private ProjectClient projectClient;

    /**
     * 将奇门退货入库单转换为TW入库单
     */
    public PtInboundOrderInfo convertReturnOrderToTwInbound(QMReturnOrderCreateVo qmReturnOrder) {
        if (qmReturnOrder == null || qmReturnOrder.getReturnOrder() == null) {
            throw new IllegalArgumentException("奇门退货入库单不能为空");
        }

        QMReturnOrderVo returnOrder = qmReturnOrder.getReturnOrder();
        PtInboundOrderInfo twInbound = new PtInboundOrderInfo();

        WarehouseVo warehouse = warehouseClient.getWarehouseById(returnOrder.getWarehouseCode());
        if (warehouse == null) {
            throw new CommonException("仓库编号不存在");
        }

        // 设置基本信息
        // erp仓库名称
        twInbound.setWhID(warehouse.getName());
        // 吉客云退货入库单号
        twInbound.setCustomerOrderID(returnOrder.getReturnOrderCode());
        // 吉客云退货入库单号
        twInbound.setExternalOrderID(returnOrder.getReturnOrderCode());
        twInbound.setTmsTask("");
        // 销售退货订单类型编号
        twInbound.setOrderType("Return");
        twInbound.setOrderDate("");
        twInbound.setRequireDeliveryDate("");
        twInbound.setRequirementsArrivalDate("");

        // 货主名称 PTTL_INV_01_太力总部 从这里截取出来的太力总部
        String ownerId = warehouse.getWhOrgName().substring(warehouse.getWhOrgName().lastIndexOf("_") + 1);
        twInbound.setOwnerID(ownerId);
        // 制单ou
        twInbound.setCustomerID(warehouse.getOrgName());

        if (StringUtil.isEmpty(qmReturnOrder.getExtendProps().getCompanyName())) {
            throw new CommonException("客户名称不存在");
        }
        // 客户名称
        twInbound.setVendorName(qmReturnOrder.getExtendProps().getCompanyName() + "-" + returnOrder.getShopNick() + "零售客户");

        // 设置退货人信息
        if (returnOrder.getSenderInfo() != null) {
            twInbound.setDeliveryToName("");
            // 退货人名称
            twInbound.setDeliveryToContact(returnOrder.getSenderInfo().getName());
            // 退货入联系电话
            twInbound.setDeliveryToPhone(returnOrder.getSenderInfo().getMobile());
            // 退货入省份
            twInbound.setDeliveryToProvince(returnOrder.getSenderInfo().getProvince());
            // 退货人城市
            twInbound.setDeliveryToCity(returnOrder.getSenderInfo().getCity());
            // 退货人详细地址
            twInbound.setDeliveryToAddress(returnOrder.getSenderInfo().getDetailAddress());
        }

        // 设置自定义字段
        // 退货单对应的原销售订单号
        twInbound.setUdf01(returnOrder.getPreDeliveryOrderCode());

        // 获取项目信息
        ProjectVo project = projectClient.getProjectByShopName(returnOrder.getShopNick());
        if (project == null) {
            throw new CommonException("项目不存在");
        }
        // 项目名称
        twInbound.setUdf05(project.getName());

        // 来源，固定传RMS
        twInbound.setUdf08("RMS");

        twInbound.setTrackingNumber("");
        twInbound.setCarrierName("");
        twInbound.setRealDepartureTime("");
        twInbound.setEstimatedArrivalTime("");
        twInbound.setRemark("");
        twInbound.setEdiSyncFlag("");

        // 设置明细信息
        if (qmReturnOrder.getOrderLines() != null && qmReturnOrder.getOrderLines().getOrderLines() != null) {
            ArrayOfPtInboundOrderDetailInfo detailInfos = new ArrayOfPtInboundOrderDetailInfo();
            List<PtInboundOrderDetailInfo> detailList = new ArrayList<>();

            for (QMReturnOrderLineVo orderLine : qmReturnOrder.getOrderLines().getOrderLines()) {
                PtInboundOrderDetailInfo detail = new PtInboundOrderDetailInfo();

                // 查询物料信息
                MaterialQueryReqVo materialQueryReqVo = new MaterialQueryReqVo();
                materialQueryReqVo.setMaterialCode(orderLine.getItemCode());
                MaterialVo material = materialClient.getMaterialByMaterialCodeOrGbCode(materialQueryReqVo);
                if (material == null) {
                    throw new CommonException(String.format("%s 物料不存在", orderLine.getItemCode()));
                }
                // 退货物料编码
                detail.setSkuID(orderLine.getItemCode());
                // 退货物料描述
                detail.setSkuDescr(material.getMaterialDesc());
                // 退货数量
                detail.setQty(BigDecimal.valueOf(orderLine.getPlanQty()));
                // 退货入库单行号
                detail.setExternalLineID(orderLine.getOrderLineNo());
                // erp仓库名称
                detail.setLotAttr01(warehouse.getName());
                // 销售单网店订单号
                detail.setUdf01(orderLine.getSourceOrderCode());

                detailList.add(detail);
            }

            detailInfos.setPtInboundOrderDetailInfo(detailList);
            twInbound.setPtInboundOrderDetailInfos(detailInfos);
        }

        // 设置来源信息
        // 来源1，固定传QI_MEN
//        twInbound.setSource1("QI_MEN");
        // 来源2，固定传RMS
//        twInbound.setSource2("RMS");
        // 来源3，固定传DIAN_KE_TAI_LI
        twInbound.setSource3("DIAN_KE_TAI_LI");

        return twInbound;
    }

}
