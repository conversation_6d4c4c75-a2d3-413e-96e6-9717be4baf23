package com.tl.rms.integration.service;

import com.tl.rms.lib.wsclient.client.tw.request.inbound.ArrayOfPtInboundOrderDetailInfo;
import com.tl.rms.lib.wsclient.client.tw.request.inbound.PtInboundOrderDetailInfo;
import com.tl.rms.lib.wsclient.client.tw.request.inbound.PtInboundOrderInfo;
import com.tl.rms.order.domain.vo.QMReturnOrderCreateVo;
import com.tl.rms.order.domain.vo.QMReturnOrderLineVo;
import com.tl.rms.order.domain.vo.QMReturnOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 奇门到TW参数转换服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class QiMenToTwConverter {

    /**
     * 将奇门退货入库单转换为TW入库单
     */
    public PtInboundOrderInfo convertReturnOrderToTwInbound(QMReturnOrderCreateVo qmReturnOrder) {
        if (qmReturnOrder == null || qmReturnOrder.getReturnOrder() == null) {
            throw new IllegalArgumentException("奇门退货入库单不能为空");
        }

        QMReturnOrderVo returnOrder = qmReturnOrder.getReturnOrder();
        PtInboundOrderInfo twInbound = new PtInboundOrderInfo();

        // 设置基本信息
        // erp仓库名称
        twInbound.setWhID(returnOrder.getWarehouseCode());
        // 吉客云退货入库单号
        twInbound.setCustomerOrderID(returnOrder.getReturnOrderCode());
        // 吉客云退货入库单号
        twInbound.setExternalOrderID(returnOrder.getReturnOrderCode());
        twInbound.setTmsTask("");
        // 销售退货订单类型编号
        twInbound.setOrderType("Return");
        twInbound.setOrderDate("");
        twInbound.setRequireDeliveryDate("");
        twInbound.setRequirementsArrivalDate("");
        // TODO 货主名称
        twInbound.setOwnerID(returnOrder.getOwnerCode());
        // TODO 制单ou
        twInbound.setCustomerID(returnOrder.getOwnerCode());
        // TODO 客户名称
        twInbound.setVendorName(returnOrder.getOwnerCode());

        // 设置退货人信息
        if (returnOrder.getSenderInfo() != null) {
            twInbound.setDeliveryToName("");
            // 退货人名称
            twInbound.setDeliveryToContact(returnOrder.getSenderInfo().getName());
            // 退货入联系电话
            twInbound.setDeliveryToPhone(returnOrder.getSenderInfo().getMobile());
            // 退货入省份
            twInbound.setDeliveryToProvince(returnOrder.getSenderInfo().getProvince());
            // 退货人城市
            twInbound.setDeliveryToCity(returnOrder.getSenderInfo().getCity());
            // 退货人详细地址
            twInbound.setDeliveryToAddress(returnOrder.getSenderInfo().getDetailAddress());
        }

        // 设置自定义字段
        // 退货单对应的原销售订单号
        twInbound.setUdf01(returnOrder.getPreDeliveryOrderCode());
        // 项目名称
        twInbound.setUdf05(returnOrder.getShopNick());
        // 来源，固定传RMS
        twInbound.setUdf08("RMS");

        twInbound.setTrackingNumber("");
        twInbound.setCarrierName("");
        twInbound.setRealDepartureTime("");
        twInbound.setEstimatedArrivalTime("");
        twInbound.setRemark("");
        twInbound.setEdiSyncFlag("");

        // 设置明细信息
        if (qmReturnOrder.getOrderLines() != null && qmReturnOrder.getOrderLines().getOrderLines() != null) {
            ArrayOfPtInboundOrderDetailInfo detailInfos = new ArrayOfPtInboundOrderDetailInfo();
            List<PtInboundOrderDetailInfo> detailList = new ArrayList<>();

            for (QMReturnOrderLineVo orderLine : qmReturnOrder.getOrderLines().getOrderLines()) {
                PtInboundOrderDetailInfo detail = new PtInboundOrderDetailInfo();
                // 退货物料编码
                detail.setSkuID(orderLine.getItemCode());
                // 退货物料描述
                detail.setSkuDescr(orderLine.getItemCode());
                // 退货数量
                detail.setQty(BigDecimal.valueOf(orderLine.getPlanQty()));
                // 退货入库单行号
                detail.setExternalLineID(orderLine.getOrderLineNo());
                // TODO erp仓库名称
                detail.setLotAttr01(returnOrder.getWarehouseCode());
                // 销售单网店订单号
                detail.setUdf01(orderLine.getSourceOrderCode());

                detailList.add(detail);
            }

            detailInfos.setPtInboundOrderDetailInfo(detailList);
            twInbound.setPtInboundOrderDetailInfos(detailInfos);
        }

        // 设置来源信息
        // 来源1，固定传QI_MEN
        twInbound.setSource1("QI_MEN");
        // 来源2，固定传RMS
        twInbound.setSource2("RMS");
        // 来源3，固定传DIAN_KE_TAI_LI
        twInbound.setSource3("DIAN_KE_TAI_LI");

        return twInbound;
    }

}
