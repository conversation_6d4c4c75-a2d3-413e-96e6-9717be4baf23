package com.tl.rms.integration.endpoint.order.linecancel;

import com.tl.rms.integration.endpoint.Response;
import com.tl.rms.lib.fulfillment.client.service.FulfillmentMessageSender;
import com.tl.rms.lib.fulfillment.message.CancelLineResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * TW调用销售单行取消操作
 *
 * <AUTHOR>
 * @date 2025/9/3
 */
@Slf4j
@Service
public class LineCancelService {

    @Autowired
    private FulfillmentMessageSender fulfillmentMessageSender;


    public Response handleLineCancel(LineCancelRequest request) {

        int returnCode = 0;

        String orderId = request.getCustomerOrderId();
        if (StringUtils.isEmpty(orderId)) {
            return new Response("orderId不可为空");
        }

        try {
            CancelLineResponse res = new CancelLineResponse();
            res.setOrderId(request.getCustomerOrderId());
            res.setShippingGroupId(request.getExternalOrderId());
            res.setCancelDate(request.getCancelDate());

            List<DetailItem> detailItems = request.getDetailItems().getDetailItem();
            for (DetailItem detailItem : detailItems) {
                if (!"ok".equals(detailItem.getSuccess())) {
                    continue;
                }
                res.addLine(detailItem.getExternalLineId(), detailItem.getSuccess(), detailItem.getMsg(), detailItem.getSkuId());
            }

            if (CollectionUtils.isNotEmpty(res.getLines())) {
                fulfillmentMessageSender.sendJkyCancelLine(res);
            }

            return new Response(returnCode, "处理成功！", null);

        } catch (Exception e) {
            String msg = "销售单行取消异常 [" + orderId + "] ";
            log.error(msg, e);
            return new Response(msg + e.getMessage());
        }

    }

}
