package com.tl.rms.integration.controller.order;

import com.qimen.api.request.ReturnorderConfirmRequest;
import com.qimen.api.response.ReturnorderConfirmResponse;
import com.tl.rms.lib.wsclient.client.qimen.InvokeQiMenService;
import com.tl.rms.lib.wsclient.vo.QiMenResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/qimen")
@RequiredArgsConstructor
@Tag(name = "奇门接口调用", description = "提供对奇门系统的接口调用")
public class QiMenTestController {
//
    private final InvokeQiMenService invokeQiMenService;
//
//    @PostMapping("/deliveryorder/confirm")
//    @Operation(summary = "发货单确认接口", description = "调用奇门发货单确认接口")
//    public QiMenResponse confirmDeliveryOrder(@RequestBody DeliveryorderConfirmRequest request) {
//        try {
//            DeliveryorderConfirmResponse response = invokeQiMenService.confirmDeliveryOrder(request);
//            if (response.isSuccess()) {
//                return QiMenResponse.success("发货单确认成功");
//            } else {
//                return QiMenResponse.failure("发货单确认失败: " + response.getMessage());
//            }
//        } catch (Exception e) {
//            log.error("调用奇门发货单确认接口异常", e);
//            return QiMenResponse.failure("系统异常: " + e.getMessage());
//        }
//    }
//
    @PostMapping("/returnorder/confirm")
    @Operation(summary = "退货入库单确认接口", description = "调用奇门退货入库单确认接口")
    public QiMenResponse confirmReturnOrder(@RequestBody ReturnorderConfirmRequest request) {
        try {
            ReturnorderConfirmResponse response = invokeQiMenService.confirmReturnOrder(request);
            if (response.isSuccess()) {
                return QiMenResponse.success("退货入库单确认成功");
            } else {
                return QiMenResponse.failure("退货入库单确认失败: " + response.getMessage());
            }
        } catch (Exception e) {
            log.error("调用奇门退货入库单确认接口异常", e);
            return QiMenResponse.failure("系统异常: " + e.getMessage());
        }
    }
//
//    @PostMapping("/stockchange/report")
//    @Operation(summary = "库存异动通知接口", description = "调用奇门库存异动通知接口")
//    public QiMenResponse reportStockChange(@RequestBody StockchangeReportRequest request) {
//        try {
//            StockchangeReportResponse response = invokeQiMenService.reportStockChange(request);
//            if (response.isSuccess()) {
//                return QiMenResponse.success("库存异动通知成功");
//            } else {
//                return QiMenResponse.failure("库存异动通知失败: " + response.getMessage());
//            }
//        } catch (Exception e) {
//            log.error("调用奇门库存异动通知接口异常", e);
//            return QiMenResponse.failure("系统异常: " + e.getMessage());
//        }
//    }
}
