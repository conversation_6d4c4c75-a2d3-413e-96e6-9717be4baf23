package com.tl.rms.integration.endpoint.order;

import com.tl.rms.integration.config.WebServiceConfiguration;
import com.tl.rms.integration.endpoint.Response;
import com.tl.rms.integration.endpoint.order.linecancel.LineCancelRequest;
import com.tl.rms.integration.endpoint.order.linecancel.LineCancelService;
import com.tl.rms.integration.endpoint.order.outbound.OrderOutboundRequest;
import com.tl.rms.integration.endpoint.order.outbound.OutboundService;
import com.tl.rms.integration.endpoint.order.sign.OrderSignRequest;
import com.tl.rms.integration.endpoint.order.sign.SignService;
import com.tl.rms.integration.endpoint.order.twInbound.TwInboundRequest;
import com.tl.rms.integration.endpoint.order.twInbound.TwInboundService;
import com.tl.rms.util.json.JsonProcessUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ws.server.endpoint.annotation.Endpoint;
import org.springframework.ws.server.endpoint.annotation.PayloadRoot;
import org.springframework.ws.server.endpoint.annotation.RequestPayload;
import org.springframework.ws.server.endpoint.annotation.ResponsePayload;

import java.util.Set;

@Slf4j
@Endpoint
@RequiredArgsConstructor
public class OrderEndpoint {

    private final OutboundService outboundService;

    private final SignService signService;

    private final TwInboundService twInboundService;

    private final LineCancelService lineCancelService;


    @PayloadRoot(namespace = WebServiceConfiguration.NAMESPACE, localPart = "orderOutboundRequest")
    @ResponsePayload
    public Response outbound(@RequestPayload OrderOutboundRequest request) {
        log.info("订单出库：{}", JsonProcessUtil.beanToJson(request));

        String msg;
        try {
            msg = checkOrderSource(request.getUdf08());
            if (StringUtils.isEmpty(msg)) {
                msg = outboundService.handleOrderOutbound(request);
            }
        } catch (Exception e) {
            msg = e.toString();
            log.error("订单出库异常", e);
        }
        return new Response(msg);
    }

    /**
     * 订单签收接口
     *
     * @param request OrderSignRequest
     * @return Response
     * @date 2019/8/15
     */
    @PayloadRoot(namespace = WebServiceConfiguration.NAMESPACE, localPart = "orderSignRequest")
    @ResponsePayload
    public Response sign(@RequestPayload OrderSignRequest request) {
        log.info("订单签收：{}", JsonProcessUtil.beanToJson(request));

        String msg;
        try {
            msg = checkOrderSource(request.getUdf08());
            if (StringUtils.isEmpty(msg)) {
                msg = signService.handleOrderSign(request);
            }
        } catch (Exception e) {
            msg = e.toString();
            log.error("订单签收异常", e);
        }
        return new Response(msg);
    }

    @PayloadRoot(namespace = WebServiceConfiguration.NAMESPACE, localPart = "twInboundRequest")
    @ResponsePayload
    public Response twInbound(@RequestPayload TwInboundRequest request) {
        log.info(request.toString());

        String msg = null;
        try {
            msg = twInboundService.twInbound(request);
        } catch (Exception e) {
            msg = e.toString();
            log.error("twInbound exception", e);
        }
        return new Response(msg);
    }

    /**
     * 订单行取消接口
     *
     * @param request LineCancelRequest
     * @return Response
     * @date 2019/8/15
     */
    @PayloadRoot(namespace = WebServiceConfiguration.NAMESPACE, localPart = "lineCancelRequest")
    @ResponsePayload
    public Response lineCancel(@RequestPayload LineCancelRequest request) {
        log.info("订单取消: {}", JsonProcessUtil.beanToJson(request));

        String msg;
        try {
            msg = checkOrderSource(request.getCommandSource());
            if (StringUtils.isEmpty(msg)) {
                return lineCancelService.handleLineCancel(request);
            }
        } catch (Exception e) {
            msg = e.toString();
            log.error("订单取消异常", e);
        }
        return new Response(msg);
    }

    private String checkOrderSource(String orderSource) {

        if (!Set.of("JKY", "RMS").contains(orderSource)) {
            return "销售单来源非法";
        } else {
            return null;
        }
    }

}