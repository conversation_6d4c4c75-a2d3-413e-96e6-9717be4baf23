
package com.tl.rms.integration.endpoint.order.outbound;

import com.tl.rms.integration.endpoint.Response;
import jakarta.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each
 * Java content interface and Java element interface
 * generated in the com.pttl.tlmall.integration.endpoint.order.outbound package.
 * <p>An ObjectFactory allows you to programatically
 * construct new instances of the Java representation
 * for XML content. The Java representation of XML
 * content can consist of schema derived interfaces
 * and classes representing the binding of schema
 * type definitions, element declarations and model
 * groups.  Factory methods for each of these are
 * provided in this class.
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.pttl.tlmall.integration.endpoint.order.outbound
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link OrderOutboundRequest }
     */
    public OrderOutboundRequest createOrderOutboundRequest() {
        return new OrderOutboundRequest();
    }

    /**
     * Create an instance of {@link DetailItems }
     */
    public DetailItems createDetailItems() {
        return new DetailItems();
    }

    /**
     * Create an instance of {@link Response }
     */
    public Response createResponse() {
        return new Response();
    }

    /**
     * Create an instance of {@link DetailItem }
     */
    public DetailItem createDetailItem() {
        return new DetailItem();
    }

}
