package com.tl.rms.integration.config;

import com.tl.rms.common.exception.AccessDeniedException;
import com.tl.rms.common.exception.AuthenticationException;
import com.tl.rms.common.exception.CommonException;
import com.tl.rms.common.exception.SystemException;
import com.tl.rms.common.exception.spring.CommonExceptionHandler;
import com.tl.rms.common.model.ResponseVo;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ResponseStatus;

@ControllerAdvice
public class GlobalExceptionHandler extends CommonExceptionHandler {
    @ResponseStatus(HttpStatus.OK)
    @Override
    public ResponseVo<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        return super.handleMethodArgumentNotValidException(ex);
    }

    @ResponseStatus(HttpStatus.OK)
    @Override
    public ResponseVo<?> handleBindException(BindException ex) {
        return super.handleBindException(ex);
    }

    @ResponseStatus(HttpStatus.OK)
    @Override
    public ResponseVo<?> handleCommonException(CommonException ex) {
        return super.handleCommonException(ex);
    }

    @ResponseStatus(HttpStatus.OK)
    @Override
    public ResponseVo<?> handleAuthenticationException(AuthenticationException ex) {
        return super.handleAuthenticationException(ex);
    }

    @ResponseStatus(HttpStatus.OK)
    @Override
    public ResponseVo<?> handleAccessDeniedException(AccessDeniedException ex) {
        return super.handleAccessDeniedException(ex);
    }

    @ResponseStatus(HttpStatus.OK)
    @Override
    public ResponseVo<?> exception(SystemException e) {
        return super.exception(e);
    }

    @ResponseStatus(HttpStatus.OK)
    @Override
    public ResponseVo<?> handleUnknownException(Exception e) {
        return super.handleUnknownException(e);
    }
}
