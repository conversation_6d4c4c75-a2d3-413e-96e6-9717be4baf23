package com.tl.rms.integration.controller.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.StringUtils;
import com.tl.rms.common.model.ResponseVo;
import com.tl.rms.lib.wsclient.client.jky.InvokeJkyService;
import com.tl.rms.lib.wsclient.client.jky.request.JkyDepartmentQueryRequest;
import com.tl.rms.lib.wsclient.client.jky.request.JkyPriceCreateRequest;
import com.tl.rms.lib.wsclient.client.jky.response.JackyunResponse;
import com.tl.rms.lib.wsclient.client.jky.vo.DepartmentVo;
import com.tl.rms.lib.wsclient.client.jky.vo.PriceAdjustmentGoodsVo;
import com.tl.rms.order.api.MaterialClient;
import com.tl.rms.order.api.PriceClient;
import com.tl.rms.order.domain.vo.BmcPushPriceVo;
import com.tl.rms.order.domain.vo.PriceJkyMaterialPriceVo;
import com.tl.rms.user.domain.constant.Constants;
import com.tl.rms.util.json.JsonProcessUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/order")
@RequiredArgsConstructor
@Tag(name = "价格管理")
public class PriceController {

    private final PriceClient priceClient;
    private final MaterialClient materialClient;
    private final InvokeJkyService invokeJkyService;

    @Value("${jky.sync-price.enable:false}")
    private boolean jkySyncPriceEnable;

    @PostMapping("/price/push")
    @Operation(summary = "BMC推送价格信息")
    public ResponseVo<Boolean> pushPrice(@RequestBody List<BmcPushPriceVo> bmcPushPriceVos) {
        log.info("BMC推送价格信息:{}", bmcPushPriceVos);
        Boolean result = priceClient.pushPrice(bmcPushPriceVos);
        log.info("BMC推送价格信息结果:{}", result);

        if (result && jkySyncPriceEnable) {
            log.info("同步吉客云开始");
            try {
                triggerSyncJky(bmcPushPriceVos.stream()
                        .map(BmcPushPriceVo::getMaterialCode)
                        .collect(Collectors.toList()));
            } catch (Exception e) {
                log.error("同步吉客云失败", e);
            } finally {
                log.info("同步吉客云结束");
            }
        }

        return ResponseVo.suc(result);
    }

    // @Async
    private void triggerSyncJky(List<String> materialCodes) {
        List<PriceJkyMaterialPriceVo> pricesList = priceClient.listPushByMaterialCode(materialCodes);
        if (CollUtil.isEmpty(pricesList)) {
            log.error("物料编码：{}, 没有价格信息", CollUtil.join(materialCodes, ","));
            return;
        }

        // 过滤掉无效的数据
        pricesList = pricesList.stream()
                .filter(priceJkyMaterialPriceVo -> {
                    if (Objects.isNull(priceJkyMaterialPriceVo.getMinPrice())
                            || StringUtils.isBlank(priceJkyMaterialPriceVo.getApplyUserName())) {
                        log.error("物料编码：{}, 有效价格或申请人信息为空", priceJkyMaterialPriceVo.getSkuBarcode());
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(pricesList)) {
            log.error("物料编码：{}, 没有符合条件的有效价格信息", CollUtil.join(materialCodes, ","));
            return;
        }

        // 检查物料是否同步至吉客云
        List<String> notSyncJkySkuBarcodeList = materialClient.checkMaterialSyncJky(
                pricesList.stream().map(PriceJkyMaterialPriceVo::getSkuBarcode).collect(Collectors.toList()));

        // 记录未同步的物料日志
        if (CollUtil.isNotEmpty(notSyncJkySkuBarcodeList)) {
            log.error("物料编码：{}未同步至吉客云", notSyncJkySkuBarcodeList);
        }

        // 移除未同步的物料
        pricesList = pricesList.stream()
                .filter(priceJkyMaterialPriceVo -> !notSyncJkySkuBarcodeList
                        .contains(priceJkyMaterialPriceVo.getSkuBarcode()))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(pricesList)) {
            log.error("所有物料均未同步至吉客云");
            return;
        }

        // 补全数据
        completeCompanyCode(pricesList);

        // pricesList 根据申请人分组
        Map<String, List<PriceJkyMaterialPriceVo>> map = pricesList.stream()
                .collect(Collectors.groupingBy(PriceJkyMaterialPriceVo::getApplyUserName));

        // 推送至吉客云
        for (Map.Entry<String, List<PriceJkyMaterialPriceVo>> entry : map.entrySet()) {
            JkyPriceCreateRequest jkyPriceCreateRequest = getJkyPriceCreateRequest(entry);
            JackyunResponse jackyunResponse = invokeJkyService.createPrice(jkyPriceCreateRequest);
            if (jackyunResponse.getCode() != 200) {
                log.error("推送至吉客云失败，data: {}, msg: {}", JsonProcessUtil.beanToJson(jkyPriceCreateRequest),
                        jackyunResponse.getMsg());
            } else {
                log.info("推送至吉客云成功，data: {}, msg: {}", JsonProcessUtil.beanToJson(jkyPriceCreateRequest),
                        jackyunResponse.getMsg());
                priceClient.updatePushStatus(
                        entry.getValue().stream().map(PriceJkyMaterialPriceVo::getSkuBarcode).toList(),
                        Constants.TASK_USER_ID);
            }
        }
    }

    private void completeCompanyCode(List<PriceJkyMaterialPriceVo> pricesList) {
        Map<String, String> companyCodeMap = getCompanyCodeMap(pricesList);
        for (PriceJkyMaterialPriceVo priceJkyMaterialPriceVo : pricesList) {
            priceJkyMaterialPriceVo.setDepartCode(priceJkyMaterialPriceVo.getProjectId());
            String companyCode = companyCodeMap.get(priceJkyMaterialPriceVo.getProjectId());
            if (StrUtil.isNotBlank(companyCode)) {
                priceJkyMaterialPriceVo.setCompanyCode(companyCode);
            }
        }
    }

    private Map<String, String> getCompanyCodeMap(List<PriceJkyMaterialPriceVo> pricesList) {
        // 根据项目id去重
        Set<String> projectIdSet = pricesList.stream().map(PriceJkyMaterialPriceVo::getProjectId).collect(Collectors.toSet());

        Map<String, String> companyCodeMap = new HashMap<>();

        projectIdSet.forEach(projectId -> {
            JkyDepartmentQueryRequest request = new JkyDepartmentQueryRequest();
            request.setDepartCode(projectId);
            List<DepartmentVo> listDepartment = invokeJkyService.listDepartment(request);
            if (!listDepartment.isEmpty()) {
                companyCodeMap.put(projectId, listDepartment.get(0).getCompanyCode());
            }
        });

        return companyCodeMap;
    }

    private JkyPriceCreateRequest getJkyPriceCreateRequest(Map.Entry<String, List<PriceJkyMaterialPriceVo>> entry) {
        List<PriceJkyMaterialPriceVo> valueList = entry.getValue();
        PriceJkyMaterialPriceVo priceJkyMaterialPriceVo = valueList.get(0);

        JkyPriceCreateRequest jkyPriceCreateRequest = new JkyPriceCreateRequest();
        // jkyPriceCreateRequest.setApplyUserName(priceJkyMaterialPriceVo.getApplyUserName());
        jkyPriceCreateRequest.setApplyUserName("刘秭宁");
        jkyPriceCreateRequest.setDepartCode(priceJkyMaterialPriceVo.getDepartCode());
        jkyPriceCreateRequest.setCompanyCode(priceJkyMaterialPriceVo.getCompanyCode());

        List<PriceAdjustmentGoodsVo> salesGoodsPriceAdjustmentList = new ArrayList<>();
        for (PriceJkyMaterialPriceVo priceJkyMaterialPriceVo1 : valueList) {
            PriceAdjustmentGoodsVo priceAdjustmentGoodsVo = new PriceAdjustmentGoodsVo();
            priceAdjustmentGoodsVo.setMinPrice(priceJkyMaterialPriceVo1.getMinPrice());
            priceAdjustmentGoodsVo.setSkuBarcode(priceJkyMaterialPriceVo1.getSkuBarcode());
            salesGoodsPriceAdjustmentList.add(priceAdjustmentGoodsVo);
        }
        jkyPriceCreateRequest.setSalesGoodsPriceAdjustmentList(salesGoodsPriceAdjustmentList);
        return jkyPriceCreateRequest;
    }

}
