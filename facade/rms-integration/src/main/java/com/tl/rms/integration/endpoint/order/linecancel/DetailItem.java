
package com.tl.rms.integration.endpoint.order.linecancel;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;
import lombok.ToString;


/**
 * <p>detailItem complex type�� Java �ࡣ
 *
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 *
 * <pre>
 * &lt;complexType name="detailItem">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="externalLineId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="msg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="success" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "detailItem", namespace = "http://service.rms.pttl.com/integration/endpoint", propOrder = {
        "externalLineId",
        "msg",
        "success",
        "skuId"
})
@ToString
public class DetailItem {

    protected String externalLineId;
    protected String msg;
    protected String success;
    protected String skuId;

    /**
     * ��ȡexternalLineId���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getExternalLineId() {
        return externalLineId;
    }

    /**
     * ����externalLineId���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setExternalLineId(String value) {
        this.externalLineId = value;
    }

    /**
     * ��ȡmsg���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getMsg() {
        return msg;
    }

    /**
     * ����msg���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setMsg(String value) {
        this.msg = value;
    }

    /**
     * ��ȡsuccess���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getSuccess() {
        return success;
    }

    /**
     * ����success���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSuccess(String value) {
        this.success = value;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }
}
