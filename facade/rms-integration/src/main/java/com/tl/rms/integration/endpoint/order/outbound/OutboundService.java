package com.tl.rms.integration.endpoint.order.outbound;

import com.tl.rms.lib.fulfillment.client.service.FulfillmentMessageSender;
import com.tl.rms.lib.fulfillment.message.TWCommerceItem;
import com.tl.rms.lib.fulfillment.message.TWMessage;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * TW->RMS 订单出库
 *
 * <AUTHOR>
 * @date 2025/8/15
 */
@Slf4j
@Service
public class OutboundService {

    @Autowired
    private FulfillmentMessageSender fulfillmentMessageSender;


    @SneakyThrows
    public String handleOrderOutbound(OrderOutboundRequest outboundResponseInfo) {

        List<DetailItem> outboundItems = outboundResponseInfo.getDetailItems().getDetailItem();
        List<TWCommerceItem> itemList = new ArrayList<>(outboundItems.size() * 2);
        for (DetailItem outboundItem : outboundItems) {
            TWCommerceItem item = new TWCommerceItem();
            item.setItemId(outboundItem.getExternalLineId());
            item.setOrderQty((int) outboundItem.getOrigianlQuantity());
            item.setQty((int) outboundItem.getShippedQuantity());
            item.setOutInv(outboundItem.getLotAttr01());
            item.setInLocator(outboundItem.getLotAttr09());
            item.setMaterialCode(outboundItem.getSkuId());
            itemList.add(item);
        }

        TWMessage twMessage = new TWMessage();
        twMessage.setOrderId(outboundResponseInfo.getCustomerOrderId());
        twMessage.setDeliveryId(outboundResponseInfo.getOrderId());
        twMessage.setAddress(outboundResponseInfo.getReceiptName());
        twMessage.setTwCommerceItemList(itemList);
        twMessage.setTransactionDate(outboundResponseInfo.getTransactionDate());

        fulfillmentMessageSender.sendJkyOrderOutbound(twMessage);

        return null;
    }

}
