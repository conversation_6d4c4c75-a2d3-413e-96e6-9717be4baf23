package com.tl.rms.integration.endpoint.order.sign;


import com.tl.rms.lib.fulfillment.client.service.FulfillmentMessageSender;
import com.tl.rms.lib.fulfillment.message.TWCommerceItem;
import com.tl.rms.lib.fulfillment.message.TWMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * TW调用销售单签收操作
 *
 * <AUTHOR>
 * @date 2025/9/3
 */
@Slf4j
@Service
public class SignService {

    @Autowired
    private FulfillmentMessageSender fulfillmentMessageSender;


    public String handleOrderSign(OrderSignRequest orderSignRequest) {

        String orderId = orderSignRequest.getCustomerOrderId();
        if (StringUtils.isEmpty(orderId)) {
            return "orderId不可为空";
        }

        try {
            List<DetailItem> signInfoItems = orderSignRequest.getDetailItems().getDetailItem();
            List<TWCommerceItem> itemList = new ArrayList<>(signInfoItems.size() * 2);
            for (DetailItem signInfoItem : signInfoItems) {
                TWCommerceItem item = new TWCommerceItem();
                item.setItemId(signInfoItem.getErpDocLineId());
                item.setQty(Integer.valueOf(signInfoItem.getTransPackageCount()));
                item.setMaterialCode(signInfoItem.getItemGid());
                itemList.add(item);
            }

            TWMessage twMessage = new TWMessage();
            twMessage.setOrderId(orderSignRequest.getCustomerOrderId());
            twMessage.setTwCommerceItemList(itemList);
            twMessage.setTransactionDate(orderSignRequest.getTransactionDate());
            twMessage.setActualTime(orderSignRequest.getSignTime());

            fulfillmentMessageSender.sendJkyOrderSign(twMessage);

        } catch (Exception e) {
            String msg = "销售单签收异常 " + orderId + " ";
            log.error(msg, e);
            return msg + e.getMessage();
        }

        return null;

    }

}
