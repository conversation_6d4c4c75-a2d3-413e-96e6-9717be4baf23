package com.tl.rms.integration.endpoint.user;

import com.tl.rms.common.exception.CommonException;
import com.tl.rms.integration.config.WebServiceConfiguration;
import com.tl.rms.integration.endpoint.Response;
import com.tl.rms.integration.endpoint.user.warehousesync.WarehouseSyncRequest;
import com.tl.rms.user.api.WarehouseClient;
import com.tl.rms.user.domain.vo.WarehouseBranchVo;
import com.tl.rms.user.domain.vo.WarehouseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.ws.server.endpoint.annotation.Endpoint;
import org.springframework.ws.server.endpoint.annotation.PayloadRoot;
import org.springframework.ws.server.endpoint.annotation.RequestPayload;
import org.springframework.ws.server.endpoint.annotation.ResponsePayload;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Endpoint
public class UserEndpoint {

    private final WarehouseClient warehouseClient;

    public UserEndpoint(WarehouseClient warehouseClient) {
        this.warehouseClient = warehouseClient;
    }

    /**
     * 仓库同步
     *
     * @param warehouseSyncRequest 仓库信息
     * @return 仓库同步结果
     */
    @PayloadRoot(namespace = WebServiceConfiguration.NAMESPACE, localPart = WebServiceConfiguration.WAREHOUSE_SYNC + "Request")
    @ResponsePayload
    public Response warehouseSync(@RequestPayload WarehouseSyncRequest warehouseSyncRequest) {
        log.info("Starting warehouse sync process for warehouse ID: {}", warehouseSyncRequest.getWarehouseId());

        try {
            WarehouseVo warehouseVo = buildWarehouseVo(warehouseSyncRequest);
            Boolean syncResult = warehouseClient.sync(warehouseVo);

            if (Boolean.TRUE.equals(syncResult)) {
                log.info("Warehouse sync completed successfully for warehouse ID: {}", warehouseSyncRequest.getWarehouseId());
                return Response.success();
            } else {
                log.warn("Warehouse sync completed but returned false for warehouse ID: {}", warehouseSyncRequest.getWarehouseId());
                return Response.failure("Warehouse sync failed");
            }

        } catch (CommonException commonException) {
            log.error("Warehouse sync failed due to CommonException for warehouse ID: {}", warehouseSyncRequest.getWarehouseId(), commonException);
            return Response.failure(commonException.getMessage());
        } catch (Exception e) {
            String errorMessage = "Failed to sync warehouse: " + e.getMessage();
            log.error("Warehouse sync failed due to Exception for warehouse ID: {}", warehouseSyncRequest.getWarehouseId(), e);
            return Response.failure(errorMessage);
        }
    }

    private WarehouseVo buildWarehouseVo(WarehouseSyncRequest warehouseSyncRequest) {
        WarehouseVo warehouseVo = new WarehouseVo();
        warehouseVo.setId(warehouseSyncRequest.getWarehouseId());
        warehouseVo.setName(warehouseSyncRequest.getWarehouseName());
        warehouseVo.setOrgCode(warehouseSyncRequest.getOrgCode());
        if (warehouseSyncRequest.getOrgName() != null) {
            String convertedOrgName = warehouseSyncRequest.getOrgName().replace("INV", "OU");
            warehouseVo.setOrgName(convertedOrgName);
        }
        warehouseVo.setOrgName(warehouseSyncRequest.getOrgName());
        warehouseVo.setWhOrgName(warehouseSyncRequest.getOrgName());
        warehouseVo.setContactName(warehouseSyncRequest.getContactName());
        warehouseVo.setContactWay(warehouseSyncRequest.getContactWay());
        warehouseVo.setAddress(warehouseSyncRequest.getAddress());
        warehouseVo.setIsActive("Y".equals(warehouseSyncRequest.getIsActive()) ? 1 : 0);

        // 处理分公司关系
        warehouseVo.setWarehouseBranchVoList(buildWarehouseBranchList(warehouseSyncRequest));

        return warehouseVo;
    }

    private List<WarehouseBranchVo> buildWarehouseBranchList(WarehouseSyncRequest request) {
        if (request.getDetailItems() == null || CollectionUtils.isEmpty(request.getDetailItems().getDetailItem())) {
            return Collections.emptyList();
        }

        // 当SalesExamine=1时才添加分公司和仓库关系
        return request.getDetailItems().getDetailItem().stream()
                .filter(item -> "1".equals(item.getSalesExamine()))
                .map(item -> {
                    WarehouseBranchVo branchVo = new WarehouseBranchVo();
                    branchVo.setWarehouseId(request.getWarehouseId());
                    branchVo.setBranchId(item.getBranchId());
                    branchVo.setBranchName(item.getBranchName());
                    return branchVo;
                })
                .collect(Collectors.toList());
    }

}