
package com.tl.rms.integration.endpoint.order.outbound;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;
import lombok.ToString;


/**
 * <p>anonymous complex type�� Java �ࡣ
 *
 * <p>����ģʽƬ��ָ�������ڴ����е�Ԥ�����ݡ�
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="customerId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="customerOrderId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="detailItems" type="{http://service.rms.pttl.com/integration/endpoint}detailItems" minOccurs="0"/>
 *         &lt;element name="externalOrderId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="lotAttr01" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="orderId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ownerId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="receiptName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="transactionDate" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="type" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf01" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf02" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf03" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf04" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf05" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf07" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="udf08" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "customerId",
        "customerOrderId",
        "detailItems",
        "externalOrderId",
        "lotAttr01",
        "orderId",
        "ownerId",
        "receiptName",
        "transactionDate",
        "type",
        "udf01",
        "udf02",
        "udf03",
        "udf04",
        "udf05",
        "udf07",
        "udf08"
})
@XmlRootElement(name = "orderOutboundRequest", namespace = "http://service.rms.pttl.com/integration/endpoint")
@ToString
public class OrderOutboundRequest {

    protected String customerId;
    protected String customerOrderId;
    protected DetailItems detailItems;
    protected String externalOrderId;
    protected String lotAttr01;
    protected String orderId;
    protected String ownerId;
    protected String receiptName;
    protected String transactionDate;
    protected String type;
    protected String udf01;
    protected String udf02;
    protected String udf03;
    protected String udf04;
    protected String udf05;
    protected String udf07;
    protected String udf08;

    /**
     * ��ȡcustomerId���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getCustomerId() {
        return customerId;
    }

    /**
     * ����customerId���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCustomerId(String value) {
        this.customerId = value;
    }

    /**
     * ��ȡcustomerOrderId���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getCustomerOrderId() {
        return customerOrderId;
    }

    /**
     * ����customerOrderId���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCustomerOrderId(String value) {
        this.customerOrderId = value;
    }

    /**
     * ��ȡdetailItems���Ե�ֵ��
     *
     * @return possible object is
     * {@link DetailItems }
     */
    public DetailItems getDetailItems() {
        return detailItems;
    }

    /**
     * ����detailItems���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link DetailItems }
     */
    public void setDetailItems(DetailItems value) {
        this.detailItems = value;
    }

    /**
     * ��ȡexternalOrderId���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getExternalOrderId() {
        return externalOrderId;
    }

    /**
     * ����externalOrderId���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setExternalOrderId(String value) {
        this.externalOrderId = value;
    }

    /**
     * ��ȡlotAttr01���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getLotAttr01() {
        return lotAttr01;
    }

    /**
     * ����lotAttr01���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setLotAttr01(String value) {
        this.lotAttr01 = value;
    }

    /**
     * ��ȡorderId���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * ����orderId���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOrderId(String value) {
        this.orderId = value;
    }

    /**
     * ��ȡownerId���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getOwnerId() {
        return ownerId;
    }

    /**
     * ����ownerId���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOwnerId(String value) {
        this.ownerId = value;
    }

    /**
     * ��ȡreceiptName���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getReceiptName() {
        return receiptName;
    }

    /**
     * ����receiptName���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setReceiptName(String value) {
        this.receiptName = value;
    }

    /**
     * ��ȡtransactionDate���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getTransactionDate() {
        return transactionDate;
    }

    /**
     * ����transactionDate���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTransactionDate(String value) {
        this.transactionDate = value;
    }

    /**
     * ��ȡtype���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getType() {
        return type;
    }

    /**
     * ����type���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * ��ȡudf01���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf01() {
        return udf01;
    }

    /**
     * ����udf01���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf01(String value) {
        this.udf01 = value;
    }

    /**
     * ��ȡudf02���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf02() {
        return udf02;
    }

    /**
     * ����udf02���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf02(String value) {
        this.udf02 = value;
    }

    /**
     * ��ȡudf03���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf03() {
        return udf03;
    }

    /**
     * ����udf03���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf03(String value) {
        this.udf03 = value;
    }

    /**
     * ��ȡudf04���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf04() {
        return udf04;
    }

    /**
     * ����udf04���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf04(String value) {
        this.udf04 = value;
    }

    /**
     * ��ȡudf05���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf05() {
        return udf05;
    }

    /**
     * ����udf05���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf05(String value) {
        this.udf05 = value;
    }

    /**
     * ��ȡudf07���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf07() {
        return udf07;
    }

    /**
     * ����udf07���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf07(String value) {
        this.udf07 = value;
    }

    /**
     * ��ȡudf08���Ե�ֵ��
     *
     * @return possible object is
     * {@link String }
     */
    public String getUdf08() {
        return udf08;
    }

    /**
     * ����udf08���Ե�ֵ��
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUdf08(String value) {
        this.udf08 = value;
    }

}
