package com.tl.rms.integration.config;

import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.ws.config.annotation.EnableWs;
import org.springframework.ws.config.annotation.WsConfigurerAdapter;
import org.springframework.ws.transport.http.MessageDispatcherServlet;
import org.springframework.ws.wsdl.wsdl11.DefaultWsdl11Definition;
import org.springframework.xml.xsd.SimpleXsdSchema;
import org.springframework.xml.xsd.XsdSchema;

@EnableWs
@Configuration
public class WebServiceConfiguration extends WsConfigurerAdapter {

    public static final String NAMESPACE = "http://service.rms.pttl.com/integration/endpoint";

    private static final String WS_PATH = "/ws/";
    private static final String XSD_SUFFIX = ".xsd";

    /**
     * user webservice
     */
    private static final String USER_XSD_PATH = "xsd/user/";
    public static final String WAREHOUSE_SYNC = "warehouseSync";

    /**
     * order webservice
     */
    private static final String ORDER_XSD_PATH = "xsd/order/";
    private static final String OUTBOUND = "outbound";
    private static final String SIGN = "sign";
    private static final String LINE_CANCEL = "lineCancel";
    public static final String TW_INBOUND = "twInbound";

    @Bean
    public ServletRegistrationBean<MessageDispatcherServlet> messageDispatcherServlet(ApplicationContext applicationContext) {
        MessageDispatcherServlet servlet = new MessageDispatcherServlet();
        servlet.setApplicationContext(applicationContext);
        servlet.setTransformWsdlLocations(true);
        return new ServletRegistrationBean<>(servlet, WS_PATH + "*");
    }

    private ClassPathResource getXsdResource(String xsdPath, String xsdName) {
        return new ClassPathResource(xsdPath + xsdName + XSD_SUFFIX);
    }

    /**
     * 仓库同步接口
     */
    @Bean(name = WAREHOUSE_SYNC)
    public DefaultWsdl11Definition warehouseSync() {
        DefaultWsdl11Definition wsdl11Definition = new DefaultWsdl11Definition();
        wsdl11Definition.setPortTypeName(WAREHOUSE_SYNC);
        wsdl11Definition.setLocationUri(WS_PATH + WAREHOUSE_SYNC);
        wsdl11Definition.setTargetNamespace(NAMESPACE);
        wsdl11Definition.setSchema(warehouseSyncSchema());
        return wsdl11Definition;
    }

    @Bean
    public XsdSchema warehouseSyncSchema() {
        return new SimpleXsdSchema(new ClassPathResource(USER_XSD_PATH + WAREHOUSE_SYNC + XSD_SUFFIX));
    }

    /**
     * 订单出库接口
     */
    @Bean(name = OUTBOUND)
    public DefaultWsdl11Definition outbound() {
        DefaultWsdl11Definition wsdl11Definition = new DefaultWsdl11Definition();
        wsdl11Definition.setPortTypeName(OUTBOUND);
        wsdl11Definition.setLocationUri(WS_PATH + OUTBOUND);
        wsdl11Definition.setTargetNamespace(NAMESPACE);
        wsdl11Definition.setSchema(outboundSchema());
        return wsdl11Definition;
    }

    @Bean
    public XsdSchema outboundSchema() {
        return new SimpleXsdSchema(getXsdResource(ORDER_XSD_PATH, OUTBOUND));
    }

    /**
     * 订单签收接口
     */
    @Bean(name = SIGN)
    public DefaultWsdl11Definition sign() {
        DefaultWsdl11Definition wsdl11Definition = new DefaultWsdl11Definition();
        wsdl11Definition.setPortTypeName(SIGN);
        wsdl11Definition.setLocationUri(WS_PATH + SIGN);
        wsdl11Definition.setTargetNamespace(NAMESPACE);
        wsdl11Definition.setSchema(signSchema());
        return wsdl11Definition;
    }

    @Bean
    public XsdSchema signSchema() {
        return new SimpleXsdSchema(getXsdResource(ORDER_XSD_PATH, SIGN));
    }

    /**
     * 订单行取消接口
     */
    @Bean(name = LINE_CANCEL)
    public DefaultWsdl11Definition lineCancel() {
        DefaultWsdl11Definition wsdl11Definition = new DefaultWsdl11Definition();
        wsdl11Definition.setPortTypeName(LINE_CANCEL);
        wsdl11Definition.setLocationUri(WS_PATH + LINE_CANCEL);
        wsdl11Definition.setTargetNamespace(NAMESPACE);
        wsdl11Definition.setSchema(lineCancelSchema());
        return wsdl11Definition;
    }

    @Bean
    public XsdSchema lineCancelSchema() {
        return new SimpleXsdSchema(getXsdResource(ORDER_XSD_PATH, LINE_CANCEL));
    }

    /**
     * TW->RMS 入库
     */
    @Bean(name = TW_INBOUND)
    public DefaultWsdl11Definition stockInbound() {
        DefaultWsdl11Definition wsdl11Definition = new DefaultWsdl11Definition();
        wsdl11Definition.setPortTypeName(TW_INBOUND);
        wsdl11Definition.setLocationUri(WS_PATH + TW_INBOUND);
        wsdl11Definition.setTargetNamespace(NAMESPACE);
        wsdl11Definition.setSchema(stockInboundSchema());
        return wsdl11Definition;
    }

    @Bean
    public XsdSchema stockInboundSchema() {
        return new SimpleXsdSchema(new ClassPathResource(ORDER_XSD_PATH + TW_INBOUND + XSD_SUFFIX));
    }
}