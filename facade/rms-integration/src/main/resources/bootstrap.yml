server:
  port: 8212
spring:
  application:
    name: rms-integration
  config:
    import: >  #spring.config.import 是从后往前加载配置的(在当前环境下,越往后优先级越高)
      apollo://tech.application-public.yml,
      apollo://tech.redis-tlmall.yml,
      apollo://tech.rabbitmq.yml,
      apollo://tech.mq-redis.yml,
      apollo://tech.WebServiceClientConfig,
      apollo://tech.ExceptionConfig,
      apollo://tech.MailConfig,
      apollo://tech.FulfillmentClientConfig,
      apollo://tech.session-redis.yml,
      apollo://application.yml

jasypt:
  encryptor:
    password: 123456789
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    algorithm: PBEWithMD5AndDES

esbWsAddress: http://apim-uat.pttl.com/gateway