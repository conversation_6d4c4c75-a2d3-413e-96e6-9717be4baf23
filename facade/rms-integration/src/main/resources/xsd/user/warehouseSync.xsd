<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" targetNamespace="http://service.rms.pttl.com/integration/endpoint"
           xmlns:tns="http://service.rms.pttl.com/integration/endpoint" xmlns:xs="http://www.w3.org/2001/XMLSchema">

    <xs:element name="warehouseSyncRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="warehouseId" type="xs:string" minOccurs="0"/>
                <xs:element name="warehouseName" type="xs:string" minOccurs="0"/>
                <xs:element name="orgCode" type="xs:string" minOccurs="0"/>
                <xs:element name="orgName" type="xs:string" minOccurs="0"/>
                <xs:element name="contactName" type="xs:string" minOccurs="0"/>
                <xs:element name="contactWay" type="xs:string" minOccurs="0"/>
                <xs:element name="address" type="xs:string" minOccurs="0"/>
                <xs:element name="isActive" type="xs:string" minOccurs="0"/>
                <xs:element name="detailItems" type="tns:detailItems" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="detailItems">
        <xs:sequence>
            <xs:element name="detailItem" type="tns:detailItem" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="detailItem">
        <xs:sequence>
            <xs:element name="salesExamine" type="xs:string" minOccurs="0"/>
            <xs:element name="branchId" type="xs:string" minOccurs="0"/>
            <xs:element name="branchName" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="response">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="dateTime" type="xs:string" minOccurs="0"/>
                <xs:element name="errorMsg" type="xs:string" minOccurs="0"/>
                <xs:element name="returnCode" type="xs:int" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

</xs:schema>