<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" targetNamespace="http://service.rms.pttl.com/integration/endpoint"
           xmlns:tns="http://service.rms.pttl.com/integration/endpoint" xmlns:xs="http://www.w3.org/2001/XMLSchema">

    <xs:element name="orderSignRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="customerId" type="xs:string" minOccurs="0"/>
                <xs:element name="customerOrderId" type="xs:string" minOccurs="0"/>
                <xs:element name="destLocationGid" type="xs:string" minOccurs="0"/>
                <xs:element name="detailItems" type="tns:detailItems" minOccurs="0"/>
                <xs:element name="erpDeliveryNo" type="xs:string" minOccurs="0"/>
                <xs:element name="externalOrderId" type="xs:string" minOccurs="0"/>
                <xs:element name="orderId" type="xs:string" minOccurs="0"/>
                <xs:element name="orderReleaseClass" type="xs:string" minOccurs="0"/>
                <xs:element name="orderType" type="xs:string" minOccurs="0"/>
                <xs:element name="ownerId" type="xs:string" minOccurs="0"/>
                <xs:element name="transactionDate" type="xs:string" minOccurs="0"/>
                <xs:element name="udf08" type="xs:string" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="detailItems">
        <xs:sequence>
            <xs:element name="detailItem" type="tns:detailItem" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="detailItem">
        <xs:sequence>
            <xs:element name="erpDocLineId" type="xs:string" minOccurs="0"/>
            <xs:element name="itemGid" type="xs:string" minOccurs="0"/>
            <xs:element name="lotAttr01" type="xs:string" minOccurs="0"/>
            <xs:element name="transPackageCount" type="xs:string" minOccurs="0"/>
            <xs:element name="udf7" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="orderSignResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="dateTime" type="xs:string" minOccurs="0"/>
                <xs:element name="errorMsg" type="xs:string" minOccurs="0"/>
                <xs:element name="returnCode" type="xs:int" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>

