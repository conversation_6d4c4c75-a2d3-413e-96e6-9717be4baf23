<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" targetNamespace="http://service.rms.pttl.com/integration/endpoint"
           xmlns:tns="http://service.rms.pttl.com/integration/endpoint" xmlns:xs="http://www.w3.org/2001/XMLSchema">

    <xs:element name="lineCancelRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="cancelDate" type="xs:string" minOccurs="0"/>
                <xs:element name="commandSource" type="xs:string" minOccurs="0"/>
                <xs:element name="customerOrderId" type="xs:string" minOccurs="0"/>
                <xs:element name="detailItems" type="tns:detailItems" minOccurs="0"/>
                <xs:element name="externalOrderId" type="xs:string" minOccurs="0"/>
                <xs:element name="success" type="xs:string" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="detailItems">
        <xs:sequence>
            <xs:element name="detailItem" type="tns:detailItem" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="detailItem">
        <xs:sequence>
            <xs:element name="externalLineId" type="xs:string" minOccurs="0"/>
            <xs:element name="msg" type="xs:string" minOccurs="0"/>
            <xs:element name="success" type="xs:string" minOccurs="0"/>
            <xs:element name="skuId" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="response">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="dateTime" type="xs:string" minOccurs="0"/>
                <xs:element name="errorMsg" type="xs:string" minOccurs="0"/>
                <xs:element name="returnCode" type="xs:int" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>

