<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" targetNamespace="http://service.rms.pttl.com/integration/endpoint"
           xmlns:tns="http://service.rms.pttl.com/integration/endpoint" xmlns:xs="http://www.w3.org/2001/XMLSchema">

    <xs:element name="orderOutboundRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="customerId" type="xs:string" minOccurs="0"/>
                <xs:element name="customerOrderId" type="xs:string" minOccurs="0"/>
                <xs:element name="detailItems" type="tns:detailItems" minOccurs="0"/>
                <xs:element name="externalOrderId" type="xs:string" minOccurs="0"/>
                <xs:element name="lotAttr01" type="xs:string" minOccurs="0"/>
                <xs:element name="orderId" type="xs:string" minOccurs="0"/>
                <xs:element name="ownerId" type="xs:string" minOccurs="0"/>
                <xs:element name="receiptName" type="xs:string" minOccurs="0"/>
                <xs:element name="transactionDate" type="xs:string" minOccurs="0"/>
                <xs:element name="type" type="xs:string" minOccurs="0"/>
                <xs:element name="udf01" type="xs:string" minOccurs="0"/>
                <xs:element name="udf02" type="xs:string" minOccurs="0"/>
                <xs:element name="udf03" type="xs:string" minOccurs="0"/>
                <xs:element name="udf04" type="xs:string" minOccurs="0"/>
                <xs:element name="udf05" type="xs:string" minOccurs="0"/>
                <xs:element name="udf07" type="xs:string" minOccurs="0"/>
                <xs:element name="udf08" type="xs:string" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="detailItems">
        <xs:sequence>
            <xs:element name="detailItem" type="tns:detailItem" nillable="true" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="detailItem">
        <xs:sequence>
            <xs:element name="attribute2" type="xs:string" minOccurs="0"/>
            <xs:element name="attribute3" type="xs:string" minOccurs="0"/>
            <xs:element name="attribute4" type="xs:string" minOccurs="0"/>
            <xs:element name="attribute5" type="xs:string" minOccurs="0"/>
            <xs:element name="externalLineId" type="xs:string" minOccurs="0"/>
            <xs:element name="lotAttr01" type="xs:string" minOccurs="0"/>
            <xs:element name="lotAttr09" type="xs:string" minOccurs="0"/>
            <xs:element name="origianlQuantity" type="xs:long"/>
            <xs:element name="shippedQuantity" type="xs:long"/>
            <xs:element name="skuId" type="xs:string" minOccurs="0"/>
            <xs:element name="udf01" type="xs:string" minOccurs="0"/>
            <xs:element name="udf02" type="xs:string" minOccurs="0"/>
            <xs:element name="udf03" type="xs:string" minOccurs="0"/>
            <xs:element name="udf04" type="xs:string" minOccurs="0"/>
            <xs:element name="udf05" type="xs:string" minOccurs="0"/>
            <xs:element name="udf06" type="xs:string" minOccurs="0"/>
            <xs:element name="udf07" type="xs:string" minOccurs="0"/>
            <xs:element name="udf08" type="xs:string" minOccurs="0"/>
            <xs:element name="udf09" type="xs:string" minOccurs="0"/>
            <xs:element name="udf10" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="orderOutboundResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="dateTime" type="xs:string" minOccurs="0"/>
                <xs:element name="errorMsg" type="xs:string" minOccurs="0"/>
                <xs:element name="returnCode" type="xs:int" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>

