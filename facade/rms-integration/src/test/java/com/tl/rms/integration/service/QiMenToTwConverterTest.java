package com.tl.rms.integration.service;

import com.tl.rms.lib.wsclient.client.tw.request.inbound.PtInboundOrderInfo;
import com.tl.rms.order.domain.vo.QMReturnOrderCreateVo;
import com.tl.rms.order.domain.vo.QMReturnOrderLineVo;
import com.tl.rms.order.domain.vo.QMReturnOrderLinesVo;
import com.tl.rms.order.domain.vo.QMReturnOrderSenderVo;
import com.tl.rms.order.domain.vo.QMReturnOrderVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 奇门到TW转换器测试
 */
public class QiMenToTwConverterTest {

    private QiMenToTwConverter converter;

    @BeforeEach
    void setUp() {
        converter = new QiMenToTwConverter();
    }

    @Test
    void testConvertReturnOrderToTwInbound() {
        // 准备测试数据
        QMReturnOrderCreateVo qmReturnOrder = createTestReturnOrder();

        // 执行转换
        PtInboundOrderInfo twInbound = converter.convertReturnOrderToTwInbound(qmReturnOrder);

        // 验证结果
        assertNotNull(twInbound);
        assertEquals("TLRK202508180001", twInbound.getCustomerOrderID());
        assertEquals("TLRK202508180001", twInbound.getExternalOrderID());
        assertEquals("Return", twInbound.getOrderType());
        assertEquals("太力总部", twInbound.getOwnerID());
        assertEquals("冯士军", twInbound.getDeliveryToContact());
        assertEquals("18423472101-2448#", twInbound.getDeliveryToPhone());
        assertEquals("黑龙江省", twInbound.getDeliveryToProvince());
        assertEquals("哈尔滨市", twInbound.getDeliveryToCity());
        assertEquals("TLJY202508080195", twInbound.getUdf01());
        assertEquals("JKY", twInbound.getUdf08());
        assertEquals("QI_MEN", twInbound.getSource1());
        assertEquals("JKY", twInbound.getSource2());
        assertEquals("DIAN_KE_TAI_LI", twInbound.getSource3());

        // 验证明细
        assertNotNull(twInbound.getPtInboundOrderDetailInfos());
        assertEquals(1, twInbound.getPtInboundOrderDetailInfos().getPtInboundOrderDetailInfo().size());
        
        var detail = twInbound.getPtInboundOrderDetailInfos().getPtInboundOrderDetailInfo().get(0);
        assertEquals("10019205483170000", detail.getSkuID());
        assertEquals("10019205483170000", detail.getSkuDescr());
        assertEquals(1, detail.getQty().intValue());
        assertEquals("1", detail.getExternalLineID());
        assertEquals("************", detail.getUdf01());
    }

    private QMReturnOrderCreateVo createTestReturnOrder() {
        QMReturnOrderCreateVo createVo = new QMReturnOrderCreateVo();

        // 主信息
        QMReturnOrderVo returnOrder = new QMReturnOrderVo();
        returnOrder.setReturnOrderCode("TLRK202508180001");
        returnOrder.setOwnerCode("OWNER001");
        returnOrder.setWarehouseCode("WH001");
        returnOrder.setOrderType("THRK");
        returnOrder.setSourcePlatformCode("JD");
        returnOrder.setPreDeliveryOrderCode("TLJY202508080195");

        // 发件人信息
        QMReturnOrderSenderVo sender = new QMReturnOrderSenderVo();
        sender.setName("冯士军");
        sender.setMobile("18423472101-2448#");
        sender.setProvince("黑龙江省");
        sender.setCity("哈尔滨市");
        sender.setDetailAddress("黑龙江省 哈尔滨市 宾县 宾州镇 大千路147号鸿福世纪城2期");
        returnOrder.setSenderInfo(sender);

        createVo.setReturnOrder(returnOrder);

        // 行项目
        QMReturnOrderLinesVo lines = new QMReturnOrderLinesVo();
        QMReturnOrderLineVo line = new QMReturnOrderLineVo();
        line.setOrderLineNo("1");
        line.setSourceOrderCode("************");
        line.setOwnerCode("OWNER001");
        line.setItemCode("10019205483170000");
        line.setPlanQty(1);
        line.setInventoryType("ZP");

        lines.setOrderLines(Arrays.asList(line));
        createVo.setOrderLines(lines);

        return createVo;
    }
}
